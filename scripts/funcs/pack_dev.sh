#!/bin/bash
# 业务机器执行操作程序

APP_NAME=FishGame
SRV_PATH=~/$APP_NAME/bin/
SRV_LIST=(gatewaysrv loginsrv usersrv assetsrv webapisrv worldsrv tripsrv spotsrv hallsrv gmsrv tasksrv msgsrv statssrv hook2srv ranksrv tasrv activitysrv) # 钓鱼游戏服务
BaserSrv=${BaserSrv:-localhost}
gray_flag=gray
normal_flag=local

function getSrvPID() {
  pid=`ps -ef|grep ${1} | grep -v grep | grep "\--config" | grep "$2"| awk '{print $2}'`
  echo "${pid}"
}

function pid_is_exist() {
  pid=`getSrvPID $1 $2`
  if [ -z "${pid}" ]; then
     return 1
  else
     return 0
  fi
}

function getServer() {
    if [[ $1 == "all" ]]; then
        srv_queue=${SRV_LIST[*]}
        return
    fi
    srv_queue=$@
}

function start() {
    server_tag=$1
    shift
    getServer $@
    for srvName in ${srv_queue[@]}
    do
        if pid_is_exist "${srvName}" "${server_tag}"; then
            if [[ $restart_flag -eq 1 ]]; then
                echo "$srvName 检查到正在运行，等待关闭"
                return 5
            else
                echo "$srvName 检查到正在运行，跳过执行"
                continue
            fi
        else
            echo "$srvName 检查没有运行"
        fi

        srv_path=$SRV_PATH/$srvName
        srv_log_file=launch.log

        cd $srv_path
        chmod +x $srv_path/$srvName
        # 构建本地化启动脚本
        make_config $server_tag
        nohup ./$srvName --config $srv_path/config.${server_tag}.yml >/dev/null 2>${srv_log_file} &

        sleep 2

        if pid_is_exist ${srvName}; then
            echo "$srvName pid:${pid} 启动成功"
        else
            echo "$srvName 启动失败"
        fi
    done
}

function stop() {
    server_tag=$1
    shift
    getServer $@
    for srvName in ${srv_queue[@]}
    do
        pid=`getSrvPID $srvName "${server_tag}"`
        if [ -z "${pid}" ]; then
            echo "$srvName 没有在运行"
            continue
        fi

        # 不使用kill -9 因为不会触发服务优雅退出
        kill  $pid
        echo "$srvName pid:$pid 停止运行"
    done
}

function restart() {
    server_tag=$1
    shift
    getServer $@
    for srvName in ${srv_queue[@]}
    do
        stop $server_tag $srvName
        # 等待确实关闭
        while true; do
            start $server_tag $srvName
            if [ $? -ne 5 ]; then
                break
            fi
            sleep 2
        done
    done
}

function make_config() {
    config_flag=$1
    cp -f config.yml config.${config_flag}.yml
    if  [[ ! ${BASER_SRV} == "" ]] ; then
        sed -i  "s/\b[0-9]\{1,3\}\.[0-9]\{1,3\}\.[0-9]\{1,3\}\.[0-9]\{1,3\}\b/${BASER_SRV}/g" config.${config_flag}.yml
    fi
    if [[ $config_flag == "$gray_flag" ]]; then
    # 替换*_port: xxxx的末尾数字为2
    sed -i "s/\([a-zA-Z_]*_port: [0-9]*\)[0-9]/\12/g" config.${config_flag}.yml
    # 替换rpc_server_tags:为rpc_server_tags: gray
    sed -i "s/rpc_server_tags:.*/rpc_server_tags: gray/g" config.${config_flag}.yml
    # 替换log_dir: ../../logs/为log_dir: ../../logs/gray
    sed -i "s/\(log_dir:.*\)\/\([a-zA-Z]*\)/\1\/gray_\2 /g" config.${config_flag}.yml
    fi
}

# 帮助文档
function usage() {
    echo "start srvName"
    echo "stop srvName"
    echo "restart srvName"
}

# 解压更新包
function unpack() {
    cd ~/$APP_NAME/
    # 清理旧数据
    rm -rf pack
    # 解压
    tar -zxvf pack.tar.gz
}

# 升级服务
function upgrade() {
    # 上传文件夹
    # ~/$APP_NAME/packs/pack_v1.111..tar.gz
    # 获取更新包路径
    pack_path=$1

    # 移除旧数据
    rm -rf ~/$APP_NAME/pack

    tar -zxvf $pack_path -C ~/$APP_NAME

    # 遍历文件夹内所有文件夹
    for srvName in $(ls ~/$APP_NAME/pack)
    do
        upgradeSrv $srvName
        echo "升级 $srvName 完成"
    done
    version_log=~/$APP_NAME/pack_version

    # 从路径中提取文件名
    filename=$(basename "$pack_path")
    echo "$(date +%y%m%d%H%M) $filename" >> $version_log
}


function upgradeSrv() {
    srvName=$1
    cd ~/$APP_NAME/
    rm -rf bin/$srvName
    cp -rf pack/$srvName bin
    # 记录版本
    echo $(date +%y%m%d%H%M) $srvName `cat bin/$srvName/version.txt` >> srv_version
}

case "$1" in
    "start")
    shift
    start $normal_flag $@
    ;;
    "stop")
    shift
    stop $normal_flag $@
    ;;
    "restart")
    shift
    restart $normal_flag $@
    ;;
    "start_gray")
    shift
    start $gray_flag $@
    ;;
    "stop_gray")
    shift
    stop $gray_flag $@
    ;;
    "restart_gray")
    shift
    restart $gray_flag $@
    ;;
    "upgrade")
    shift
    upgrade $@
    ;;
    *)
    usage
    ;;
esac
