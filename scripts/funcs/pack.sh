#!/bin/bash
# 打包程序

# 切换当前目录
cd $(dirname $0)

. ./const.sh
APP_NAME=FishGame

pack_path=${SERVER_PATH}/pack
ENV_LIST=(develop test release)
envHost=$1
shift 
update_list=$@
# 检查标志
check_flag=0

# 检查更新列表是否在服务列表中
contains_element () {
    local e match="$1"
    shift
    for e; do [[ "$e" == "$match" ]] && return 0;
    done
    return 1
}

if ! contains_element "$envHost" "${ENV_LIST[@]}"; then
    echo "环境参数错误:$envHost"
    exit 1
fi

# 检查更新内容
if [[ $update_list == "" ]]; then
    echo "更新列表不能为空"
    exit 1
elif [[ $update_list == "all" ]]; then
    update_list=${SRV_LIST[@]}
# else
#     # 遍历 update_list，查找不在 SRV_LIST 中的元素
#     for srvname in ${update_list[@]}
#     do
#         if ! contains_element "$srvname" "${SRV_LIST[@]}"; then
#             echo "不在列表中的服务: $srvname"
#             check_flag=1
#         fi
#     done

#     if [[ $check_flag -eq 1 ]]; then
#         echo "更新列表中存在不在服务列表中的服务"
#         exit 1
#     fi
fi

echo "环境: $envHost"
echo "更新列表: ${update_list[@]}"

# 清理旧数据
rm -rf $pack_path

mkdir -p $pack_path

# 复制执行辅助脚本
#cp ./funcs/pack_dev.sh $pack_path/dev.sh

## 重新打包bin
for srvname in ${update_list[@]}
    do

    cd "$SERVER_PATH"/"${srvname}"
    pwd


    # 清理数据
    git fetch --all && git reset --hard  

    # 切换到对应环境分支
    git checkout $envHost
    git pull

    go mod tidy
    mkdir -p ${pack_path}/${srvname}

    CGO_ENABLED="0" GOOS=linux GOARCH=${ARCH} GO111MODULE=on go build -o ${pack_path}/${srvname}/$srvname ./cmd/main.go 
    cp ./cmd/config.yml ${pack_path}/${srvname}/
    echo `git rev-parse --short HEAD` > $pack_path/${srvname}/version.txt
done

# 打包bin文件
cd $SERVER_PATH
rm -f pack.tar.gz 
tt=$(date +%y%m%d%H%M)
tar -zcvf ~/$APP_NAME/packs/pack_${envHost}_${tt}.tar.gz pack/
echo "打包完成: ~/$APP_NAME/packs/pack_${envHost}_${tt}.tar.gz"
echo ~/$APP_NAME/packs/pack_${envHost}_${tt}.tar.gz > ~/$APP_NAME/packs/last_pack.txt