syntax = "proto3";
package gate;
option go_package = "./gate;gatePB;";
import "enum.proto";

// GateHeartBeatReq 心跳检测请求
message GateHeartBeatReq {
    optional uint64 time_stamp = 1; // 时间
}

// GateHeartBeatRsp 心跳检测应答
message GateHeartBeatRsp {
    optional uint64 time_stamp = 1; // 时间
}

// GateAnotherLoginNtf 顶号通知
message GateAnotherLoginNtf {
    optional uint32 reserve   = 1; // 保留
    optional string device    = 2; // 顶号设备
    optional int64 time_stamp = 3; // 顶号时间
}

// 踢人通知
message GateKickNtf {
    common.KICK_PLAYER_REASON reason = 1;
}