// gm 模块
syntax = "proto3";

package gmPB;

option go_package = "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/gm;gmPB";

import "enum.proto";
import "common.proto";
import "errors.proto";

message GmOperateReq {
    common.PRODUCT_ID   pid    = 1 [json_name = "pid"];     // product Id
    string              sercet = 2 [json_name = "sercet"];  // 加密密钥
    common.GM_CMD       cmd    = 3 [json_name = "cmd"];     // 操作类型
    string              data   = 4 [json_name = "data"];    // 操作请求 Json打包
    string              reason = 5 [json_name = "reason"];  // 操作原因
    string              user   = 6 [json_name = "user"];    // 操作者
    common.CHANNEL_TYPE cid    = 7 [json_name = "cid"];     // 渠道id
}

message GmOperateRsp {
    common.Result ret  = 1 [json_name = "ret"];
    string        oid  = 2 [json_name = "oid"];   // order_id 操作唯一id
    string        data = 3 [json_name = "data"];  // 操作结果 Json打包
}

// ------------------------------------
//                world
// ------------------------------------
message GmCmdGameTimeReq {
    int64 ts     = 1 [json_name = "ts"];      // 设置时间戳
    int64 offset = 2 [json_name = "offset"];  // 设置偏移值(秒) 优先
}

message GmCmdGameTimeRsp {
}

message GmCmdClearWeatherReq {
    int64 pond_id = 1 [json_name = "pond_id"];
}

message GmCmdClearWeatherRsp {
}

// ------------------------------------
//              hallsrv
// ------------------------------------
// 道具修改请求
message GmCmdOperateItemReq {
    int32                 product_id     = 1[json_name = "ProductId"];      // 产品 ID
    uint64                player_id      = 2[json_name = "PlayerId"];       // 玩家id
    common.ITEM_OPERATION item_operation = 3[json_name = "ItemOperation"];  // 物品操作类型
    int64                 item_id        = 4[json_name = "ItemId"];         // 物品ID
    int64                 item_count     = 5[json_name = "ItemCount"];      // 物品数量
    bool                  is_unpack      = 6[json_name = "IsUnpack"];       // 是否拆包
}

// 道具修改请求
message GmCmdOperateItemRsp {
    common.Reward  reward_info = 1 [json_name = "RewardInfo"]; // 物品信息
}

// 清理背包
message GmCmdClearCatogoryReq {
    int32                product_id = 1 [json_name= "ProductId"];  // 产品 ID
    uint64               player_id  = 2 [json_name = "PlayerId"];  // 玩家id
    common.ITEM_CATEGORY category   = 3 [json_name = "Category"]; // 大分类
}

message GmCmdClearCatogoryRsp {
}

// GmCmdLoadRigRuleReq 加载干架规则
message GmCmdLoadRigRuleReq {
    int32 product_id = 1 [json_name= "ProductId"];  // 产品 ID
}

message GmCmdLoadRigRuleRsp {

}

// 连续登录奖励
message GmCmdContinuousLoginReq {
    uint64 player_id   = 1 [json_name = "PlayerId"];
    int64  last_update = 2 [json_name = "LastUpdate"]; // 强制修改上次领奖时间 （unixtime）
    int32  is_clear    = 3 [json_name = "IsClear"]; // 是否清理数据 （优先）
}

message GmCmdContinuousLoginRsp {
    common.Result ret  = 1 [json_name = "Ret"];
}

// 创建CDK批次请求
message GmCmdCreateCDKReq {
    int32                 channel_id                 = 1; // 渠道ID (必填)
    string                description                = 2; // 批次描述 (可选)
    common.CDK_GENERATION_OPTION generation_option   = 3; // CDK生成方式 (必填)
    int32                 generation_count           = 4; // 随机生成时，需要生成的CDK数量 (当 generation_option 为 "random" 时必填, >0)
    repeated              string         manual_cdks = 5; // 手动输入时，指定的CDK码列表 (当 generation_option 为 "manual" 时必填, 不能为空数组)
    int64                 start_time                 = 6; // 生效开始时间 (Unix时间戳, 秒) (必填)
    int64                 end_time                   = 7; // 生效结束时间 (Unix时间戳, 秒) (必填, 必须大于 start_time)
    int32                 cdk_limit                  = 8; // 单个CDK可被所有用户使用的总次数 (-1表示不限制，默认为0)
    repeated              common.ItemBase rewards    = 9; // CDK奖励列表 (必填, 不能为空数组)
}

// 创建CDK批次响应
message GmCmdCreateCDKRsp {
    common.Result   ret      = 1;  // 返回结果
}

// CDK批次查询请求
message GmCmdQueryCDKBatchesReq {
    common.PaginationReq    pagination = 1; // 分页信息
    common.CDK_BATCH_STATUS status     = 2; // 状态筛选，可选
}

// CDK批次摘要信息
message CDKBatchSummary {
    uint64 id                           = 1; // 批次ID
    common.CHANNEL_TYPE      channel_id = 2; // 渠道ID
    string description                  = 3; // 批次描述
    int32  cdk_count                    = 4; // CDK数量
    int32  cdk_limit                    = 5; // 单个CDK可被所有用户使用的总次数 (-1表示不限制，默认为0)
    common.CDK_BATCH_STATUS  status     = 6; // 状态
    int64  start_time                   = 7; // 生效开始时间 (Unix时间戳)
    int64  end_time                     = 8; // 生效结束时间 (Unix时间戳)
    int64  created_at                   = 9; // 创建时间 (Unix时间戳)
    string rewards                      = 10; // 奖励信息
}

// CDK批次查询响应
message GmCmdQueryCDKBatchesRsp {
    common.Result              ret        = 1; // 返回结果
    int64                      total      = 2; // 总数
    common.PaginationReq       pagination = 3; // 分页信息
    repeated CDKBatchSummary   batches    = 4; // 批次列表
}

// CDK记录查询请求
message GmCmdQueryCDKRecordsReq {
    uint64 batch_id                       = 1; // 批次ID
    common.PaginationReq       pagination = 2; // 分页信息
}

// CDK记录信息
message CDKRecordInfo {
    uint64 id         = 1;  // 记录ID
    string cdk        = 2;  // CDK码
    int32  used_count = 3;  // 使用次数
    int64  created_at = 4;  // 创建时间 (Unix时间戳)
    int64  updated_at = 5;  // 更新时间 (Unix时间戳)
}

// CDK记录查询响应
message GmCmdQueryCDKRecordsRsp {
    common.Result            ret        = 1; // 返回结果
    int64                    total      = 2; // 总数
    common.PaginationReq     pagination = 3; // 分页信息
    repeated CDKRecordInfo   records    = 4; // CDK记录列表
}

// CDK批次作废请求
message GmCmdDisableCDKBatchReq {
    uint64 batch_id    = 1;  // 批次ID
    uint64 operator_id = 2;  // 操作人ID，可选
}

// CDK批次作废响应
message GmCmdDisableCDKBatchRsp {
    common.Result ret     = 1;  // 返回结果
}

// ------------------------------------
//               task
// ------------------------------------

// gm操作类型
enum GM_TASK_OPERATE {
    GM_TASK_UNKNOW         = 0;
    GM_TASK_OPERATE_SET    = 1; // 强制添加或刷新
    GM_TASK_OPERATE_FINISH = 2; // 强制完成
    GM_TASK_OPERATE_DELETE = 3; // 强制删除
}

// gm强制操作任务
message GmCmdOperateTaskReq {
    uint64          player_id  = 1[json_name = "PlayerId"];       // 玩家id
    int64           task_id    = 2 [json_name = "TaskId"];
    GM_TASK_OPERATE operate    = 3 [json_name = "Operate"];
}

message GmCmdOperateTaskRsp {
}


// 设置积分进度
message GmCmdTaskProgressSetReq{
    uint64 player_id = 1[json_name = "PlayerId"];
    int64 category = 2 [json_name = "Category"];
    int64 sub_id = 3 [json_name = "SubId"];
    int64 score = 4 [json_name = "Score"];
}

message GmCmdTaskProgressSetRsp{
    common.Result ret  = 1 [json_name = "Ret"];
}

// 更新子任务进度
message GmCmdTaskSubUpdateReq {
    uint64 player_id   = 1[json_name = "PlayerId"];
    int64  task_id     = 2;
    int64  sub_task_id = 3;
    int64  progress    = 4;
}

message GmCmdTaskSubUpdateRsp {
}

// ------------------------------------
//               spot
// ------------------------------------
// 修改体力 针对在钓场中的玩家
message GmCmdModifyEnergyReq {
    uint64 player_id = 1 [json_name = "PlayerId"];  // 玩家id
    int32 energy_num = 2 [json_name = "EnergyNum"]; // 修改后的体力值数量
}

message GmCmdModifyEnergyRsp {
}


// ------------------------------------
//               hook
// ------------------------------------
// GmCmdOperateHookFishReq 玩家中鱼操作请求
message GmCmdOperateHookFishReq {
    uint64 player_id = 1 [json_name = "PlayerId"];  // 玩家id
    int32 operate    = 2 [json_name = "Operate"];    // 1:修改 2:删除 3:随机一条配置的鱼(不按概率计算) 4:查询
    int64 fish_id   = 3 [json_name = "FishId"];    // 鱼id
}

// GmCmdOperateHookFishRsp 中鱼操作结果
message GmCmdOperateHookFishRsp {
}

// ------------------------------------
//              rank
// ------------------------------------

// 强制刷新排期
message GmCmdFlushRankReq {
    int64 rank_id = 1; // 排行id
}

message GmCmdFlushRankRsp {
    common.Result ret = 1; // 返回结果
    int64 rank_id = 2; // 排行id
}

// 强制发奖
message GmCmdRewardRankReq {
    int64 rank_id = 1; // 排行id
}

message GmCmdRewardRankRsp {
    common.Result ret = 1; // 返回结果
    int64 rank_id = 2; // 排行id
}

// ------------------------------------
//              gate
// ------------------------------------
// 停服踢人请求
message GmDowntimeReq{
    int32 channel_id = 1; // 渠道id
}