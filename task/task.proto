// 登录模块协议
syntax = "proto3";
package taskPB;

option go_package = "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/task;taskPB";

import "common.proto";
import "errors.proto";
import "enum.proto";


// 获取任务列表
message GetTaskListReq {
    common.TASK_CATEGORY category = 1;
}

message GetTaskListRsp {
    common.Result ret                       = 1;
    repeated      common.TaskInfo task_list = 2;
    common.TASK_CATEGORY category = 3;
}

// 下发推送更新
message UpdateTaskNTF {
    common.Result ret                  = 1;
    repeated      common.TaskInfo info = 2;  // 任务信息
}

// 领取奖励
message RewardTaskReq {
    int64 task_id = 1;  // 任务id
    common.TASK_CATEGORY category = 2;
}

message RewardTaskRsp {
    common.Result   ret                         = 1;
    common.TaskInfo info                        = 2;  // 任务信息
    common.Reward   reward                      = 3;
}

// 获取奖励进度请求
message TaskProgressReq {
    common.TASK_CATEGORY category = 1;  // 任务类型
}

// 获取奖励进度响应
message TaskProgressRsp {
    common.Result        ret               = 1;
    common.TASK_CATEGORY category          = 2;  // 任务类型
    repeated             common.TaskProgress list = 3;  // 进度数据
}

// 广播协议
message TaskProgressNTF {
    common.TASK_CATEGORY category          = 2;  // 任务类型
    repeated             common.TaskProgress list = 3;  // 有变化的进度
}

// 领取奖励进度请求
message TaskProgressRewardReq {
    common.TASK_CATEGORY category = 1;  // 任务类型
    int64                sub_id   = 2;  // 子类型
    int64                index    = 3;  // 领取索引
}

// 领取奖励进度返回
message TaskProgressRewardRsp {
    common.Result       ret    = 1;
    common.TaskProgress info   = 2;  // 进度信息
    common.Reward       reward = 3;  // 奖励信息
}

// 获取奖励进度请求
message ProgressReq {
    repeated common.PROGRESS_TYPE category = 1;  // 任务类型
}

// 获取奖励进度响应
message ProgressRsp {
    common.Result         ret                      = 1;
    repeated              common.ProgressInfo list = 2;  // 进度数据
}

// 广播协议
message ProgressNTF {
    repeated              common.ProgressInfo list = 1;  // 有变化的进度
}

// 领取奖励进度请求
message ProgressRewardReq {
    common.PROGRESS_TYPE category = 1;  // 任务类型
    int64                 sub_id   = 2;  // 子进度索引
    int64                 index    = 3;  // 领取索引
}

// 领取奖励进度返回
message ProgressRewardRsp {
    common.Result       ret    = 1;
    common.ProgressInfo info   = 2;  // 进度信息
    common.Reward       reward = 3;  // 奖励信息
}

message GetQuestListReq {
    int64            pond_id    = 1;  // 钓场id
    common.QuestMode quest_mode = 2;  // 任务模式
}


message GetQuestListRsp {
    common.Result ret                             = 1;
    repeated      common.QuestList current_list   = 2;  // 玩家当前任务列表
    repeated      common.QuestList completed_list = 3;  // 玩家已完成任务列表
}

message SubmitQuestReq {
    int64            quest_id                 = 1;  // 提交的任务ID
    int64            pond_id                  = 2;  // 钓场id
    common.QuestMode quest_mode               = 3;  // 任务模式
    repeated         string selected_fish_ids = 4;  // 选中的鱼ID列表（鱼护中每条鱼的唯一标识）
}

message SubmitQuestRsp {
    common.Result       ret        = 1;
    common.QuestList    quest_info = 2;  // 刷新后的任务信息
    common.Reward       reward     = 3;  // 奖励信息
}

message RefreshQuestReq {
    int64            quest_id   = 1;  // 任务ID
    int64            pond_id    = 2;  // 钓场id
    common.QuestMode quest_mode = 3;  // 任务模式
}

message RefreshQuestRsp {
    common.Result    ret        = 1;
    common.QuestList quest_info = 2;  // 刷新后的任务信息
}

// 领取奖励
message RewardMultiTaskReq {
    repeated int64 task_id = 1;  // 任务id
    common.TASK_CATEGORY category = 2;
}

message RewardMultiTaskRsp {
    common.Result   ret                         = 1;
    repeated      common.TaskInfo info = 2;  // 任务信息
    common.Reward   reward                      = 3;
}
