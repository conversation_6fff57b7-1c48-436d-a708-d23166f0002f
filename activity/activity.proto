// 活动模块协议
syntax = "proto3";
package activityPB;

option go_package = "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/activity;activityPB";

import "errors.proto";
import "enum.proto";



/**************************************************************************/
//                              活动接口                                   /
/**************************************************************************/
// 获取活动进度请求
message GetActivityProgressReq {
  common.ACTIVITY_TYPE      activity_id            = 1; // 活动ID
}

// 获取活动进度响应
message GetActivityProgressRsp {
  common.Result             ret                    = 1; // 结果
  repeated ActivityProgress activity_progress_list = 2; // 活动进度列表
}

// 领取活动奖励请求
message ClaimActivityRewardReq {
  common.ACTIVITY_TYPE    activity_id = 1; // 活动ID
  int32                   cycle_id    = 2; // 周期ID
}

// 领取活动奖励响应
message ClaimActivityRewardRsp {
  common.Result           ret         = 1; // 结果
  common.ACTIVITY_TYPE    activity_id = 2; // 活动ID
  repeated int32          stage_id    = 3; // 阶段ID
  int32                   cycle_id    = 4; // 周期ID
}

/**************************************************************************/
//                              活动相关结构                                /
/**************************************************************************/

message ActivityProgress {
    common.ACTIVITY_TYPE activity_id               = 1; // 活动ID
    int32 current_cycle_id                         = 2; // 当前周期ID
    int64 cycle_end_time                           = 3; // 当前周期结束时间戳
    map<int32, int64> metrics                      = 4; // 玩家指标列表
    repeated int32 claimed_records                 = 5; // 已领取阶段列表
}
