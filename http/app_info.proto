syntax = "proto3";
package app_info;
option go_package = "/appInfoPB";

import "common.proto";
import "enum.proto";
import "errors.proto";

// AppInfoReq App信息
message AppInfoReq {
    string                  app_version  = 1;            // 客户端版本
    string                  app_language = 2;            // 语言
    common.PRODUCT_ID       product_id   = 3;            // 产品ID
    common.CHANNEL_TYPE     channel      = 4;            // 渠道
    common.PLATFORM_TYPE    platform     = 5;            // 平台类型
}

message AppInfoRsp {
    common.Result          ret            = 1;
    common.AppUpdateInfo   info_update    = 2;
    common.AppResourceInfo info_resource  = 3;
    common.AppAddressInfo  info_address   = 4;
}

// ServerInfoReq 服务器信息请求
message ServerInfoReq {
    int32 product_id = 1; // 产品ID
    int32 channel    = 2; // 渠道
}

// ServerInfoRes 服务器信息响应
message ServerInfoRes {
    common.Result   ret        = 1; // 返回结果
    ServerTimeInfo   time_info  = 2; // 服务器时间信息
}

// ServerTimeRes 服务器时间响应
message ServerTimeInfo {
  string datetime  = 1; // 格式化的日期时间
  int64  timestamp = 2; // Unix 时间戳（秒）
  string timezone  = 3; // 时区信息
}
