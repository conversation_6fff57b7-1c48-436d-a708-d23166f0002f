// 世界服协议
syntax = "proto3";
package hallPB;

option go_package = "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/hall;hallPB";

import "enum.proto";
import "errors.proto";
import "common.proto";

// 请求玩家所在房间信息(用于断线重连)
message GetRoomInfoReq {

}

// 请求玩家所在房间信息(用于断线重连)响应
message GetRoomInfoRsp {
  common.Result   ret       = 1;      // 结果 是否已经在房间中
  common.RoomInfo room_info = 2;      // 房间信息 room_id为空代表不在房间中
}

// 请求上次游戏信息
message GetLastGameInfoReq {

}

// 请求上次游戏信息响应
message GetLastGameInfoRsp {
  common.Result   ret          = 1;  // 结果
  common.RoomInfo last_game    = 2;  // 上次游戏信息
}

// 进入钓场请求
message EnterFisheryReq {
  int64            pond_id   = 1;     // 钓场id
  common.GAME_TYPE game_type = 2;     // 游戏类型
  common.ROOM_TYPE room_type = 3;     // 房间类型
  int32            spot_id   = 4;     // 钓点id(快速进入时使用)
}

// 进入钓点响应
message EnterFisheryRsp {
  common.Result    ret       = 1;     // 结果
  int64            pond_id   = 2;     // 钓场id
  common.RoomInfo  room_info = 3;     // 房间信息
  int32            spot_id   = 4;     // 钓点id
}

// 查询指定道具信息
message GetItemInfoReq {
  repeated int64 item_list = 1;            // 道具id列表
}

// 查询指定道具信息响应
message GetItemInfoRsp {
  common.Result            ret       = 1;   // 结果
  repeated int64           item_list = 2;   // 道具id列表
  repeated common.ItemInfo item_info = 3;   // 道具信息
}

// 根据道具类型查询道具信息
message GetItemInfoByTypeReq {
  common.ITEM_TYPE item_type = 1;           // 道具类型(-1 查询全部)
}

// 根据道具类型查询道具信息响应
message GetItemInfoByTypeRsp {
  common.Result            ret       = 1;   // 结果
  repeated common.ItemInfo item_info = 2;   // 道具信息
}

// 更新玩家道具推送
message UpdateItemInfoNtf {
  common.Reward       reward_info = 1;  // 道具信息
  common.STORAGE_TYPE storage     = 2;  // 存储位置(扣除来源)
}

// 商品购买信息请求(针对限制商品)
message GetGoodsBuyInfoReq {
  repeated int64 goods_list = 1;    // 商品id列表
}

// 商品购买信息响应(针对限制商品)
message GetGoodsBuyInfoRsp {
  common.Result                ret      = 1;   // 结果
  repeated common.GoodsBuyInfo buy_info = 2;   // 商品购买信息
}

// 商城购买请求
message StoreBuyReq {
  int64                   store_buy_id = 1;  // 商城购买id
  int32                   count        = 2;  // 数量
  common.STORE_SHOW_STYLE style_type   = 3;  // 商品样式(大厅还是房间)
}

// 商城购买响应
message StoreBuyRsp {
  common.Result ret         = 1;  // 结果
  int64 store_buy_id        = 2;  // 商城购买id
  int32 count               = 3;  // 数量
  common.Reward reward_info = 4;  // 奖励信息
}

// 玩家等级变化通知
message ExpLevelChangeNtf {
  int64                              cur_exp           = 1; // 当前经验值
  repeated common.ExpLevelChangeInfo level_change_info = 2; // 等级变化信息
}

// 玩家信息查询
message GetPlayerInfoReq {
  uint64 player_id = 1; // 玩家id
}

// 玩家信息查询响应
message GetPlayerInfoRsp {
  common.PlayerBaseInfo player_info = 1; // 玩家信息
}

// 玩家使用道具
message UseItemReq {
  repeated common.ItemBase item_info = 1; // 使用道具信息(扣除)
  common.ITEM_SOURCE_TYPE   src_type = 2; // 使用来源(扣除原因)
  common.STORAGE_TYPE       storage  = 3; // 存储位置(扣除来源)
}

// 玩家使用道具
message UseItemRsp {
  common.Result ret = 1; // 结果 (具体更新后的数据使用 UpdateItemInfoNtf 推送)
  int64  timestamp  = 2; // 使用时间
}

// FirstEnterHallReq 首次进入大厅请求
message FirstEnterHallReq {
  bool  is_reg     = 1; // 是否注册
}

// FirstEnterHallRsp 首次进入大厅响应
message FirstEnterHallRsp {
  common.PlayerBaseInfo             player_info = 1; // 玩家信息
  repeated common.AnnPopupInfo      popup_info  = 2; // 拍脸图
}

// StoreMultiBuyReq 多商品购买(针对非限制商品)
message StoreMultiBuyReq {
  repeated common.ItemBase goods_list = 1; // 商品列表 (里面的itemId是商城购买id)
  common.STORE_SHOW_STYLE style_type  = 2; // 商品样式(大厅还是房间)
}

// StoreMultiBuyRsp 多商品购买(针对非限制商品)响应
message StoreMultiBuyRsp {
  common.Result ret                  = 1;  // 结果
  repeated common.ItemBase goos_list = 2;  // 商品列表
  common.Reward reward_info          = 3;  // 奖励信息
}

// GetStatListReq 获取用户统计列表请求
message GetStatListReq {
}

// GetStatListRsp 获取用户统计列表响应
message GetStatListRsp {
  common.Result ret = 1;
  repeated common.StatInfo list = 2;
}

// StatInfoUpdateNtf 统计信息通知
message StatInfoUpdateNtf {
  repeated common.StatInfo list = 1;
}

// GetRodRigInfoReq 竿组信息请求
message GetRodRigInfoReq {
  int32 rig_id = 1; // 钓组id(-1 获取全部)
}

// GetRodRigInfoRsp 竿组信息响应
message GetRodRigInfoRsp {
  common.Result ret                   = 1;  // 结果
  repeated common.RodRigInfo rig_list = 2;  // 钓组信息列表
}

// UpdateRodRigInfoReq 更新竿组信息请求
message UpdateRodRigInfoReq {
  common.RodRigInfo rig_info = 1;  // 竿组信息
}

// UpdateRodRigInfoRsp 更新竿组信息响应
message UpdateRodRigInfoRsp {
  common.Result ret          = 1;  // 结果
  common.RodRigInfo rig_info = 2;  // 竿组信息
}

// 删除竿组信息请求
message DeleteRodRigInfoReq {
  int32 rig_id = 1; // 钓组id
}

// 删除竿组信息响应
message DeleteRodRigInfoRsp {
  common.Result ret = 1; // 结果
  int32 rig_id      = 2; // 钓组id
}

// 更新杆组信息推送
message UpdateRodRigNtf {
    repeated common.RodBagInfo rod_info = 1;  // 钓组信息
}

// 修改道具耐久度请求
message ModifyDurabilityReq {
    int32 rig_id = 1; // 钓组id
    map<int32, int64> change = 2; // key = sitId，value=扣减值
}

// 修改道具耐久度返回
message ModifyDurabilityRsp {
    common.Result     ret      = 1;  // 返回结果
    common.RodBagInfo rod_info = 2;  // 钓组信息
}

// 维修杆包道具请求
message MaintainRodItemReq {
    int32 rig_id = 1; // 钓组id
    repeated int32 sitId = 2; // 钓组位置id
}

// 维修杆包道具返回
message MaintainRodItemRsp {
    common.Result     ret      = 1;  // 返回结果
    common.RodBagInfo rod_info = 2;  // 钓组信息
    common.Reward    cost   = 3;
}

// 维修仓库道具请求
message MaintainStorageItemReq {
    common.STORAGE_TYPE store_type  = 1;  // 仓库类型
    repeated string     instance_id = 2;  // 道具id
}

// 维修杆包道具返回
message MaintainStorageItemRsp {
    common.Result     ret      = 1;  // 返回结果
    common.Reward    cost   = 3;
}

// 出售道具请求 (仓库/背包)
message SellItemReq {
    common.STORAGE_TYPE store_type  = 1;
    repeated common.ItemBase list = 2;
}

message SellItemRsp {
    common.Result     ret      = 1;
    common.Reward reward = 2;
}

// ------------------------------------
// 背包系统
// ------------------------------------

// 获取钓组信息
message GetTripRodReq {
  common.STORAGE_TYPE   storage = 1; // 存储类型
}

// 获取钓组信息
message GetTripRodRsp {
  common.Result ret                    = 1;  // 结果
  repeated      common.RodBagInfo list = 2;  // 钓组信息列表
}

// detached
// 导入预设钓组
message LoadTripRodReq {
  int32 rig_id = 2;  // 鱼竿架id
  int32 id     = 3;  // 钓组id
}

// detached
// 导入预设钓组
message LoadTripRodRsp {
  common.Result     ret    = 1;  // 结果
  int32             rig_id = 2;  // 鱼竿架id
  int32             id     = 3;  // 钓组id
  common.RodBagInfo info   = 4;  // 钓组信息
}

// detached
// 保存预设钓组
message SaveTripRodReq {
  int32 rig_id = 2;  // 鱼竿方案id
}

// detached
// 保存预设钓组
message SaveTripRodRsp {
  common.Result     ret    = 1;  // 结果
  int32             rig_id = 2;  // 鱼竿方案id
  int32             id     = 3;  // 杆组id
  common.RodBagInfo info   = 4;  // 钓组信息
}

// 保存新钓组
message SaveNewTripRodReq {
  string            name          = 1;  // 钓组名称
  repeated UpdateTripRodReq list  = 2;  // 修改的钓组信息
  int32             bag_index     = 3;  // 背包位置
}

// 保存新钓组
message SaveNewTripRodRsp {
  common.Result     ret           = 1;  // 结果
  int32             id            = 2;  // 杆组id
  common.RodBagInfo info          = 3;
  int32             bag_index     = 4;  // 背包位置
}

// detached
// 钓组放入背包请求
message PutTripRodReq {
  int32 id        = 1;  // 钓组id
  int32 bag_index = 2;  // 背包位置
}

// detached
// 钓组放入背包返回
message PutTripRodRsp {
  common.Result     ret       = 1;  // 结果
  int32             bag_index = 2;  // 背包位置
  int32             id        = 3;  // 钓组id
  common.RodBagInfo info      = 4;  // 钓组信息
}

// detached
// 卸下钓组
message DelTripRodReq {
  int32 id = 1; // 钓组id
}

// detached
// 卸下钓组
message DelTripRodRsp {
  common.Result ret       = 1;  // 结果
  int32         id        = 2;  // 钓组id
  common.RodBagInfo info  = 3;  // 钓组信息
}

// 拆卸钓组
message SplitTripRodReq {
  int32 id = 1; // 钓组id
}

// 拆卸钓组
message SplitTripRodRsp {
  common.Result ret       = 1;  // 结果
  int32         id        = 2;  // 钓组id
}

// 更新钓组 (上膛/ 改线/ 改饵)
message UpdateTripRodReq {
  int32               id          = 1;  // 钓组id
  int32               sit         = 2;  // 钓组内位置信息
  int64               item_id     = 3;  // 修改道具
  string              instance_id = 4;  // 实例id
  common.STORAGE_TYPE storage     = 5;  // 存储类型
}

// 更新钓组 (上膛/ 改线/ 改饵)
message UpdateTripRodRsp {
  common.Result     ret         = 1;  // 结果
  int32             id          = 2;  // 钓组id
  int32             sit         = 3;  // 钓组内位置信息
  int64             item_id     = 4;  // 修改道具
  string            instance_id = 5;  // 实例id
  common.RodBagInfo info        = 6;
}

// 批量更新钓组
message BatchUpdateTripRodReq {
  int32             id           = 1;  // 钓组id
  repeated UpdateTripRodReq list = 2;  // 修改的钓组信息
  string            name         = 3;  // 钓组名称
}

// 批量更新钓组
message BatchUpdateTripRodRsp {
  common.Result     ret         = 1;  // 结果
  common.RodBagInfo info        = 6;
}

// detached
// 获取背包请求
message GetTripBagReq {
  common.TRIP_BAG_TYPE    type    = 2;  // 背包类型
}

// detached
// 获取背包响应
message GetTripBagRsp {
  common.Result           ret     = 1;  // 结果
  common.TRIP_BAG_TYPE    type    = 2;  // 背包类型
  repeated common.ItemInfo    list    = 3;  // 道具列表
}

// detached
// 修改旅途背包请求
message ModifyTripBagReq {
  common.TRIP_BAG_TYPE    type        = 1;  // 背包类型
  common.TRIP_BAG_OPERATE operate     = 2;  // 操作类型
  int64                   item_id     = 3;  // 道具id
  string                  instance_id = 4;  // 实例id
  int64                   count       = 5;  // 数量
}

// detached
// 修改旅途背包响应
message ModifyTripBagRsp {
  common.Result           ret         = 1;  // 结果
  common.TRIP_BAG_TYPE    type        = 2;  // 背包类型
  common.TRIP_BAG_OPERATE operate     = 3;  // 操作类型
  int64                   item_id     = 4;  // 道具id
}

// detached
// 旅途背包快捷购买
message TripBagQuickBuyReq {
  int64                   item_id = 3;  // 道具id
  int64                   count   = 4;  // 数量
}

// detached
// 旅途背包快捷购买
message TripBagQuickBuyRsp {
  common.Result           ret     = 1;  // 结果
  int64                   item_id = 3;  // 道具id
  int64                   count   = 4;  // 数量
}

// detached
// 旅途背包使用
message TripBagUseReq {
  common.TRIP_BAG_TYPE    type    = 2;  // 背包类型
  int64                   item_id = 3;  // 道具id
  int64                   count   = 4;  // 数量
}

// detached
// 旅途背包使用
message TripBagUseRsp {
  common.Result           ret     = 1;  // 结果
  common.TRIP_BAG_TYPE    type    = 2;  // 背包类型
  int64                   item_id = 3;  // 道具id
  int64                   count   = 4;  // 数量
}

// CheckForbidWordReq 检查屏蔽字请求
message CheckForbidWordReq {
  string word = 1; // 需要检测的单词或内容
}

// CheckForbidWordRsp 检查屏蔽字响应
message CheckForbidWordRsp {
  bool is_forbid     = 1; // 是否含有屏蔽字
  string forbid_word = 2; // 处理后的单词或内容
}

// 卸下旅行背包所有物品请求
message UnloadAllTripBagReq {
}

// 卸下旅行背包所有物品返回
message UnloadAllTripBagRsp {
  common.Result           ret     = 1;  // 结果
}

// detached
// 拉取鱼饵数据
message ItemHeapReq {
}

// detached
// 拉取鱼饵数据
message ItemHeapRsp {
  common.Result           ret     = 1;  // 结果
  map<int64, int32>       item_heaps = 2; // 鱼饵数据 key: 物品id value: 耐久损耗度百分比
}

// detached
// 鱼饵数据更新
message ItemHeapUpdateNotify {
  map<int64, int32>       item_heaps = 1; // 鱼饵数据 key: 物品id value: 耐久损耗都百分比
}

// ------------------------------------
// 其它系统
// ------------------------------------

// RealNameAuthReq 实名认证请求
message RealNameAuthReq {
  string real_name = 1; // 真实姓名
  string id_card_num = 2; // 身份证号
}

// RealNameAuthRsp 实名认证响应
message RealNameAuthRsp {
  common.Result ret = 1; // 结果
}

//  AntiAddictionNtf 防沉迷通知
message AntiAddictionNtf {
  bool is_child        = 1; // 是否未成年人
  int32 exceed_time    = 2; // 超过时间（秒）
}

// TempCashBuyReq 临时现金购买请求(针对版号版本)
message TempCashBuyReq {
  int64 store_buy_id = 1;  // 现金购买id
}

// TempCashBuyRsp 临时现金购买响应(针对版号版本)
message TempCashBuyRsp {
  common.Result ret         = 1;  // 结果
  int64 store_buy_id        = 2;  // 现金购买id
  common.Reward reward_info = 3;  // 奖励信息
}

// UpdateGuideProgressReq 新手引导进度更新请求
message UpdateGuideProgressReq {
  int32 progress = 1; // 进度
}

// UpdateGuideProgressRsp 新手引导进度更新应答(如果有奖励使用通用奖励推送:UpdateItemInfoNtf)
message UpdateGuideProgressRsp {
  common.Result ret = 1; // 结果
  int32 progress    = 2; // 进度
}

// 连续登录查询
message ContinuousLoginReq {
}

message ContinuousLoginRsp {
  common.Result ret       = 1;  // 结果
  int64         update_ts = 2;  // 上一次更新时间
  int32         day       = 3;  // 已领奖日
}

message ContinuousLoginRewardReq {
}

message ContinuousLoginRewardRsp {
  common.Result ret       = 1;  // 结果
  int64         update_ts = 2;  // 更新时间
  int32         day       = 3;  // 已领奖日
  common.Reward reward    = 5;  // 奖励
}

// 红点信息
message RedDotInfo {
  common.USER_MODULE_TYPE   module_type     = 1; // 主模块类型
  int32                     sub_module_type = 2; // 子模块类型，每个模块有不同的常量定义
  bool                      has_red_dot     = 3; // 是否有红点
}

// 清除红点请求
message ClearRedDotRequest {
  common.USER_MODULE_TYPE module_type = 2; // 主模块类型
  int32 sub_module_type               = 3; // 子模块类型
}

// 清除红点响应
message ClearRedDotResponse {
  common.Result ret                   = 1; // 结果
  RedDotInfo red_dot                  = 2; // 当前红点状态
}

// 获取玩家所有红点请求
message GetPlayerAllRedDotsRequest {
}

// 获取玩家所有红点响应
message GetPlayerAllRedDotsResponse {
  common.Result ret                   = 1; // 结果
  repeated RedDotInfo red_dots        = 2; // 红点列表
}

// 获取玩家指定模块红点请求
message GetPlayerModuleRedDotsRequest {
  common.USER_MODULE_TYPE module_type = 1; // 主模块类型
}

// 获取玩家指定模块红点响应
message GetPlayerModuleRedDotsResponse {
  common.Result ret                   = 1; // 结果
  repeated RedDotInfo red_dots        = 2; // 红点列表
}

// 更新玩家红点推送
message RedDotUpdateNotify {
  repeated RedDotInfo red_dots        = 1; // 红点列表
}

// 修改玩家信息请求
message ModifyPlayerInfoReq {
  string nick_name = 1;  // 昵称
  int64  avatar    = 2;  // 头像
  int64  frame     = 3;  // 头像框
}

// 修改玩家信息响应
message ModifyPlayerInfoRsp {
  common.Result ret = 1;  // 结果
}

// CDKey兑换码请求
message CDKeyExchangeReq {
  string cd_key = 1;  // CDKey兑换码
}

// CDKey兑换码响应
message CDKeyExchangeRsp {
  common.Result ret         = 1;  // 结果
  common.Reward reward_info = 2;  // 奖励信息
}

// 获取统计规则请求
message GetStatsRulesReq {
  repeated common.StatsRuleDesc list = 1; // 统计规则描述
}

// 获取统计规则响应
message GetStatsRulesRsp {
  common.Result ret                       = 1;  // 结果
  repeated      common.StatsRuleInfo list = 2;  // 统计规则
}

// 获取玩家道具cd请求
message GetItemCdReq {
}

// 获取玩家道具cd响应
message GetItemCdRsp {
  common.Result ret                    = 1; // 结果
  repeated      common.ItemCdInfo list = 2; // 道具cd列表
}

// 局内商城请求
message PondStoreReq {
  repeated int64 pond_id = 1; // 场次id
}

// 局内商城响应
message PondStoreRsp {
  common.Result ret                   = 1;  // 结果
  repeated      common.PondStore list = 2;  // 场次道具信息
}

// 购买局内商城请求
message PondStoreBuyReq {
  int64 pond_id = 1; // 场次id
  int64 batch = 2; // 批次
  int64 good_id = 3; // 商品id
  int64 count = 4; // 数量
}

// 购买局内商城响应
message PondStoreBuyRsp {
  common.Result    ret     = 1;  // 结果
  common.Reward    reward  = 2;  // 奖励
  int64            pond_id = 3;  // 场次id
  int64            batch   = 4;  // 批次
  common.PondGoods good    = 5;  // 商品信息
}