syntax = "proto3";

package common;

//option go_package = "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/commonPB;commonPB";
option go_package = "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common;commonPB";

import "enum.proto";

/**************************************************************************/
//                              系统模块                                    /
/**************************************************************************/

// 客户端更新
message AppUpdateInfo {
    string version         = 1;               // app当前版本
    bool   isPrompt        = 2;               // 是否提示更新版本
    bool   is_force        = 3;               // 是否强制更新版本
    string download_url    = 4;               // APP下载地址
    string tip_msg_key     = 5;               // 更新描述的KEY
    string min_app_version = 6;               // 最小兼容版本[低于这个强更]
}

// app资源信息
message AppResourceInfo {
    string config_md5   = 1;                // 配置config_md5.json的md5值
    string remote_md5   = 2;                // 远程资源MD5值
    bool   remote_force = 3;                // 远程资源是否强制更新
    bool   patch_force  = 4;                // 补丁是否只更新代码
}

// app地址信息
message AppAddressInfo {
    repeated string srv_uri         = 1;    // 服务器uri
    string          cdn_host        = 2;    // CDN地址
    string          resource_uri    = 3;    // 下载地址的路径，绝对路径  /cdn/fancy/asset_bundles/
    string          config_uri      = 4;    // 配置中心路径 /cdn/fancy/config/
    string          fb_share_uri    = 5;    // fb分享地址
    string          log_upload_uri  = 6;    // log上传地址
}

/**************************************************************************/
//                              玩家信息                                     /
/**************************************************************************/
// 第三方登录
message ThirdLoginInfo {
   string open_id = 1;  // open_id (如google登录, 传入google账号)
   string token   = 2;  // token (如facebook登录, 传入facebook的token)
   string code_id = 3;  // code_id
}

// 账号信息 (针对账号密码登录)
message AccountInfo {
    string        account       = 1; // 账号
    string        password      = 2; // 密码
}

// 玩家设备信息
message DeviceInfo {
    string        device_model  = 1;   // 设备型号，eg: iphone X, huawei mate40
    string        device_brand  = 2;   // 设备品牌，eg: Apple, Huawei，Xiaomi
    string        os            = 3;   // 操作系统 ios14 / android10
    string        os_language   = 4;   // os 语言 玩家设备设置的语言
    LANGUAGE_TYPE app_language  = 5;   // APP内设置语言，APP支持的国家化语言
    string        resolution    = 6;   // 设备分辨率 (暂无使用)
    string        adjust_id     = 7;   // 设备 Adjust ID
    string        idfa          = 8;   // iOS-设备广告ID
    string        time_zone     = 9;   // 时区
    string        device_name   = 10;  // 登录设备名
    string        device_code   = 11;  // 设备唯一标识符
    string        mvno          = 12;  // 运营商 (移动虚拟运营商 Mobile virtual network operator)
    string        android       = 13;  // 安卓Id (暂无使用)
    string        cpu           = 14;  // cpu  (暂无使用)
    string        directx       = 15;  // 显卡 (暂无使用)
    string        ram           = 16;  // 内存 (暂无使用)
    string        video_adapter = 17;  // 显卡 (暂无使用)
    string        client_ip     = 18;  // 客户端IP
}

// 玩家全量信息 (外部包装信息不可外发给其他用户)
message RichUserInfo {
    BriefUserInfo        brief_user_info  = 1;   // 玩家简要信息
    BanAccountInfo       ban_account_info = 2;   // 封号信息
    string               app_version      = 3;   // 版本号
    common.ACC_TYPE      acc_type         = 5;   // 账号类型，区别于登录类型
    int64                register_time    = 7;   // 注册时间
    PLATFORM_TYPE        platform         = 9;   // 平台类型
    common.LANGUAGE_TYPE app_language     = 12;  // 多语言
    bool                 real_name_auth   = 13;  // 实名状态 TODO:delete
    ExtendUserInfo       extend_user_info = 14;  // 玩家拓展信息
}

// 玩家简要信息 (公共通讯信息，如好友，公告等直接使用)
message BriefUserInfo {
    uint64              player_id        = 1;   // 玩家id
    string              name             = 2;   // 名字
    int64               avatar           = 3;   // 游戏内置头像编号
    string              avatar_url       = 4;   // 第三方头像地址
    int64               frame            = 7;   // 游戏内头像框
    int32               lev              = 8;   // 用户等级
    string              country          = 9;   // 国家
    int64               last_login_time  = 10;  // 最后登录时间
    int64               last_logout_time = 11;  // 最后退出游戏时间
    string              last_device_code = 12;  // 上次登录设备码
}

message RegionInfo {
    string Country = 1; // 国家
    string City    = 2; // 城市
}

// 玩家拓展信息
message ExtendUserInfo {
    int32  novice_guide = 1;  // 新手引导进度
}

// 封号信息
message BanAccountInfo {
    uint64              player_id    = 1;   // 玩家id
    BAN_ACC_REASON_TYPE BanReason    = 2;   // 封禁原因
    int64               BanLoginTime = 3;   // 用户封号的结束时间
}

// 钓鱼佬
message Fisher {
    BriefUserInfo   brief_user_info      = 1;   // 玩家简要信息
    uint64          enter_coin           = 2;   // 携带金币
}

/**************************************************************************/
//                              钓鱼场景模块                                 /
/**************************************************************************/
// 房间信息
message RoomInfo {
    string           room_id       = 1; // 房间id
    common.ROOM_TYPE room_type     = 2; // 房间类型
    int64            pond_id       = 3; // 钓场id
    uint32           room_code     = 4; // 房间编号，客户端输入用这个字段
    int32            spot_id       = 5; // 钓点id
}

// 坐标信息
message Position {
    float pos_x = 1; // x坐标
    float pos_y = 2; // y坐标
    float pos_z = 3; // z坐标 水深(单位:米)
}

// 方向
message Direction {
    float dir_x = 1; // x分量
    float dir_y = 2; // y分量
}

// 玩家钓点同步信息
message FisherSpotSync {
    uint64             player_id   = 1; // 玩家id
    bytes              sync_data   = 2; // 同步信息
    uint64             sync_num    = 3; // 同步次数
    common.FISH_STATUS fish_status = 4; // 钓鱼状态
}

// 鱼信息
message FishInfo {
    int64              fish_id      = 1; // 鱼id
    string             name         = 2; // 鱼名称
    int32              length       = 3; // 鱼长度(cm)
    int32              weight       = 4; // 鱼重量(g)
    int32              award_num    = 5; // 奖励数量
    int32              exp          = 6; // 鱼经验
    int64              special      = 7; // 品种
    int32              somatotype   = 8; // 体型
    int32              quality      = 9; // 品质
    int32              genus        = 10; // 属科
    int32              unhook_value = 11; // 鱼的脱钩值
}

// 鱼详细信息
message FishDetailInfo {
    string             instance_id          = 1; // 实例id
    int32              freshness            = 2; // 新鲜度
    int64              hook_time            = 3; // 钓上时间(时间戳)
    float              award_per            = 4; // 奖励百分比
    FishInfo           fish_info            = 5; // 鱼信息
    bool               is_first             = 6; // 是否第一次
    int64              bait_id              = 7; // 钓饵id
    FishDamagedInfo    fish_damaged_info    = 8; // 鱼破损信息
}

// 中鱼同步控制
message FishSyncControl {
    int64                   interval_time = 1;  // 请求间隔时间(毫秒)
    int64                   stop_time     = 2;  // 结束时间(毫秒) 超过该时间就自动结束
}

// 中鱼饵姿态习性参数
message HookBaitTypePose {
    common.FISHING_BAIT_TRICK_TYPE pose_type = 1; // 姿态类型
    int32 score                              = 2; // 分数
}

// 中鱼请求时的习性参数
message HookHabitParam {
    int32  water_temp                                    = 1; // 水温
    repeated common.MAP_WATER_LAYER_TYPE  layer_list     = 2; // 水层列表
    repeated common.UNDER_WATER_STRUCTURE structure_list = 3; // 结构列表
    HookBaitTypePose                      bait_pose_info = 4; // 饵姿态信息
    double                                log_light      = 5; // 日照强度
}

// 时间
message TimeOfDay {
    int32 hour   = 1; // 时
    int32 minute = 2; // 分
}

// 抛竿格子信息
message ThrowGridInfo {
    common.Position pos                = 1; // 坐标
    common.WATER_AREA_TYPE area_type   = 2; // 水域类型
    common.OBSTACLE_TYPE obstacle_type = 3; // 障碍物类型
}

// 钓场玩家信息
message FisheryPlayerInfo {
    BriefUserInfo 	    user_info      = 1; // 玩家信息
    int32               fish_weight    = 2; // 鱼护重鱼重量(g)
    int32               spot_id        = 3; // 钓点id
    int32               exp_level      = 4; // 经验等级
    int32               energy         = 5; // 体力值
}

// 玩家基础信息
message PlayerBaseInfo {
    uint64              player_id = 1; // 玩家id
    int64               exp_num   = 2; // 经验值
    int64               coins     = 3; // 金币
    int64               diamond   = 4; // 钻石
    int32               expLevel  = 5; // 经验等级
    string              name      = 6; // 用户名
    int64               avatar    = 7; // 头像
    int64               frame     = 8; // 头像框
    int64               country   = 9; // 地区
    string              show_id   = 10;// showId
}

// 经验等级变化信息
message ExpLevelChangeInfo {
    int32                    exp_level = 1;    // 经验等级
    repeated common.ItemBase item_list = 2;    // 奖励道具列表
}

// 钓场事件变化投递
message PondEventChangeInfo {
    POND_EVENT_CHANGE_TYPE event_id 	= 1; // 事件id
    int64                  before_num   = 2; // 变化前数量
    int64                  after_num    = 3; // 变化后数量
    int64                  change_num   = 4; // 变化数量
}

// 钓鱼事件信息
message FishingEventInfo {
    common.FISHING_EVENT_TYPE event_type  = 1;  // 事件类型
    uint64                    player_id   = 2;  // 玩家id(如:中鱼者id)
    map<int32, int64>         int_data    = 3;  // 整形事件参数 key:EVENT_INT_KEY value:int data
    map<int32, string>        string_data = 4;  // 字符串事件参数 key:EVENT_STRING_KEY value:string data
}

// 鱼破损信息
message FishDamagedInfo {
    int32                  fish_damaged_value = 1;  // 鱼破损程度价值百分比，服务器校验值位于(0,100]区间内
    common.FISH_DAMAGED_LV fish_damaged_lv    = 2;  // 鱼破损程度等级
}


// 钩饵组合
message HookBait {
    int64 hook_id = 1; // 钩子id
    int64 bait_id = 2; // 饵料id
}

/**************************************************************************/
//                              物品模块                                     /
/**************************************************************************/
// Item
message Item {
    int64                item_id          = 1;  // 物品id required
    common.ITEM_CATEGORY item_category    = 2;  // 大类别 required
    common.ITEM_TYPE     item_type        = 3;  // 物品类型
    int32                item_sub_type    = 4;  // 物品子类型
    int32                item_level       = 6;  // 物品等级
    int64                item_expire_time = 7;  // 有效时间
    string               instanceId       = 8;  // 实例id
    int64                update_time      = 9;  // 更新时间
    map<int32,int64>     extra            = 10; // 拓展信息
}

// ItemInfo
message ItemInfo {
    Item                    item                    = 1;
    int64                   item_count              = 2; // 存量
    int64                   item_delta_count        = 3; // 增量
}

// 玩家物品信息
message PlayerItemInfo {
    uint64                  player_id           = 1; // 玩家id
    repeated ItemInfo       prop_list           = 2; // 物品列表
    repeated ItemInfo       bag_list            = 3; // 背包列表
    repeated ItemInfo       equip_tackle_list   = 4; // tackle 列表
}

// 发奖前Item
message OriginLoot {
    common.Item                 item      = 1; // 物品
    int64                       value     = 2; // 数量
}

message OriginLoots{
    repeated OriginLoot         loots     = 1;
    common.ITEM_SOURCE_TYPE     source    = 3; // 来源
}

// 奖励信息
message Reward {
    string                  claimID                  = 1;  // 领奖唯一ID
    common.ITEM_SOURCE_TYPE source_type              = 2;  // 奖励来源
    int64                   timestamp                = 3;  // 时间戳
    common.REWARD_SHOW_TYPE show_type                = 4;  // 奖励展示方式
    repeated                ItemInfo       item_list = 5;  // 物品列表
}

// 商品购买信息
message GoodsBuyInfo {
    int64  goods_id           = 1; // 商品id
    int32  buy_times          = 2; // 购买次数
    int64  last_buy_time      = 3; // 上次购买时间(时间戳)
}

// 道具基础信息(服务器内部rpc使用 转发到hall服务器添加)
message ItemBase {
    int64                     item_id          = 1;  // 物品id
    int64                     item_count       = 2;  // 数量
    string                    instance_id      = 3;  // 实例id
}

message ItemBaseList {
    repeated ItemBase item_list = 1;
}

// 道具冷却信息
message ItemCdInfo {
    int64 item_id       = 1; // 道具id
    int64 last_use_time = 2; // 上次使用时间
}

/**************************************************************************/
//                              支付模块                                     /
/**************************************************************************/
// 订单信息
message PurchaseOrderInfo {
    common.PRODUCT_ID           ProductID    = 1;        // 产品ID
    int32                       PurchaseID   = 2;       // 支付ID(对应配置文件)
    string                      CommodityID  = 3;        // 第三方商品ID,对应配置的渠道商品ID
    int64                       OrderID      = 4;        // 订单ID
}

/**************************************************************************/
//                              登录模块                                     /
/**************************************************************************/


/**************************************************************************/
//                              世界模块                                     /
/**************************************************************************/

// 天气分段信息
message WeatherPeriod {
    int64                 period            = 1;  // 第n时间段
    int64                 air_temperature   = 2;  // 气温
    int64                 water_temperature = 3;  // 水温
    int64                 wind_speed        = 4;  // 风速
    common.DIRECTION_TYPE wind_direction    = 5;  // 风向
    common.WEATHER_TYPE   sky               = 6;  // 天气
    int64                 lux_in_exposure   = 7;  // 光照强度
    int64                 lux_in_shadow     = 8;  // 阴影光照强度
    int64                 pressure_influence = 9;  // 气压影响
    int64                 cfg_id             = 10; // 配置id
}

// 每日天气信息
message WeatherDay {
    int64    index                 = 1;  // 天数索引 1-90
    repeated WeatherPeriod periods = 2;  // 天气分段信息
}

// 游戏时间
message GameTime {
    int32 Day    = 1;  // 天
    int32 Hour   = 2;  // 小时
    int32 Min    = 3;  // 分钟
    int32 Period = 4;  // 时段
}

/**************************************************************************/
//                              大厅模块                                     /
/**************************************************************************/
message RodRigInfo {
    int32 rig_id     = 1;  // 钓组id(从1开始)
    string name      = 2;  // 名称
    int64  rod_id    = 3;  // 竿id
    int64  reel_id   = 4;  // 轮id
    int64  line_id   = 5;  // 主线id
    int64  leader_id = 6;  // 子线id
    int64  bait_id   = 7;  // 鱼饵id
    int64  float_id  = 8;  // 浮漂id
    int64  hook_id   = 9;  // 鱼钩id
}

// 统计信息
message StatInfo {
    int64 id = 1; // 统计id
    int64 val = 2; // 统计值
}

// 统计规则信息
message StatsRuleInfo {
    int32          typ       = 1;  // 类型
    int64          field     = 2;  // 字段
    int64          target    = 3;  // 目标值
    common.SUM_ADD add_type  = 4;  // 统计规则
    int64          val       = 5;  // 统计值
    int32          cond_key  = 6;  // 条件key
    int64          cond_val  = 7;  // 条件值
    int64          update_time = 8;  // 更新时间
}

message StatsRuleDesc {
	int32          typ       = 1;  // 类型
	int64          field     = 2;  // 字段
	int64          target    = 3;  // 目标值
	common.SUM_ADD add_type  = 4;  // 统计规则
    int32          cond_key  = 6;  // 条件key
    int64          cond_val  = 7;  // 条件值
}

// 旅途钓组信息
message RodBagInfo {
    int32     id          = 1;  // 钓组id
    map<int32, Item> info = 2;  // key定义 见enum #TRIP_ROD_SIT
    string    name        = 3;  // 钓组名称
    int32     bag_index   = 4;  // 背包位置：0在仓库 1,2,3... 对应背包位置
}

/**************************************************************************/
//                              msg模块                                     /
/**************************************************************************/
// 简要邮件信息
message MailBrief {
    uint64            mail_id      = 1;  // 邮件ID
    string            title        = 2;  // 邮件标题
    string            icon_url     = 3;  // 邮件配图  (未实现)
    common.MAIL_TYPE  mail_type    = 4;  // 邮件类型
    int64             create_time  = 5;  // 创建时间
    int64             expires_time = 6;  // 到期时间
    string            content      = 7;  // 邮件内容
    string            sender       = 8;  // 发件人
    map<int32,int64>  extend       = 9;  // 拓展参数 key: MAIL_EXTEND_KEY Value: int64
}

message MailAttachInfo {
    common.MAIL_ATTACH_STATUS       attach_status = 1;  // 附件状态
    common.MAIL_ATTACH_CLAIM_STATUS claim_status  = 2;  // 附件领取状态
    ItemBaseList                    rewards       = 3;  // 附件奖励
}

// 详细邮件信息
message MailDetailInfo {
    common.MailBrief              brief         = 1;
    common.MAIL_STATUS            read_yet      = 2;  // 是否已读
    MailAttachInfo                attach_info   = 3;  // 附件信息
}

// 邮件装配
message MailAssembly {
    int64            template_id   = 1;  // 邮件模板id
    common.MAIL_TYPE mail_type     = 2;  // 邮件类型
    ItemBaseList     rewards       = 3;  // 附件奖励
    int64            expires_time  = 4;  // 过期时间
    int64            create_time   = 5;  // 创建时间
    map<int32,int64> extend        = 6;  // 拓展参数 key: MAIL_EXTEND_KEY Value: int64
    string           icon_url      = 7;  // 邮件配图  (未实现)
}

// 详细广播信息
message MsgBroadcastDetailInfo {
    common.MSG_BROADCAST_TYPE   bc_type     = 1;  // 广播类型
    int64                       template_id = 2;  // 模板id
    repeated  string            params      = 3;  // 参数
    int64                       create_time = 4;  // 创建时间
}

/**************************************************************************/
//                              任务模块                                     /
/**************************************************************************/

message TaskCond {
    int64 cond_id  = 1;
    int64 progress = 2;  // 任务进度
}

message TaskInfo {
    int64              task_id           = 1;  // 任务id  对应配置表
    repeated           TaskCond progress = 2;  // 任务进度
    common.TASK_STATUS status            = 3;  // 任务状态
}

// 进度奖励
message TaskProgress {
    common.TASK_CATEGORY category = 1;  // 任务类型
    int64                sub_id   = 2;  // 子任务id
    repeated int64       rewarded = 3;  // 已领取索引
    int64                score    = 4;  // 现在多少分
}

message ProgressInfo {
    common.PROGRESS_TYPE category             = 1;  // 任务类型
    int64                 sub_id               = 2;  // 子进度索引
    repeated              int64       rewarded = 3;  // 已领取索引
    int64                 score                = 4;  // 现在多少分
}

/**************************************************************************/
//                              排行模块                                     /
/**************************************************************************/

// 个人排行数据
message RankPlayer {
    BriefUserInfo     user         = 1;  // 用户数据
    int32             rank         = 2;  // 名次
    map<int32, int64> info         = 4;  // 通用封装数据 key定义 见enum #RANK_INFO_KEY
}

/**************************************************************************/
//                              公告模块                                     /
/**************************************************************************/

// 内容结构体
message AnnContent {
    string image_url = 1;  // 图像地址
}

// 动作结构体
message AnnAction {
    common.ANN_JUMP_TYPE jump_type = 1;  // 跳转类型
    string               jump_args = 2;  // 跳转参数
}

// 条件结构体
message AnnConditions {
    common.ANN_DISPLAY_TYPE display_strategy = 1;  // 展示策略
    string                  min_version      = 2;  // 最小版本号
}

// 主响应结构体
message AnnPopupInfo {
    int32         id             = 1;  // 公告id
    int32         priority       = 2;  // 优先级
    int32         channel_id     = 3;  // 渠道
    AnnContent    ann_content    = 4;  // 内容
    int32         pop_style      = 5;  // 默认值语法
    AnnAction     ann_action     = 6;  // 动作
    AnnConditions ann_conditions = 7;  // 条件
    int64         start_time     = 8;  // 生效时间
    int64         end_time       = 9;  // 结束时间
}
