syntax = "proto3";

package common;

option go_package = "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common;commonPB";

// MsgID 消息 ID
enum MsgID {
  /**********************************************************************************/
  /*                                     字段分割                                   */
  /**********************************************************************************/
  CMD_BEGIN = 0;

  /**********************************************************************************/
  /*                                     Gate服务                                   */
  /**********************************************************************************/
  CMD_GATE_BEGIN          = 1000;

  GATE_HEART_BEAT_REQ     = 1001; // 心跳检测请求
  GATE_HEART_BEAT_RSP     = 1002; // 心跳检测应答

  GATE_ANOTHER_LOGIN_NTF  = 1004; // 顶号通知

  CMD_GATE_END            = 1099;

  /**********************************************************************************/
  /*                                     Login服务                                   */
  /**********************************************************************************/
  CMD_LOGIN_BEGIN      = 1100;

  CMD_LOGIN_REQ          = 1101; // 登录请求
  CMD_LOGIN_RSP          = 1102; // 登录应答

  CMD_LOGOUT_REQ         = 1103; // 登出请求
  CMD_LOGOUT_RSP         = 1104; // 登出应答

  CMD_DELETE_ACCOUNT_REQ = 1105; // 删除账号请求
  CMD_DELETE_ACCOUNT_RSP = 1106; // 删除账号应答

  CMD_LOGIN_END        = 1199;

  /**********************************************************************************/
  /*                                     hall服务                                   */
  /**********************************************************************************/
  CMD_HALL_BEGIN       = 2000;

  CMD_GET_ROOM_INFO_REQ         = 2001; // 获取房间信息请求
  CMD_GET_ROOM_INFO_RSP         = 2002; // 获取房间信息应答

  CMD_ENTER_FISHERY_REQ         = 2003; // 进入钓场请求
  CMD_ENTER_FISHERY_RSP         = 2004; // 进入钓场应答

  CMD_GET_ITEM_INFO_REQ         = 2005; // 获取物品信息请求
  CMD_GET_ITEM_INFO_RSP         = 2006; // 获取物品信息应答

  CMD_GET_ITEM_INFO_BY_TYPE_REQ = 2007; // 获取类型物品请求
  CMD_GET_ITEM_INFO_BY_TYPE_RSP = 2008; // 获取类型物品应答

  CMD_GET_GOODS_BUY_INFO_REQ    = 2009; // 获取购买物品信息请求
  CMD_GET_GOODS_BUY_INFO_RSP    = 2010; // 获取购买物品信息应答

  CMD_STORE_BUY_REQ             = 2011; // 商店购买请求
  CMD_STORE_BUY_RSP             = 2012; // 商店购买应答

  CMD_UPDATE_ITEM_NTF           = 2014; // 更新物品信息推送

  CMD_GET_LAST_GAME_INFO_REQ    = 2015; // 获取上局游戏信息请求
  CMD_GET_LAST_GAME_INFO_RSP    = 2016; // 获取上局游戏信息应答

  CMD_GET_PLAYER_INFO_REQ       = 2017; // 获取玩家信息请求
  CMD_GET_PLAYER_INFO_RSP       = 2018; // 获取玩家信息应答

  CMD_USE_ITEM_REQ              = 2019; // 使用物品请求
  CMD_USE_ITEM_RSP              = 2020; // 使用物品应答

  CMD_EXP_LEVEL_CHANGE_NTF      = 2022; // 经验等级变化通知

  CMD_FIRST_ENTER_HALL_REQ      = 2023; // 首次进入大厅请求
  CMD_FIRST_ENTER_HALL_RSP      = 2024; // 首次进入大厅应答

  CMD_STORE_MULTI_BUY_REQ       = 2025; // 商店批量购买请求
  CMD_STORE_MULTI_BUY_RSP       = 2026; // 商店批量购买应答

  CMD_GET_ROD_RIG_INFO_REQ      = 2027; // 获取钓组信息请求
  CMD_GET_ROD_RIG_INFO_RSP      = 2028; // 获取钓组信息应答

  CMD_UPDATE_ROD_RIG_INFO_REQ   = 2029; // 更新钓组信息请求
  CMD_UPDATE_ROD_RIG_INFO_RSP   = 2030; // 更新钓组信息应答

  CMD_DELETE_ROD_RIG_INFO_REQ   = 2031; // 删除钓组信息请求
  CMD_DELETE_ROD_RIG_INFO_RSP   = 2032; // 删除钓组信息应答

  CMD_MODIFY_PLAYER_NAME_REQ   = 2033; // 修改玩家名称请求
  CMD_MODIFY_PLAYER_NAME_RSP   = 2034; // 修改玩家名称应答

  CMD_MODIFY_PLAYER_AVATAR_REQ   = 2035; // 修改玩家头像请求
  CMD_MODIFY_PLAYER_AVATAR_RSP   = 2036; // 修改玩家头像应答

  CMD_MODIFY_PLAYER_FRAME_REQ   = 2037; // 修改玩家头像框请求
  CMD_MODIFY_PLAYER_FRAME_RSP   = 2038; // 修改玩家头像框应答

  CMD_PLAYER_INFO_UPDATE_NTF    = 2040; // 玩家信息更新

  CMD_GET_STAT_LIST_REQ         = 2041;  // 获取统计列表请求
  CMD_GET_STAT_LIST_RSP         = 2042;  // 获取统计列表应答

  CMD_STAT_INFO_UPDATE_NTF      = 2044;  // 统计信息更新通知

  CMD_GET_TRIP_ROD_REQ       = 2045;  // 获取旅途钓组信息
  CMD_GET_TRIP_ROD_RSP       = 2046;  // 获取旅途钓组信息

  CMD_LOAD_TRIP_ROD_REQ      = 2047;  // 加载钓组
  CMD_LOAD_TRIP_ROD_RSP      = 2048;  // 加载钓组

  CMD_DEL_TRIP_ROD_REQ       = 2049;  // 卸下钓组
  CMD_DEL_TRIP_ROD_RSP       = 2050;  // 卸下钓组

  CMD_UPDATE_TRIP_ROD_REQ    = 2051;  // 更新钓组
  CMD_UPDATE_TRIP_ROD_RSP    = 2052;  // 更新钓组

  CMD_GET_TRIP_BAG_REQ       = 2053;  // 获取旅途背包信息
  CMD_GET_TRIP_BAG_RSP       = 2054;  // 获取旅途背包信息

  CMD_MODIFY_TRIP_BAG_REQ    = 2055;  // 修改背包信息
  CMD_MODIFY_TRIP_BAG_RSP    = 2056;  // 修改背包信息

  CMD_TRIP_BAG_QUICK_BUY_REQ = 2057;  // 旅途背包快捷购买
  CMD_TRIP_BAG_QUICK_BUY_RSP = 2058;  // 旅途背包快捷购买

  CMD_TRIP_BAG_USE_REQ       = 2059;  // 旅途背包使用使用
  CMD_TRIP_BAG_USE_RSP       = 2060;  // 旅途背包使用使用

  CMD_CHECK_FORBID_WORD_REQ  = 2061;  // 检查屏蔽字请求
  CMD_CHECK_FORBID_WORD_RSP  = 2062;  // 检查屏蔽字应答

  CMD_REAL_NAME_AUTH_REQ     = 2063; // 实名认证请求请求
  CMD_REAL_NAME_AUTH_RSP     = 2064; // 实名认证请求应答

  CMD_MODIFY_DURABILITY_REQ = 2069; // 扣除杆组耐久
  CMD_MODIFY_DURABILITY_RSP = 2070; // 扣除杆组耐久

  CMD_MAINTAIN_ROD_ITEM_REQ = 2071; // 维修杆组耐久
  CMD_MAINTAIN_ROD_ITEM_RSP = 2072; // 维修杆组耐久

  CMD_MAINTAIN_STORAGE_ITEM_REQ = 2073; // 维修仓库道具
  CMD_MAINTAIN_STORAGE_ITEM_RSP = 2074; // 维修仓库道具

  CMD_SAVE_TRIP_ROD_REQ      = 2075;  // 保存鱼杆方案到仓库
  CMD_SAVE_TRIP_ROD_RSP      = 2076;  // 保存鱼杆方案到仓库

  CMD_PUT_TRIP_ROD_REQ       = 2077;  // 从仓库放入鱼杆背包
  CMD_PUT_TRIP_ROD_RSP       = 2078;  // 从仓库放入鱼杆背包

  CMD_UNLOAD_TRIP_BAG_REQ    = 2079;  // 卸下背包所有物品
  CMD_UNLOAD_TRIP_BAG_RSP    = 2080;  // 卸下背包所有物品

  CMD_UPDATE_ROD_RIG_NTF     = 2082;  // 更新杆组信息

  CMD_SELL_ITEM_REQ          = 2083;  // 道具出售
  CMD_SELL_ITEM_RSP          = 2084;  // 道具出售

  CMD_BATCH_UPDATE_TRIP_ROD_REQ    = 2085;  // 批量更新钓组
  CMD_BATCH_UPDATE_TRIP_ROD_RSP    = 2086;  // 批量更新钓组

  CMD_ITEM_HEAP_REQ            = 2087; // 鱼饵数据请求
  CMD_ITEM_HEAP_RSP            = 2088; // 鱼饵数据返回

  CMD_ITEM_HEAP_UPDATE_NTF     = 2090;  // 鱼饵更新通知

  CMD_SPLIT_TRIP_ROD_REQ       = 2091;  // 拆卸钓组
  CMD_SPLIT_TRIP_ROD_RSP       = 2092;  // 拆卸钓组

  CMD_TEMP_CASH_BUY_REQ        = 2093; // 临时现金购买请求
  CMD_TEMP_CASH_BUY_RSP        = 2094; // 临时现金购买应答

  CMD_HALL_UPDATE_GUIDE_PROGRESS_REQ = 2095; // 新手引导进度更新请求
  CMD_HALL_UPDATE_GUIDE_PROGRESS_RSP = 2096; // 新手引导进度更新应答

  CMD_HALL_CONTINUOUS_LOGIN_REQ = 2097; // 登录登录请求
  CMD_HALL_CONTINUOUS_LOGIN_RSP = 2098; // 登录登录应答

  CMD_HALL_CONTINUOUS_LOGIN_REWARD_REQ = 2099; // 登录登录奖励请求
  CMD_HALL_CONTINUOUS_LOGIN_REWARD_RSP = 2100; // 登录登录奖励应答

  CMD_SAVE_NEW_TRIP_ROD_REQ      = 2101;  // 保存新杆组到仓库
  CMD_SAVE_NEW_TRIP_ROD_RSP      = 2102;  // 保存新杆组到仓库

  CMD_CLEAR_RED_DOT_REQ      = 2103;  // 清除红点
  CMD_CLEAR_RED_DOT_RSP      = 2104;  // 清除红点

  CMD_GET_PLAYER_ALL_RED_DOT_REQ      = 2105;  // 获取玩家所有红点
  CMD_GET_PLAYER_ALL_RED_DOT_RSP      = 2106;  // 获取玩家所有红点

  CMD_GET_PLAYER_MODULE_RED_DOT_REQ      = 2107;  // 获取玩家指定模块红点
  CMD_GET_PLAYER_MODULE_RED_DOT_RSP      = 2108;  // 获取玩家指定模块红点

  CMD_PLAYER_RED_DOT_UPDATE_NTF = 2110;  // 玩家红点推送

  CMD_HALL_MODIFY_PLAYER_INFO_REQ      = 2111; // 修改玩家信息请求
  CMD_HALL_MODIFY_PLAYER_INFO_RSP      = 2112; // 修改玩家信息应答

  CMD_CDKEY_EXCHANGE_REQ      = 2113; // CDKEY兑换请求
  CMD_CDKEY_EXCHANGE_RSP      = 2114; // CDKEY兑换应答

  CMD_HALL_GET_STATS_RULES_REQ = 2115;  // 获取统计规则请求
  CMD_HALL_GET_STATS_RULES_RSP = 2116;  // 获取统计规则应答  

  CMD_HALL_END         = 2999;


  /**********************************************************************************/
  /*                                     spot服务                                   */
  /**********************************************************************************/
  CMD_SPOT_BEGIN            = 3000;

  CMD_GET_SPOT_SCENE_REQ       = 3001; // 钓点场景请求
  CMD_GET_SPOT_SCENE_RSP       = 3002; // 钓点场景应答

  CMD_ENTER_ROOM_BS_NTF        = 3004; // 进入房间广播

  CMD_SYNC_SPOT_INFO_REQ       = 3005; // 同步钓点信息请求
  CMD_SYNC_SPOT_INFO_BS_NTF    = 3006; // 同步钓点信息广播

  CMD_THROW_ROD_REQ            = 3007; // 抛竿请求
  CMD_THROW_ROD_RSP            = 3008; // 抛竿应答

  CMD_FISH_HOOK_REQ            = 3009; // 中鱼请求
  CMD_FISH_HOOK_RSP            = 3010; // 中鱼应答

  CMD_CATCH_ROD_REQ            = 3011; // 收竿请求
  CMD_CATCH_ROD_RSP            = 3012; // 收竿应答

  CMD_EXIT_ROOM_REQ            = 3013; // 退出房间请求
  CMD_EXIT_ROOM_RSP            = 3014; // 退出房间应答

  CMD_EXIT_ROOM_BS_NTF         = 3016; // 退出房间广播

  CMD_FISH_ENTRY_OPT_REQ       = 3017; // 鱼入护操作请求
  CMD_FISH_ENTRY_OPT_RSP       = 3018; // 鱼入护操作应答

  CMD_PLAYER_FISH_ENTRY_BS_NTF = 3020; // 鱼入护广播

  CMD_FISH_KEEPNET_OPT_REQ     = 3021; // 鱼护操作请求
  CMD_FISH_KEEPNET_OPT_RSP     = 3022; // 鱼护操作应答

  CMD_KEEPNET_FISH_INFO_REQ    = 3023; // 鱼护中鱼信息请求
  CMD_KEEPNET_FISH_INFO_RSP    = 3024; // 鱼护中鱼信息应答

  CMD_GET_ROOM_ALL_PLAYER_INFO_REQ = 3025; // 获取房间玩家信息请求
  CMD_GET_ROOM_ALL_PLAYER_INFO_RSP = 3026; // 获取房间玩家信息应答

  CMD_CHOOSE_SPOT_REQ              = 3027; // 选择钓点请求
  CMD_CHOOSE_SPOT_RSP              = 3028; // 选择钓点应答

  CMD_EXIT_ROOM_SETTLE_NTF         = 3029; // 退出结算通知
  CMD_ENERGY_CHANGE_NTF            = 3030; // 体力变化通知

  CMD_FISH_BATTLE_FISH_REQ         = 3031; // 搏鱼请求
  CMD_FISH_BATTLE_FISH_RSP         = 3032; // 搏鱼应答

  CMD_SWITCH_ROD_RIG_REQ           = 3033; // 切换鱼竿请求
  CMD_SWITCH_ROD_RIG_RSP           = 3034; // 切换鱼竿响应

  CMD_PLAYER_ENERGY_COST_REQ       = 3035; // 用户体力消耗
  CMD_PLAYER_ENERGY_COST_RSP       = 3036; // 用户体力消耗

  CMD_SPOT_FISHING_EVENT_REQ       = 3037;  // 中鱼事件请求
  CMD_SPOT_FISHING_EVENT_RSP       = 3038;  // 中鱼事件应答
  CMD_SPOT_FISHING_EVENT_BS_NTF    = 3039;  // 中鱼事件广播

  CMD_SPOT_HOOK_START_REQ          = 3040;  // 开始中鱼
  CMD_SPOT_HOOK_START_RSP          = 3041;  // 开始中鱼

  CMD_SPOT_KILL_LINE_REQ           = 3042;  // 切线请求
  CMD_SPOT_KILL_LINE_RSP           = 3043;  // 切线应答

  CMD_SPOT_END              = 3099;

  /**********************************************************************************/
  /*                                     world服务                                   */
  /**********************************************************************************/
  CMD_WORLD_BEGIN          = 3200;

  CMD_WORLD_GET_WEATHER_REQ            = 3201; // 获取天气信息请求
  CMD_WORLD_GET_WEATHER_RSP            = 3202; // 获取天气信息应答

  CMD_WORLD_GET_GAMETIME_REQ            = 3203; // 获取游戏时间请求
  CMD_WORLD_GET_GAMETIME_RSP            = 3204; // 获取游戏时间应答

  CMD_WORLD_GET_WORLD_TIME_REQ            = 3205; // 获取服务器时间请求
  CMD_WORLD_GET_WORLD_TIME_RSP            = 3206; // 获取服务器时间应答

  CMD_WORLD_UPDATE_SERVER_TIME_NTF        = 3207; // 同步更新服务器时间

  CMD_WORLD_END          = 3299;
  /**********************************************************************************/
  /*                                     GM服务                                      */
  /**********************************************************************************/
  CMD_GM_BEGIN          = 3300;

  CMD_GM_OPERATE_REQ            = 3301;
  CMD_GM_OPERATE_RSP            = 3302;

  CMD_GM_END          = 3399;

  /**********************************************************************************/
  /*                                     支付服务                                    */
  /**********************************************************************************/
  CMD_PAY_BEGIN          = 3400;

  CMD_PAY_CREATE_PURCHASE_ORDER_REQ       = 3401; // 创建订单
  CMD_PAY_CREATE_PURCHASE_ORDER_RSP       = 3402;

  CMD_PAY_DELIVER_PURCHASE_ORDER_REQ      = 3403; // 订单发货
  CMD_PAY_DELIVER_PURCHASE_ORDER_RSP      = 3404;

  CMD_PAY_CLOSE_PURCHASE_ORDER_REQ        = 3405; // 关闭订单
  CMD_PAY_CLOSE_PURCHASE_ORDER_RSP        = 3406;

  CMD_PAY_END            = 3499;


  /**********************************************************************************/
  /*                                     任务服务                                    */
  /**********************************************************************************/
 CMD_TASK_BEGIN             = 3500;

  CMD_TASK_GET_LIST_REQ = 3501;  // 全量更新
  CMD_TASK_GET_LIST_RSP = 3502;

  CMD_TASK_UPDATE_NTF   = 3504; // NTF

  CMD_TASK_REWARD_REQ   = 3505;  // 领取奖励
  CMD_TASK_REWARD_RSP   = 3506;

  // CMD_TASK_POND_PROGRESS_REQ   = 3507;  // 探索任务进度  (Deprecated)
  // CMD_TASK_POND_PROGRESS_RSP   = 3508;

  // CMD_TASK_REWARD_TASK_POND_REQ   = 3509;  // 领取探索任务进度 (Deprecated)
  // CMD_TASK_REWARD_TASK_POND_RSP   = 3510;

  // CMD_TASK_POND_UPDATE_NTF   = 3512; // 探索奖励进度更新 (Deprecated)

  CMD_TASK_PROGRESS_REQ   = 3513; // 任务进度请求
  CMD_TASK_PROGRESS_RSP   = 3514; // 任务进度返回

  CMD_TASK_PROGRESS_NTF = 3515; // 任务进度更新

  CMD_TASK_REWARD_PROGRESS_REQ   = 3517; // 任务进度奖励请求
  CMD_TASK_REWARD_PROGRESS_RSP   = 3518; // 任务进度奖励返回

  CMD_PROGRESS_REQ   = 3519; // 进度请求
  CMD_PROGRESS_RSP   = 3520; // 进度返回

  CMD_PROGRESS_NTF = 3521; // 进度更新

  CMD_PROGRESS_REWARD_REQ   = 3523; // 进度奖励请求
  CMD_PROGRESS_REWARD_RSP   = 3524; // 进度奖励返回


  CMD_TASK_END               = 3599;

  /**********************************************************************************/
  /*                                     MSG服务                                     */
  /**********************************************************************************/
  CMD_MSG_BEGIN                 = 3600;

  CMD_MSG_GET_MAIL_LIST_REQ         = 3601;  // 获取邮件列表
  CMD_MSG_GET_MAIL_LIST_RSP         = 3602;

  CMD_MSG_READ_MAIL_REQ             = 3603;  // 读邮件
  CMD_MSG_READ_MAIL_RSP             = 3604;

  CMD_MSG_CLAIM_REWARD_ATTACH_REQ   = 3605;  // 领取附件
  CMD_MSG_CLAIM_REWARD_ATTACH_RSP   = 3606;

  CMD_MSG_NEW_MAIL_NTF              = 3608;  // 邮件通知

  CMD_MSG_BROADCAST                 = 3610;  // 广播

  CMD_MSG_NEW_SYSTEM_MAIL_NTF       = 3612;  // 系统邮件通知

  CMD_MSG_END                       = 3699;

   /**********************************************************************************/
  /*                                     SYNC服务                                     */
  /**********************************************************************************/
  CMD_SYNC_BEGIN             = 3700;

  //CMD_SYNC_SPOT_INFO_REQ     = 3701; // 同步钓点信息请求
  //CMD_SYNC_SPOT_INFO_BS_NTF  = 3702; // 同步钓点信息广播


  CMD_SYNC_END               = 3799;
   /**********************************************************************************/
  /*                                 排行榜服务                                     */
  /**********************************************************************************/
  CMD_RANK_BEGIN             = 3800;

  CMD_RANK_GET_RANK_LIST_REQ = 3801; // 获取排行榜信息
  CMD_RANK_GET_RANK_LIST_RSP = 3802; // 获取排行榜信息

  CMD_RANK_END             = 3899;
   /**********************************************************************************/
  /*                                 埋点服务                                     */
  /**********************************************************************************/
  CMD_TA_BEGIN           = 3900;

  CMD_TA_DATA_REPORT_REQ = 3901;  // 数据上报
  CMD_TA_DATA_REPORT_RSP = 3902;  // 数据上报
  
  CMD_TA_END             = 3999;
   /**********************************************************************************/
  /*                                 活动服务                                     */
  /**********************************************************************************/
  CMD_ACTIVITY_BEGIN                    = 4000;

  CMD_GET_ACTIVITY_PROGRESS_REQ         = 4001;  // 获取活动进度请求
  CMD_GET_ACTIVITY_PROGRESS_RSP         = 4002;  // 获取活动进度响应

  CMD_CLAIM_ACTIVITY_REWARD_REQ         = 4003;  // 领取活动奖励请求
  CMD_CLAIM_ACTIVITY_REWARD_RSP         = 4004;  // 领取活动奖励响应

  CMD_ACTIVITY_END                      = 4099;
   /**********************************************************************************/
  /*                                 xx服务                                     */
  /**********************************************************************************/


  CMD_END   = 9999;
}
