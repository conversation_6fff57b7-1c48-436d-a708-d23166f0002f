// 枚举
syntax = "proto3";

package common;

option go_package = "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common;commonPB";

/**************************************************************************/
//                              系统常量                                     /
/**************************************************************************/
// 平台类型
enum PLATFORM_TYPE {
    PT_INIT      = 0;
    PT_UNITY     = 1;    // unity编辑器
    PT_IOS       = 2;    // iOS平台
    PT_ANDROID   = 3;    // Android平台
    PT_WINDOWS   = 4;    // Windows平台
    PT_MINI      = 5;    // 小游戏平台
    PT_H5        = 6;    // H5 Web平台
    PT_WEAR      = 7;    // 智能穿戴平台
    PT_CONSOLE   = 8;    // 主机游戏平台
}

// 产品ID
enum PRODUCT_ID {
    PID_INIT    = 0;
    PID_FISHER  = 1; // 钓鱼产品
}

// 渠道类型
enum CHANNEL_TYPE {
    CT_INIT       = 0;
    CT_MASTER     = 1001;    // 研发
    CT_GOOGLE     = 1002;    // Google渠道包
    CT_APPSTORE   = 1003;    // 苹果包

    CT_PLAN       = 2001;    // 内部策划渠道
}

// 环境类型
enum ENV_TYPE {
    ET_INIT         = 0;
    ET_DEV          = 1;    // 开发
    ET_TEST         = 2;    // 测试
    ET_PRE          = 3;    // 预发布
    ET_PROD         = 4;    // 生产环境
    ET_AUDIT        = 5;    // 审核环境
    ET_OPTIMIZE     = 6;    // 单用途，优化环境
}

// 网络类型
enum NETWORK_TYPE {
    NT_UNKNOWN      = 0; // 未知网络
    NT_NONE         = 1; // 未能获取到
    NT_MOBILE       = 2; // 移动网络
    NT_LOCAL        = 3; // 局域网
    NT_OTHER        = 4; // 其他
}

// 语言码映射枚举类型 （支持适配的国际化版本）
enum LANGUAGE_TYPE {
    LT_INIT  = 0;  // 缺省
    LT_EN_US = 1;  // 英语
    LT_ZH_CN = 2;  // 简体中文
    LT_ZH_TW = 3;  // 繁体中文
    LT_DE_DE = 4;  // 德语
}

// 灰度状态
enum GRAY_STATUS {
    GS_None   = 0; // none
    GS_Normal = 1; // 正常
    GS_Gray   = 2; // 灰度
}

// 灰度策略
enum GRAY_STRATEGY {
    GS_NONE        = 0;  // 无
    GS_LOCATION    = 1;  // 地理位置(ip)
    GS_TAIL_NUMBER = 2;  // 尾号
    GS_TAG         = 3;  // 标签
    GS_VERSION     = 4;  // 版本
    GS_WHITE_LIST  = 5;  // 白名单
}

// 地理位置类型
enum LOCATION_TYPE {
    LT_NONE   = 0;  // 无
    LT_FORBID = 1;  // 屏蔽
    LT_GRAY   = 2;  // 灰度
}
// 功能隐藏类型
enum FEATURE_HIDE_TYPE {
    FHT_NONE        = 0; // 不隐藏
    FHT_HIDE        = 1; // 隐藏
    FHT_NOT_OPEN    = 2; // 暂未开放
}

// 白名单功能类型
enum WHITE_FUNC_TYPE {
    WFT_NONE     = 0;  // 无
    WFT_LOGIN    = 1;  // 登录
    WFT_RECHARGE = 2;  // 充值
}

/**************************************************************************/
//                              登录相关                                    /
/**************************************************************************/
// 登录方式
enum LOGIN_TYPE {
    LT_Unknown      = 0; // 未知
    LT_VISITOR      = 1; // 游客登录
    LT_TOKEN        = 2; // token 登录
    LT_FACEBOOK     = 3; // FB登录
    LT_APPLE        = 4; // 苹果账号登录
    LT_PASSWORD     = 5; // 账号密码登录
    LT_GOOGLE       = 6; // Google登录
}

enum ACC_STATUS {
    AS_NORMAL       = 0; // 正常
    AS_BAN          = 1; // 冻结，封号
    AS_LOGOFF       = 2; // 注销
    AS_FORBIDDEN    = 3; // 禁止游戏
}

// 账户类型
enum ACC_TYPE {
    AT_INIT         = 0;
    AT_VISITOR      = 1; // 游客
    AT_PASSWORD     = 2; // 账号密码
    AT_TOKEN        = 3; // token
    AT_ROBOT        = 4; // 机器人

    // 第三方账号登录类型预留区间 （10, 100]
    AT_FACEBOOK     = 11; // facebook账号
    AT_APPLE        = 12; // 苹果账号
    AT_GOOGLE       = 13; // Google
    AT_THIRD_END    = 100; // 第三方账号登录类型预留区间上限


    // AT_BAN     = 101; // 封禁账户
}

// 封号原因类型
enum BAN_ACC_REASON_TYPE {
    BAR_TYPE_INIT        = 0;
    BAR_TYPE_REFUND_FREQ = 1; // 频繁退款
    BAR_TYPE_GM          = 2; // GM操作
}

// 登录方式
enum LOGIN_MODEL {
    LIM_SUCCESS = 0; // 正常登录
    LIM_ANOTHER = 1; // 顶号登录
}

// 下线方式
enum LOGOUT_MODEL {
    LOM_SUCCESS    = 0;  // 主动下线
    LOM_DISCONNECT = 1;  // 断线
}

// 踢人原因
enum KICK_PLAYER_REASON {
    KPR_UNKNOWN  = 0; // 未知原因
    KPR_DOWNTIME = 1; // 停服维护
}

/**************************************************************************/
//                              玩家相关                                     /
/**************************************************************************/
enum USER_NOVICE_DONE_STATUS {
    UND_STATUS_INIT    = 0; // 未完成新手
    UND_STATUS_DONE    = 1; // 完成新手
}

// 用户初始信息
enum USER_INIT_INFO {
    UIF_UKNOWN          = 0;
    UIF_FRAME           = 4010000; // 初始头像框
    UIF_AVATAR          = 4020000; // 初始头像
}

// 年龄段定义
enum USER_AGE {
    UA_UNKNOWN  = 0;    // 未知
    UA_CHILD    = 8;    // 幼年 （0,8)
    UA_JUVENILE = 16;   // 少年  [8,16)
    UA_YOUNG    = 18;   // 青少年 [16,18)
    UA_ADULT    = 200;  // 成年人  >= 18
}

// 角色等级解锁功能（红点也使用）
enum USER_MODULE_TYPE {
    UMT_UNKNOWN        = 0;  // 未知
    UMT_FISH_CARD      = 1;  // 鱼册
    UMT_RANK           = 2;  // 排行榜
    UMT_WAREHOUSE      = 3;  // 仓库
    UMT_ACTIVITY       = 4;  // 活动
    UMT_CHAT           = 5;  // 聊天
    UMT_NOVICE         = 6;  // 新手引导
    UMT_MAIL           = 7;  // 邮件
    UMT_ACHIEVEMENT    = 8;  // 成就
    UMT_FB_LOGIN       = 9;  // FB登录
    UMT_GP_LOGIN       = 10; // GP 登录
    UMT_TASK           = 11; // 任务
    UMT_CHALLENGE_TASK = 12; // 挑战任务
    UMT_HALUERSWAY     = 13; // 爆护之路
}


// 邮件子模块
enum RED_DOT_MAIL_SUB_TYPE {
    RDMST_DEFAULT = 0; // 没有子模块就是默认
    RDMST_NOTIFY  = 1; // 通知
    RDMST_MAIL    = 2; // 邮件
}

// 仓库子模块
enum RED_DOT_WAREHOUSE_SUB_TYPE {
    RDWST_DEFAULT = 0; // 没有子模块就是默认
}

// 成就子模块
enum RED_DOT_ACHIEVEMENT_SUB_TYPE {
    RDAST_DEFAULT = 0; // 没有子模块就是默认
}


/**************************************************************************/
//                              游戏相关                                     /
/**************************************************************************/
// 游戏类型
enum GAME_TYPE {
    GT_UNKNOWN           = 0;
    GT_RECREATION        = 1;        // 娱乐玩法
    GT_PRIVATE           = 2;        // 约局玩法
}

// 房间类型
enum ROOM_TYPE {
    RT_UNKNOWN           = 0;
    RT_CASUAL            = 1;        // 休闲场
    RT_PRIVATE           = 2;        // 私人约鱼场
    RT_EXCLUSIVE         = 3;        // 专属独家场
}

// 竿操作类型
enum ROD_ACTION {
    RA_UNKNOWN          = 0;
    RA_PUT              = 1;        // 放线
    RA_TAKE             = 2;        // 收线
}

// 竿操作状态
enum ROD_STATUS {
    RS_UNKNOWN          = 0;
    RS_BEGIN            = 1;        // 开始
    RS_END              = 2;        // 结束
}

// 上鱼结果
enum FISH_RESULT {
    FR_UNKNOWN         = 0; // 未知
    FR_TAKED           = 1; // 钓到
    FR_NOTHING         = 2; // 空钩
    FR_FISH_OUT        = 3; // 鱼跑
    FR_HOOKED          = 4; // 咬钩
    FR_FORCE_KEEP      = 5; //强制保留，不允许脱钩
}

// 钓鱼状态
enum FISH_STATUS {
    FS_UNKNOWN         = 0;
    FS_FISHING         = 1; // 钓鱼中
    FS_FISH_UP         = 2; // 上鱼
    FS_FISH_IN         = 3; // 中鱼
    FS_THROW           = 4; // 抛竿
}

// 天气类型
enum WEATHER_TYPE {
    WT_UNKNOWN       = 0; // 未知
    WT_SUNNY         = 1; // 晴天
    WT_CLOUDY        = 2; // 多云
    WT_OVERCAST      = 3; // 阴天
    WT_FINE_RAIN     = 4; // 小雨
    WT_MODERATE_RAIN = 5; // 中雨
    WT_HEAVY_RAIN    = 6; // 大雨
    WT_STORMY        = 7; // 暴雨
    WT_THUNDERSTORM  = 8; // 雷阵雨
}

// 水域类型
enum WATER_AREA_TYPE {
    WAT_UNKNOWN      = 0; // 未知
    WAT_SLOW_CURRENT = 1; // 缓流
}

// 障碍物类型
enum OBSTACLE_TYPE {
    OT_NONE       = 0; // 无障碍
    OT_WEED       = 1; // 杂草
    OT_WATER_WEED = 2; // 水草
    OT_PEBBLE     = 3; // 鹅卵石
    OT_FLOATING   = 4; // 浮萍
    OT_WOOD       = 5; // 沉木
    OT_ROCK       = 6; // 石块
}

enum DIRECTION_TYPE {
    D_UNKNOWN       = 0;
    D_NORTH         = 1; // 北
    D_NORTH_EAST    = 2; // 东北
    D_EAST          = 3; // 东
    D_SOUTH_EAST    = 4; // 南东
    D_SOUTH         = 5; // 南
    D_SOUTH_WEST    = 6; // 南西
    D_WEST          = 7; // 西
    D_NORTH_WEST    = 8; // 北西
}

// 鱼入护操作类型
enum FISH_ENTRY_OPT_TYPE {
    FEOT_UNKNOWN     = 0; // 未知
    FEOT_KEEP        = 1; // 放入
    FEOT_RELEASE     = 2; // 放生
}

// 鱼护操作类型
enum FISH_KEEPNET_OPT_TYPE {
    FKOT_UNKNOWN     = 0; // 未知
    FKOT_DISCARD     = 1; // 丢弃
    FKOT_FREEZE      = 2; // 冷冻
}

// 鱼属科类型
enum FISH_SPECIES_TYPE {
    FST_UNKNOWN        = 0;  // 未知
    FST_BASS           = 1;  // 鲈鱼
    FST_CYPRINIDAE     = 2;  // 鲤科
    FST_CENTRARCHIDAE  = 3;  // 太阳鱼科
    FST_CATOSTOMIDAE   = 4;  // 胭脂鱼科
    FST_AMIIDAE        = 5;  // 弓鳍鱼科
    FST_PERCIDAE       = 6;  // 鲈科
    FST_ICTALURIDAE    = 7;  // 真鲇科
    FST_ESOCIDAE       = 8;  // 狗鱼鱼科
    FST_CLUPEIDAE      = 9;  // 鲱科
    FST_MORONIDAE      = 10; // 狼鲈科
    FST_LEUCISCIDAE    = 11; // 雅罗鱼科
    FST_SALMONIDAE     = 12; // 鲑科
    FST_TINCIDAE       = 13; // 丁桂鱼科
    FST_SCIAENIDAE     = 14; // 石首鱼科
    FST_ANGUILLIDAE    = 15; // 鳗鲡科
}

// 钓鱼事件类型
enum FISHING_EVENT_TYPE {
    FET_UNKNOWN         = 0;  // 未知
    FET_HOOK_FISH       = 1;  // 中鱼事件
    FET_RELEASE_KEPPENT = 2;  // 丢弃事件
    FET_CONGRATULATION  = 3;  // 祝贺事件
}

// 中鱼计算类型
enum HOOK_FISH_CALC_TYPE {
    HFCT_UNKNOWN        = 0;  // 未知
    HFCT_START          = 1;  // 开始计算
    HFCT_RESET          = 2;  // 重置计算
}

/**************************************************************************/
//                              物品相关                                     /
/**************************************************************************/
// 类别
enum ITEM_CATEGORY {
    IC_UNKNOWN   = 0;
    IC_CURRENCY  = 1;   // 货币
    IC_PROP      = 2;   // 道具
    IC_TACKLE    = 3;   // 渔具
    IC_WEARABLE  = 4;   // 时装
    IC_EQUIP     = 5;   // 装备，武器，工具类
    IC_TICKET    = 6;   // 票券
    IC_FRAGMENTS = 7;   // 合成系统碎片
    IC_REWARD    = 8;   // 奖励物品
    IC_GOODS     = 9;   // 商品
    IC_CATCH     = 10;  // 捕获物
}

// 道具类型
enum ITEM_TYPE {
    IT_UNKNOWN              = 0;
    IT_CURRENCY_COIN        = 101; // 金币
    IT_CURRENCY_DIAMOND     = 102; // 钻石
    IT_CURRENCY_ENERGY      = 103; // 体力
    IT_CURRENCY_EXP         = 104; // 经验
    IT_PROP_PROBE           = 201; // 探测器
    IT_PROP_FOOD            = 202; // 食物
    IT_TACKLE_RODS          = 301; // 钓竿
    IT_TACKLE_REEl          = 302; // 渔轮
    IT_TACKLE_LINE          = 303; // 主线
    IT_TACKLE_LEADER        = 304; // 子线
    IT_TACKLE_BAIT          = 305; // 活饵
    IT_TACKLE_BOBBERS       = 306; // 浮漂
    IT_TACKLE_KEEPNETS      = 307; // 鱼库
    IT_TACKLE_HOOKS         = 308; // 鱼钩
    IT_TACKLE_LURES         = 309; // 拟饵
    IT_WEARABLE_HEAD_FRAME  = 401; // 头像框
    IT_WEARABLE_AVATAR      = 402; // 头像
    IT_EQUIP_SINKERS        = 501; // 铅坠
    IT_EQUIP_HOOK_REMOVERS  = 502; // 脱勾器
    IT_TICKET_MATCH         = 601; // 比赛券
    IT_FRAGMENTS_CARD       = 701; // 卡牌
    IT_REWARD_BAG           = 801; // 礼包奖励
    IT_GOODS_CHARGE         = 901; // 支付商品
    IT_CATCH_FISH           = 1001; // 鱼获
}

// 道具额外属性键值
enum ITEM_EXTRA_KEY {
    IEK_UNKNOWN         = 0;
    IEK_CURR_DURABILITY = 1; // 当前耐久度
    IEK_MAX_DURABILITY = 2;  // 当前耐久度上限
}

// 不堆叠类型 (货币默认堆叠 200以下 其他相同属性进行组合)
enum UNSTACK_TYPE {
    UST_UNKNOWN = 0;
    UST_TACKLE_RODS          = 301; // 钓竿
    UST_TACKLE_REEl          = 302; // 渔轮
    UST_TACKLE_LINE          = 303; // 主线
    UST_TACKLE_LEADER        = 304; // 子线
}

// 存储类型
enum STORAGE_TYPE {
    ST_UNKNOWN = 0;
    ST_STORE = 1; // 仓库
    ST_BAG   = 2; // 背包
}

enum ITEM_STATUS {
    IS_UNKNOWN         = 0;
    IS_NORMAL          = 1; // 正常
    IS_EXPIRED         = 2; // 过期
}

enum ITEM_OPERATION {
    IO_UNKNOWN      = 0;
    IO_ADD          = 1; // 增加
    IO_REDUCE       = 2; // 减少
    IO_LOCK         = 3; // 锁定
    IO_FREEZE       = 4; // 冻结
    IO_UPDATE       = 5; // 更新数据 (仅能以InstanceId作为更新参数)
}

// 物品来源
enum ITEM_SOURCE_TYPE {
    IST_UNKNOWN                   = 0;
    IST_PAY                       = 1;  // 支付
    IST_GM                        = 2;  // GM操作
    IST_DAILY_BONUS               = 3;  // 每日奖励

    IST_STORE_BUY                 = 4;  // 商店购买

    IST_GM_OPERATION              = 6;  // GM操作

    IST_ENTRY_POND_FEE            = 7;  // 进入钓场费用
    IST_FISH_CATCH_ADD            = 8;  // 捕获鱼奖励

    IST_POND_USE_ITEM             = 9;  // 钓场使用物品

    IST_TASK_REWARD               = 10; // 任务奖励
    IST_EXP_LEVEL_UP              = 11; // 升级奖励

    IST_TASK_POND_PROGRESS_REWARD = 12; // 任务进度奖励

    IST_PERSON_MAIL_REWARD        = 13; // 个人邮件奖励
    IST_SYSTEM_MAIL_REWARD        = 14; // 系统邮件奖励
    IST_MAIL_REWARD               = 15; // 邮件奖励(一键领取)

    IST_BAG_MOVE                  = 16; // 背包转移

    IST_FIX_ITEM                  = 17; // 装备维修
    IST_ITEM_SELL                 = 18; // 道具出售
    IST_ITEM_DURABILITY           = 19; // 耐久磨

    IST_GUIDE_REWARD              = 20; // 新手引导奖励

    IST_ACTIVITY_CONTINUOUS_LOGIN = 21; // 连续登录奖励

    IST_SPOT_KILL_LINE            = 22; // 切线丢失
    IST_SPOT_CATCH_FISH           = 23; // 钓场捕获鱼
    IST_SPOT_CATCH_NOT_FISH       = 24; // 钓场未捕获鱼

    IST_CDK_REWARD                = 25; // 兑换码奖励

    IST_ACTIVITY_REWARD           = 26; // 活动奖励
    IST_SPOT_THROW_ROD             = 27; // 抛竿

    // ......
}

// 奖励展示类型
enum REWARD_SHOW_TYPE {
    RST_UNKNOWN    = 0;
    RST_NORMAL     = 1;
    RST_TIP        = 2; // 弱Tips提示
    RST_DIALOG     = 3; // 强弹窗
    RST_COMBINE    = 4; // 合并展示
}

// 物品使用类型
enum BAG_OPERATE_TYPE {
    BOT_UNKNOWN = 0;
    BOT_USE     = 1;  // 使用
    BOT_SALE    = 2;  // 售卖
    BOT_LOCK    = 3;  // 锁定
}

// 物品品质类型
enum ITEM_QUALITY_TYPE {
    IQT_UNKNOWN    = 0;
    IQT_NORMAL     = 1; // 普通 C
    IQT_EPIC       = 2; // 优秀 B
    IQT_RARE       = 3; // 稀有 A
    IQT_UNIQUE     = 4; // 特殊 S
    IQT_SUPERB     = 5; // 极佳 SS
    IQT_LEGENDARY  = 6; // 传说 SSS
}

// 鱼竿子类型
enum ROD_SUB_TYPE {
    RODST_UNKNOWN      = 0;
    RODST_FLOAT        = 1; // 浮钓竿
    RODST_SPINNING     = 2; // 直柄竿
    RODST_CASTING      = 3; // 枪柄竿
    RODST_DIVIDER      = 4; // 分区竿
    RODST_EXTEND       = 5; // 伸缩竿
}

// 鱼轮子类型
enum REEL_SUB_TYPE {
    REELST_UNKNOWN      = 0;
    REELST_SPIN         = 1; // 纺车轮
    REELST_CAST         = 2; // 水滴轮
    REELST_SPOOL        = 3; // 鼓轮
}

// 主线子类型
enum LINE_SUB_TYPE {
    LINEST_UNKNOWN      = 0;
    LINEST_NYLON        = 1; // 尼龙线
    LINEST_CARBON       = 2; // 碳素线
    LINEST_COMPOSITE    = 3; // 编制线
    LINEST_FLUOROCARBON = 4; // 氟碳线
}

// 子线子类型
enum LEADER_SUB_TYPE {
    LEADERST_UNKNOWN      = 0;
    LEADERST_FLOAT        = 1; // 浮钓子线
    LEADERST_LURE         = 2; // 路亚前导线
}

// 真饵子类型
enum BAIT_SUB_TYPE {
    BAITST_UNKNOWN      = 0;
    BAITST_LIVE         = 101;  // 活饵
    BAITST_DEAD         = 102;  // 死饵
    BAITST_SEEDS        = 103;  // 种子
    BAITST_INSECT       = 104;  // 昆虫
    BAITST_INSECT_SHELL = 105;  // 甲壳
    BAITST_FISH         = 106;  // 鱼
    BAITST_FISH_BLOCK   = 107;  // 鱼块
    BAITST_FISH_EGG     = 108;  // 鱼卵
    BAITST_FISH_PASTA   = 109;  // 面团
    BAITST_GRAIN        = 110;  // [真饵]谷物
    BAITST_MEAT         = 111;  // [真饵]肉饵
    BAITST_DAIRY        = 112;  // [真饵]乳制
}

// 拟饵子类型
enum LURE_SUB_TYPE {
    LUREST_UNKNOWN              = 0;   // 无
    LUREST_SHAD                 = 201; // T尾
    LUREST_GRUB                 = 202; // 卷尾
    LUREST_WORM                 = 203; // 软虫
    LUREST_SHRIMP               = 204; // 虾管
    LUREST_MINNOW               = 301; // 米诺
    LUREST_POPPER               = 302; // 波趴
    LUREST_SPOON                = 303; // 勺子亮片
    LUREST_SPINNER              = 304; // 旋转亮片
    LUREST_VIB                  = 305; // VIB
    LUREST_TRACTOR              = 306; // 水面拖拉机
    LUREST_PENCIL               = 307; // 铅笔
    LUREST_NOSIE                = 308; // 嘈杂饵
    LUREST_KNOTTY               = 309; // 多节鱼
    LUREST_FROG                 = 310; // 雷蛙
    LUREST_BASSJIGGER           = 311; // 胡须佬
    LUREST_SPINNER_COMPOSITE    = 312; // 复合亮片
    LUREST_SHAKER               = 313; // 摇滚饵
}

// 浮漂子类型
enum BOBBER_SUB_TYPE {
    BST_UNKNOWN    = 0;
    BST_LIGHT      = 1; // 轻型浮漂
    BST_MIDDLE     = 2; // 中型浮漂
    BST_HEAVY      = 3; // 重型浮漂
    BST_THROUGH    = 4; // 贯穿漂
    BST_BASE       = 5; // 底座漂
}

// 鱼钩子类型
enum HOOKS_SUB_TYPE {
    HKST_UNKNOWN    = 0;
    HKST_LURE       = 1; // 路亚钩
    HKST_FLOAT      = 2; // 浮钓钩
    HKST_ISENI      = 101; // 伊势尼钩
    HKST_SLEEVE     = 102; // 袖钩
    HKST_LEAD_HEAD  = 201; // 铅头钩
    HKST_FISH_EYE   = 202; // 鱼眼钩
    HKST_BENT_SHANK = 203; // 曲柄钩
    HKST_WACKY      = 204; // wacky钩
}

// 鱼竿调性类型
enum ROD_ACTION_TYPE {
    RAT_UNKNOWN     = 0; // 未知
    RAT_XS          = 1; // 超慢
    RAT_S           = 2; // 慢
    RAT_MS          = 3; // 中慢
    RAT_M           = 4; // 中
    RAT_MF          = 5; // 中快
    RAT_F           = 6; // 快
    RAT_XF          = 7; // 超快
}

// 竿硬度类型 分别为软 中软 中 中硬 硬 超硬
enum ROD_HARDNESS_TYPE {
    RHT_UNKNOWN     = 0; // 未知
    RHT_UL          = 1; // 超软
    RHT_L           = 2; // 软
    RHT_ML          = 3; // 中软
    RHT_M           = 4; // 中
    RHT_MH          = 5; // 中硬
    RHT_H           = 6; // 硬
    RHT_XH          = 7; // 超硬
}

// 刺鱼等级
enum HOOKSET_RESULT {
    HSR_UNKNOWN     = 0; // 无
    HSR_BARELY      = 1; // 勉强
    HSR_SUCC        = 2; // 完成
    HSR_GOOD        = 3; // 优秀
    HSR_PERFECT     = 4; // 完美
}

// 水花类型
enum LURES_SPLASH_TYPE {
    LST_UNKNOWN      = 0; // 无水花
    LST_SMALL        = 1; // 小水花
    LST_MIDDLE       = 2; // 中水花
    LST_BIG          = 3; // 大水花
}

// 甩钩类型
enum UNHOOK_TYPE {
    UHT_UNKNOWN     = 0; // 无
    UHT_NO          = 1; // 否
    UHT_YES         = 2; // 是
}

// 鱼饵在水中悬停类型（目前针对于路亚饵）
enum BAIT_HOVER_TYPE {
    BHT_SURFACE = 0; // 水面
    BHT_BED = -1; // 水底
    BHT_COURSE1 = 1; // 水层1
    BHT_COURSE2 = 2; // 水层2
    BHT_COURSE3 = 3; // 水层3
}

// 渔具出售类型
enum TACKLE_SALE_TYPE {
    TST_NOT_SALE = 0;  // 不可出售
    TST_FREE     = 1;  // 免费出售
    TST_REDUCE   = 2;  // 折价出售
    TST_ORIGINAL = 3;  // 原价出售
}

// 商城展示样式(客户端使用)
enum STORE_SHOW_STYLE {
    STRT_NORMAL    = 0;  // 普通样式(默认)
    STRT_AD        = 1;  // 广告样式
    STRT_RECOMMEND = 2;  // 推荐样式
    STRT_ROOM      = 3;  // 房间样式
    STRT_ROD_PACK  = 4;  // 鱼竿架商场样式
}

// 钓场事件类型
enum POND_EVENT_CHANGE_TYPE {
    PECV_UNKNOWN          = 0;
    PECV_EX_LEVEL         = 1; // 等级变化
    PECV_ENERGY_CHANGE    = 2; // 体力变化
}

// 旅途背包类型
enum TRIP_BAG_TYPE {
    TBT_UNKNOWN       = 0;
    TBT_FISHING_GEAR  = 1; // 渔具包
    TBT_FOOD          = 2; // 食物盒
}

// 旅途背包操作
enum TRIP_BAG_OPERATE {
    TBO_UNKNOWN       = 0;
    TBO_IN            = 1; // 移入
    TBO_OUT           = 2; // 移出
}

// 旅途钓组位置
enum TRIP_ROD_SIT {
    TRS_UNKNOWN = 0;
    TRS_ROD     = 1;  // 鱼竿
    TRS_REEL    = 2;  // 鱼轮
    TRS_LINE    = 3;  // 主线
    TRS_LEADER  = 4;  // 子线
    TRS_BAIT    = 5;  // 鱼饵
    TRS_BOBBER  = 6;  // 浮漂
    TRS_HOOKS   = 7;  // 鱼钩
    TRS_SINKERS = 8;  // 铅坠
}

enum FISH_SIZE_TYPE {
    SIZE_UNKNOWN = 0;
    SIZE_YOUNG   = 1;  // 幼年
    SIZE_ADULT   = 2;  // 成年
    SIZE_PRIZE   = 3;  // 奖杯
    SIZE_SPECIAL = 4;  // 特殊
    SIZE_LEGEND  = 5;  // 传说
}

// 道具品牌
enum TACKLE_BRAND_TYPE {
    TACKLEBT_UNKNOWN     = 0;
    TACKLEBT_KF          = 1; // KF
    TACKLEBT_INSECT      = 2; // 昆虫
    TACKLEBT_TORAY       = 3; // 东丽
    TACKLEBT_YGK         = 4; // YGK
    TACKLEBT_SIEGEL      = 5; // 西格
}

// 道具品牌系列
enum TACKLE_BRAND_SERIES_TYPE {
    TBST_UNKNOWN          = 0;
    TBST_ROLLFISH         = 1;
    TBST_CREST            = 2;
    TBST_ATHLON           = 3;
    TBST_DEMONHUNTER      = 4;
    TBST_CORSSFIRE        = 5;
    TBST_EXPRIDE          = 6;
    TBST_PMAXSX           = 7;
    TBST_VERITAS          = 8;
    TBST_GREATHUNTING     = 9;
    TBST_GIANTWAVES       = 10;
    TBST_SHADOW           = 11;
    TBST_CQBFS            = 12;
    TBST_CARDIFF          = 13;
    TBST_MAX4             = 14;
    TBST_NYLON            = 15;
    TBST_UPGRADE          = 16;
    TBST_LONG             = 17;
    TBST_CLASSICLONG      = 18;
    TBST_MAYFLY           = 19;
    TBST_SHAD             = 20;
    TBST_GRUB             = 21;
    TBST_POPPER           = 22;
    TBST_MINNOW           = 23;
    TBST_SPOON            = 24;
    TBST_SPINNER          = 25;
    TBST_CHKHOOK          = 26;
}

/**************************************************************************/
//                              支付相关                                     /
/**************************************************************************/
enum PAY_TYPE {
    PT_UNKNOWN            = 0;
    PT_SANDBOX            = 1; // 测试支付
    PT_WECHAT             = 2;
    PT_ALIPAY             = 3;
    PT_APPLE              = 4; // [支付渠道]APP_STORE
    PT_GOOGLE             = 5; // [支付渠道]GOOGLE_PLAY
}

// 购买限制类型
enum BUY_LIMIT_TYPE {
    BLT_NONE    = 0;  // 无限制
    BLT_DAY     = 1;  // 按天限制
    BLT_WEEK    = 2;  // 按周限制
    BLT_MONTH   = 3;  // 按月限制
    BLT_FOREVER = 4;  // 永久限制
}

// 支付入口场景类型
enum PURCHASE_ENTRANCE_TYPE {
    PET_UNKNOWN         = 0;
    PET_LOBBY_STORE     = 1; // 商城
    PET_LOBBY_TASK      = 2; // 任务
    PET_LOBBY_ACTIVITY  = 3; // 活动
    PET_LOBBY_GAME      = 4; // 游戏
    PET_LOBBY_OTHER     = 5; // 其它
}

// 触发支付原因
enum PURCHASE_TRIGGER_TYPE {
    PTT_UNKNOWN                 = 0;
    PTT_INITIATIVE_CLICK        = 1; // 主动点击
    PTT_LACK_SPIN_POPUP         = 2; // 缺体力弹出
    PTT_LACK_COIN_POPUP         = 3; // 缺金币弹出
    PTT_LACK_DIAMOND_POPUP      = 4; // 缺钻石弹出
    PTT_LOGIN_AUTO_POPUP        = 5; // 登录自动弹
    PTT_MODULE_AUTO_POPUP       = 6; // 功能自动弹(如鱼护满提示扩容弹出)
    PTT_MODULE_ACTIVITY_POPUP   = 7; // 活动触发
    PTT_TOP_UP_ACTIVITY         = 8; // 充值活动
}

// 支付失败原因，[0-7]为Unity Purchaseing Service返回的PurchaseFailureReason结果
// [8+]的为项目中的支付流程异常问题
enum PURCHASE_FAILED_TYPE {
    PURCHASE_FAILED_PURCHASING_UNAVAILABLE      = 0;        // 支付功能不可使用。The system purchasing feature is unavailable.
    PURCHASE_FAILED_EXISTING_PURCHASE_PENDING   = 1;        // 支付请求一个商品时，已经有一个对应商品正在支付中。A purchase was already in progress when a new purchase was requested.
    PURCHASE_FAILED_PRODUCT_UNAVAILABLE         = 2;        // 该商品不可使用，在第三方商店中找不到。The product is not available to purchase on the store.
    PURCHASE_FAILED_SIGNATURE_INVALID           = 3;        // 购买的收据签名验证失败。Signature validation of the purchase's receipt failed.
    PURCHASE_FAILED_USER_CANCELLED              = 4;        // 用户手动取消支付。The user opted to cancel rather than proceed with the purchase.
    PURCHASE_FAILED_PAYMENT_DECLINED            = 5;        // 付款存在异常问题。There was a problem with the payment.
    PURCHASE_FAILED_DUPLICATE_TRANSACTION       = 6;        // 交易完成时，出现重复的交易。A duplicate transaction error when the transaction has already been completed successfully.
    PURCHASE_FAILED_UNKNOWN                     = 7;        // 未知异常。A catch-all for unrecognized purchase problems.
    PURCHASE_FAILED_CREATE_ORDER_FAILED         = 8;        // 下单失败
    PURCHASE_FAILED_INVALID_RECEIPT             = 9;        // 无效的收据凭证,空收据
    PURCHASE_FAILED_DELIVER_FAILED              = 10;       // 发货失败
    PURCHASE_FAILED_SERVICE_NO_INITIALIZE       = 11;       // 支付服务模块初始化失败
    PURCHASE_FAILED_PURCHASING                  = 12;       // 正在支付中
}

// 支付订单状态
enum PURCHASE_BILL_STATUS {
    PBS_UNKNOWN              = 0; //未知
    PBS_PLACE                = 1; //新下单
    PBS_CANCEL               = 2; //已取消
    PBS_PLACE_ANEW           = 3; //重新下单（取消未支付订单）
    PBS_PAID                 = 4; //已支付
    PBS_VERIFY_FAILURE       = 5; //（校验）校验失败
    PBS_VERIFY_PAID_CANCEL   = 6; //（校验）支付取消
    PBS_VERIFY_CONSUMPTION   = 7; //（校验）已消费
    PBS_VERIFY_SUCCESS       = 8; //校验成功
    PBS_DELIVERED            = 9; //已发货
    PBS_REPLENISHMENT        = 10; //补单成功
    PBS_ABNORMAL_DELIVERY    = 11; //异常订单（坏账订单）
    PBS_DELIVER_FAILURE      = 12; //发货失败
    PBS_ORDER_NOT_EXIST      = 13; //订单不存在
}

/**************************************************************************/
//                              邮件消息相关                                  /
/**************************************************************************/


/**************************************************************************/
//                              GM相关                                  /
/**************************************************************************/
enum GM_CMD {
    GC_UNKNOWN       = 0;
    GC_GAMETIME      = 1;  // 设置游戏时间
    GC_LOAD_RIG_RULE = 2;  // 加载干架规则
    GC_DOWNTIME_KICK = 3;  // 维护踢人

    // -------------- world ----------------------
    GC_WORLD_BEGIN   = 1000;
    GC_CLEAR_WEATHER = 1001;  // 清理天气
    GC_WORLD_END     = 1099;

    // -------------- Hall ----------------------
    GC_HALL_BEGIN               = 1100;
    GC_HALL_ITEM                = 1101;
    GC_HALL_CONTINUOUS_LOGIN    = 1102; // 连续登录奖励
    GC_HALL_CREATE_CDK          = 1103; // cdk 创建
    GC_HALL_QUERY_CDK_BATCHES   = 1104; // cdk 批次查询
    GC_HALL_QUERY_CDK_RECORDS   = 1105; // cdk 使用记录查询
    GC_HALL_DISABLE_CDK_BATCHES = 1106; // cdk 批次禁用
    GC_HALL_END                 = 1199;

    // -------------- asset ----------------------
    GC_ASSET_BEGIN          = 1200;
    GC_ASSET_CLEAR_CATEGORY = 1201;
    GC_ASSET_END            = 1299;

    // -------------- task ----------------------
    GM_TASK_BEGIN        = 1300;
    GM_TASK_OPERATE      = 1301;  // 任务操作
    GM_TASK_PROGRESS_SET = 1302;  // 设置积分进度
    GM_TASK_SUB_UPDATE   = 1303;  // 更新子任务进度
    GM_TASK_END          = 1399;

    // -------------- spot ----------------------
    GM_CMD_GM_SPOT_BEGIN    = 1400;
    GM_CMD_GC_MODIFY_ENERGY = 1401;  // 修改体力
    GM_CMD_GM_SPOT_END      = 1499;

  	// -------------- hook ----------------------
    GM_CMD_GM_HOOK_BEGIN        = 1500;
    GM_CMD_GC_OPERATE_HOOK_FISH = 1501;  // 中鱼操作
    GM_CMD_GM_HOOK_END          = 1599;

    // -------------- msg ----------------------
    GC_MSG_BEGIN         = 1600;
    GC_MSG_SEND_MAIL     = 1601;  // 发送邮件
    GC_MSG_BROADCAST     = 1610;  // 广播
    GC_MSG_ANN_POP_GET   = 1611;  // 拍脸图获取
    GC_MSG_ANN_POP_EDIT  = 1612;  // 拍脸图编辑
    GC_MSG_ANN_POP_DEL   = 1613;  // 拍脸图删除
    GC_MSG_END           = 1699;

    // -------------- user ----------------------
    GC_USER_BEGIN               = 1700;
    GC_USER_BATCH_PLAYER_INFO   = 1701;  // 获取玩家信息请求
    GC_USER_END                 = 1799;


    // -------------- GM ----------------------
    GC_RANK_BEGIN               = 1800;
    GC_RANK_FLUSH_RANK          = 1801; // 刷新排行
    GC_RANK_REWARD_RANK         = 1802; // 强制发奖
    GC_RANK_END                 = 1899;
}

/**************************************************************************/
//                              Event相关                                  /
/**************************************************************************/
enum EVENT_TYPE {
    ET_UNKNOWN            = 0;
    ET_ROLE_LEVEL_UP      = 1;  // 角色升级
    ET_ITEM_CHANGE        = 10; // 道具变更
    ET_ITEM_ADD           = 11; // 道具增加
    ET_ITEM_REDUCE        = 12; // 道具消耗
    ET_FISH_GET           = 30; // 中鱼
    ET_FISH_GET_WEIGHT    = 31; // 中鱼-重量 (非标准事件-拓展处理)
    ET_LOGIN              = 40; // 用户登录事件
    ET_LOGOUT             = 41; // 用户登出
    ET_TASK_COMPLETE      = 50; // 任务完成
    ET_ENTER_SPOT         = 60; // 进入钓点
    ET_LEAVE_SPOT         = 61; // 离开钓点
    ET_TRIP_SETTLE        = 62; // 旅行结算
    ET_TRIP_SETTLE_WEIGHT = 63; // 结算时最大的鱼 (非标准事件)
    ET_TRIP_SETTLE_VAL    = 64; // 结算时最有价值的鱼 (非标准事件)
    ET_FISH_KEEPNET       = 65; // 鱼入户
}


// int 事件子字段埋点 mainKey * 1000
// 与下面 EVENT_STR_KEY 中枚举值不能重复
enum EVENT_INT_KEY {
    EIK_UNKNOWN            = 0;
    EIK_COMMON_TS          = 1;     // 通用-时间戳
    EIK_ROLE_LEVEL         = 2;     // 角色-等级
    EIK_ROLE_BEFORE_LEVEL  = 3;     // 角色-升级前等级
    // 道具相关 1001-2000
    EIK_ITEM_ID            = 1001;  // 道具-id
    EIK_ITEM_CATEGORY      = 1002;  // 道具-种类
    EIK_ITEM_TYPE          = 1003;  // 道具-类型
    EIK_ITEM_SOURCE        = 1004;  // 道具-来源
    EIK_ITEM_COUNT         = 1005;  // 道具-数量
    // 中鱼相关 3001-4000
    EIK_FISH_POND              = 3001;  // 中鱼-钓场
    EIK_FISH_WEATHER           = 3002;  // 中鱼-天气 (未实现)
    EIK_FISH_ID                = 3003;  // 中鱼-鱼id
    EIK_FISH_WEIGHT            = 3004;  // 中鱼-鱼重
    EIK_FISH_ROG               = 3005;  // 中鱼-钓竿 (未实现)
    EIK_FISH_LENGTH            = 3006;  // 中鱼-鱼长
    EIK_FISH_QUALITY           = 3007;  // 中鱼-鱼品质 (未确定)
    EIK_FISH_GENUS             = 3008;  // 中鱼-属科
    EIK_FISH_SOMATOTYPE        = 3009;  // 中鱼-体型
    EIK_FISH_SPECIAL           = 3010;  // 中鱼-品种
    EIK_FISH_BAIT              = 3011;  // 中鱼-钓饵
    EIK_FISH_BROKEN            = 3012;  // 中鱼-鱼破损
    EIK_FISH_CONGRATULATION_ID = 3013;  // 中鱼-钓鱼日志祝贺词ID
    EIK_FISH_NUM               = 3014;  // 中鱼-鱼数量
    // 登录相关 4001-5000
    EIK_LOGIN_DATE            = 4002;  // 登入-日期
    EIK_LOGIN_MODEL           = 4003;  // 登入-方式
    EIK_LOGOUT_ONLINE_TIME    = 4101;  // 登出-在线时长
    EIK_LOGOUT_MODEL          = 4102;  // 登出-方式
    // 钓场相关 5001-6000
    EIK_POND_ID            = 5002;  // 钓场 - 钓场id
    EIK_SPOT_ID            = 5003;  // 钓场 - 钓点id
    EIK_TRIP_COINS_RECORD  = 5004;  // 结算 - 金币记录
    EIK_TRIP_FISH_VAL      = 5005;  // 结算 - 渔获价值
    // 任务相关 6001-7000
    EIK_TASK_ID            = 6001;  // 任务-id
    EIK_TASK_TYPE          = 6002;  // 任务-类型
    EIK_TASK_SUB_TYPE      = 6003;  // 任务-子任务类型
}

// string 事件子字段埋点 mainKey * 1000
enum EVENT_STR_KEY {
    ESK_UNKNOWN = 0;
    // 登录相关 4001-5000
    ESK_LOGOUT_RECENT_MSG_IDS = 4103;  // 登出-最近消息id列表
    // 钓场相关 5001-6000
    ESK_ROOM_ID               = 5001;  // 钓场 - 房间id
}


enum EVENT_UNIQUE_KEY {
    EUK_UNKNOWN            = 0;
    EUK_TRIP_COINS_RECORD  = 20141088; // 旅行结算金币记录
}

/**************************************************************************/
//                              统计相关                                  /
/**************************************************************************/

// 统计类型
enum STATS_TYPE {
    STATS_TYPE_UNKNOWN  = 0; // 重复了
    ST_NORMAL           = 1; // 通用统计
    ST_FISH             = 2; // 鱼(品种)统计
    ST_SETTLE_RECORD    = 3; // 结算记录统计
    ST_PLAY_TIME        = 4; // 游玩时间统计
    ST_TROPHY           = 5; // 奖杯统计
    ST_FISH_ID          = 6; // 鱼(id)统计
}

// 辅助统计类型
enum StatsSubType {
    STS_UNKNOWN          = 0;
    STS_FISH_WEIGHT_MAX  = 1; // 鱼-最大重量
    STS_FISH_COUNT       = 2; // 鱼-数量
    STS_FISH_BEST_SIZE   = 3; // 鱼-最好的尺寸
}
/**************************************************************************/
//                              任务相关                                  /
/**************************************************************************/
// 任务状态
enum TASK_STATUS {
    TS_UNKNOWN           = 0;
    TS_UNACCEPTABLE      = 1; // 未接受
    TS_ACCEPTABLE        = 2; // 可接受
    TS_DURING            = 3; // 进行中
    TS_SUBMIT            = 4; // 已提交
    TS_COMPLETE          = 5; // 已完成
    TS_DELETE            = 6; // 删除
}

enum TASK_ADD_TYPE {
    TAT_UNKNOWN     = 0;
    TAT_ADD         = 1; /// 加类型
    TAT_VAL         = 2; // 值类型
}

enum TASK_CATEGORY {
    TC_UNKNOWN = 0;
    TC_MAIN    = 1;  // 主线任务
    TC_POND    = 2;  // 场景探索任务
    TC_DAILY   = 3;  // 每日任务
    TC_ACHIEVE = 4;  // 成就任务
    TcFishachieve = 6;  // 图鉴任务
}

// 进度积分系统
enum PROGRESS_TYPE {
    PXT_UNKNOWN      = 0;
    PXT_ENCYCLOPEDIA = 1;  // 百科
    PXT_HANDBOOK     = 2;  // 图鉴
}

// 表达式 是否
enum EXPRESSION_TYPE {
    ET_NO  = 0; // 否
    ET_YES = 1; // 是
}

enum VAL_OPERATE {
    VO_UNKNOWN       = 0;  // 未知
    VO_LESS          = 1;  // 小于
    VO_LESS_EQUAL    = 2;  // 小于等于
    VO_EQUAL         = 3;  // 等于
    VO_GREATER       = 4;  // 大于
    VO_GREATER_EQUAL = 5;  // 大于等于
    VO_NOT_EQUAL     = 6;  // 不等于
}

enum AOI {
    AOI_UNKNOWN = 0;
    AOI_AND     = 1;  // 与
    AOI_OR      = 2;  // 或
    AOI_INVERT  = 3;  // 非
}

enum TASK_OPEN {
    TO_UNKNOW        = 0;
    TO_ROLE_LEV_GT   = 1;  // 角色等级大于等于
    TO_TASK_COMPLETE = 2;  // 任务完成
}

enum TASK_TRACE {
    TT_UNKNOW                 = 0;   // 无
    TT_TRACE_YES              = 1;   // 追踪
    TT_TRACE_AUTO             = 2;   // 自动追踪        
}

// 累加类型
enum SUM_ADD {
    SA_UNKNOWN      = 0;
    SA_ADD          = 1;  // +=
    SA_VAL          = 2;  // =
    SA_MAX          = 3;  // max(val,cur)
    SA_UNI_ADD      = 4;  // 去重累加
    SA_MIN          = 5;  // 最小值
    SA_CONTINUE_DAY = 6;  // 连续日期
}

// 假期类型(数脉)
enum HOLIDAY_TYPE {
    HT_UNKNOWN  = 0;
    HT_WORKING  = 1;  // 工作日
    HT_WEEKEND  = 2;  // 周末
    HT_HOLIDAY  = 3;  // 假期
}

/**************************************************************************/
//                              msg相关                                     /
/**************************************************************************/

enum MSG_CATEGORY {
    MC_UNKNOWN           = 0;
    MC_MAIL              = 1; // 邮件
    MC_NOTIFY            = 2; // 通知
}

// 邮件状态
enum MAIL_STATUS {
    MS_UNREAD            = 0; // 未读
    MS_READ              = 1; // 已读
    MS_EXPIRED           = 2; // 已过期
    MS_DELETE            = 3; // 删除
}

enum MAIL_ATTACH_STATUS {
    MAS_NOT_ATTACH         = 0; // 没有附件
    MAS_HAD_ATTACH         = 1; // 有附件
}

enum MAIL_ATTACH_CLAIM_STATUS {
    MACS_NOT_CLAIM         = 0; // 未领取
    MACS_HAD_CLAIM         = 1; // 已领取
}

enum MAIL_TYPE {
    MT_UNKNOWN           = 0;
    MT_ORDINARY          = 1; // 普通
    MT_SYSTEM            = 2; // 系统
}

// 邮件拓展参数key
enum MAIL_EXTEND_KEY {
    MEK_UNKNOWN     = 0;
    MEK_RANK_ID     = 1; // 排行榜id
    MEK_RANK_SORT   = 2; // 排行榜排名
    MEK_ACTIVITY_ID = 3; // 活动id
}

// 播报类型
enum MSG_BROADCAST_TYPE {
  MB_UNKNOWN   = 0;
  MB_BROADCAST = 1; // 广播
  MB_TIPS      = 2; // tips
  MB_POPUP     = 3; // 弹窗
}

// 通知类型
enum MSG_BROADCAST_NTF_TYPE {
    MBN_UNKNOWN    = 0;
    MBN_NORMAL     = 1; // 默认
    MBN_FISH_MOUTH = 2; // 鱼口
}

// 事件
enum MSG_BROADCAST_EVENT {
    MBE_UNKNOWN    = 0;
    MBE_CATCH_FISH = 1; // 中鱼
    MBE_FISH_MOUTH = 2; // 鱼口
}

// 钓场地图水下结构
enum UNDER_WATER_STRUCTURE {
    UWS_OPEN_WATER    = 0; // 开放水域
    UWS_WATER_GRASS   = 1; // 水草 
    UWS_STONE         = 2; // 石头
    UWS_DRIFTWOOD     = 3; // 沉木
    UWS_PIER          = 4; // 桥墩
    UWS_DEEPPIT       = 5; // 深坑
    UWS_RIDGE         = 6; // 尖脊
    UWS_FAULT         = 7; //断层
    UWS_ROCKSHELF     = 8; //岩架
    UWS_BAY           = 9; //湾子
    UWS_MUD           = 10; //泥底
    UWS_GRAVEL        = 11; //碎石底
}

// 地图水层类型
enum MAP_WATER_LAYER_TYPE {
    MWLT_UNKNOWN    = 0;
    MWLT_SURFACE    = 1;  // 表层
    MWLT_MIDDLE     = 2;  // 中层
    MWLT_BOTTOM     = 3;  // 底层
}

enum FISHING_TRICK_LEVEL_TYPE {
    FTLT_ZERO   = 0;  // 零档
    FTLT_FIRST  = 1;  // 一档
    FTLT_SECOND = 2;  // 二档
    FTLT_THIRD  = 3;  // 三挡
}

// 钓鱼增益类型
enum FISHING_BUFF_TYPE {
    FBT_NONE          = 0;    // 无

    FBT_BAIT_TRICK    = 101;  // 动作(技法)
    FBT_BAIT_SPLASH   = 102;  // 水花
    FBT_BAIT_NOISE    = 103;  // 音效
    FBT_BAIT_LIGHTING = 104;  // 光效
}

// 饵动作技法类型
enum FISHING_BAIT_TRICK_TYPE {
    FBTT_NONE                 = 0;  // 无
    FBTT_RULELESS             = 1;  // 无规则运动
    FBTT_FORZEN               = 2;  // 静止
    FBTT_DRIFTING             = 3;  // 下沉(自由落体)
    FBTT_STRAIGHT             = 4;  // 直线运动
    FBTT_STRAIGHT_AND_SHAKING = 5;  // 直线+摇摆
    FBTT_SHAKING              = 6;  // 摇摆
    FBTT_TWITCHING            = 7;  // 抽搐
    FBTT_STOP_AND_GO          = 8;  // 走停
}

// 饵水花类型
enum FISHING_BAIT_SPLASH_TYPE {
    FBST_NONE   = 0;  // 无
    FBST_SMALL  = 1;  // 小
    FBST_MIDDLE = 2;  // 中
    FBST_BIG    = 3;  // 大
}

// 饵音效类型
enum FISHING_BAIT_NOISE_TYPE {
    FBNT_NONE   = 0;  // 无
    FBNT_LOW    = 1;  // 小
    FBNT_MIDDLE = 2;  // 中
    FBNT_HIGH   = 3;  // 大
}

// 饵光效类型
enum FISHING_BAIT_LIGHTING_TYPE {
    FBLT_NONE = 0;                  // 无
    FBLT_WEEK = 1;                  // 弱(小)
    FBLT_SOFT = 2;                  // 一般(中)
    FBLT_HARD = 3;                  // 强(大)
}

// 鱼破损状态
enum FISH_DAMAGED_LV {
    FDL_INTACT = 0;     // 完好
    FDL_LV1 = 1;        // 1级破损
    FDL_LV2 = 2;        // 2级破损
}

// 搏鱼对抗类型
enum BATTLING_FIGHT_TYPE {
    BFT_UNAVAILING  = 0; // 无效对抗
    BFT_BEST        = 1; // 最优对抗
    BFT_GOOD        = 2; // 次优对抗
    BFT_DRASTIC     = 3; // 高压对抗
}

// 钓组钓法类型
enum FISHING_RIG_TYPE {
    FRT_UNKNOWN     = 0; // 无
    FRT_LURE        = 1; // 路亚
    FRT_FLOAT       = 2; // 浮钓
    FRT_ROCK        = 3; // 矶钓
}

/**************************************************************************/
//                              排行榜相关                                     /
/**************************************************************************/

// 排名通用数据封装
enum RANK_INFO_KEY {
    RIK_UNKNOWN     = 0;
    RIK_FISH_ID     = 1;  // 鱼id
    RIK_FISH_VALUE  = 2;  // 鱼价值
    RIK_BAIT_ID     = 3;  // 饵id
    RIK_FISH_WEIGHT = 4;  // 鱼重量（克）
    RIK_FISH_POND   = 5;  // 钓场id
    RIK_FISH_LENGTH = 6;  // 鱼长度（厘米）
    RIK_FISH_BROKEN = 7;  // 鱼破损
}

// 刷新时间类型
enum FLUSH_TIME_TYPE {
    FTT_PERMANENT = 0; // 永久
    FTT_MONTH     = 1; // 每月
    FTT_WEEK      = 2; // 每周
    FTT_DAY       = 3; // 每天
}

// 排行榜类型
enum RANK_TYPE {
    RANK_TYPE_UNKNOWN = 0;
    RT_FISHING_WEIGHT    = 1; // 钓鱼重量榜单
    RT_FISHING_VALUE     = 2; // 钓鱼价值榜单
}

/**************************************************************************/
//                              公告相关                                     /
/**************************************************************************/

// 公告标签类型定义
enum ANN_TAG_TYPE {
    TAG_NONE = 0;       // 无
    TAG_NEW  = 1;       // NEW
    TAG_HOT  = 2;       // HOT
}

// 公告生效类型定义
enum ANN_EFFECT_TYPE {
    EFFECT_NONE     = 0;   // 无
    EFFECT_LOGIN    = 1;   // 登录页
    EFFECT_HOMEPAGE = 2;   // 游戏首页
}

// 公告显示类型定义
enum ANN_DISPLAY_TYPE {
    DISPLAY_NONE    = 0;   // 无
    DISPLAY_DAILY   = 1;   // 每日一次
    DISPLAY_HISTORY = 2;   // 历史一次
    DISPLAY_LOGIN   = 3;   // 每次登录
}

// 公告跳转类型定义
enum ANN_JUMP_TYPE {
    JUMP_NONE         = 0;   // 无
    JUMP_STORE        = 1;   // 商城
    JUMP_ACTIVE       = 2;   // 活动中心
    JUMP_ANNOUNCEMENT = 3;   // 公告中心
    JUMP_SELECT_POND  = 4;   // 选场中心
}

/**************************************************************************/
//                              数据上报相关                                 /
/**************************************************************************/

enum DATA_REPORT_TYPE {
    DRT_UNKNOWN      = 0;  // 未知类型
    DRT_APP_OPEN     = 1;  // 打开上报
    DRT_NOVICE_SCENE = 2;  // 新手引导
    DRT_FISHING      = 3;  // 钓鱼记录
  }

/**************************************************************************/
//                              CDK相关                                 /
/**************************************************************************/
// CDK 批次状态
enum CDK_BATCH_STATUS {
    CBS_UNKNOWN = 0;
    CBS_ENABLE  = 1;  // 启用
    CBS_DISABLE = 2;  // 作废
}

// CDK生成方式枚举
enum CDK_GENERATION_OPTION {
    CGO_UNKNOWN = 0;
    CGO_RANDOM  = 1;  // 随机生成
    CGO_CUSTOM  = 2;  // 自定义
}

/**************************************************************************/
//                              活动相关                                 /
/**************************************************************************/


// 活动类型
enum ACTIVITY_TYPE {
    AT_UNKNOWN        = 0;  // 未知
    AT_WEIGHT_PURSUIT = 1;  // 爆护之路（重量追求）
}
