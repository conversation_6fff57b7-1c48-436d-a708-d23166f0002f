// 钓点协议
syntax = "proto3";
package spotPB;

option go_package = "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/spot;spotPB";

import "enum.proto";
import "errors.proto";
import "common.proto";

// 请求钓点场景信息
message GetSpotSceneReq {
    int64  pond_id = 1; // 钓场id
    string room_id = 2; // 房间号
    int32  spot_id = 3; // 钓点id
}

// 请求钓点场景信息响应
message GetSpotSceneRsp {
    common.Result                  ret       = 1; // 结果
    int64                          pond_id   = 2; // 钓场id
    int32                          spot_id   = 3; // 钓点id
    repeated common.FisherSpotSync sync_list = 4; // 玩家同步信息列表
    int32                          rig_id    = 5; // 钓组id
    int32                          energy    = 6; // 体力
}

// 玩家进房广播
message EnterRoomBroadcastNtf {
    common.FisheryPlayerInfo player_info = 1; // 玩家信息
}

// 选择进入钓点请求
message ChooseSpotReq {
    int32 spot_id = 1;     // 钓点id
  }

  // 选择进入钓点响应
  message ChooseSpotRsp {
    common.Result ret = 1;     // 结果
    int32 spot_id     = 2;     // 钓点id
  }

// 同步钓点信息
message SyncSpotInfoReq {
    common.FisherSpotSync sync_info = 1; // 同步信息
}

// 同步钓点信息广播
message SyncSpotInfoBroadcastNtf {
    common.FisherSpotSync sync_info = 1; // 同步信息
}

// 抛竿请求
message ThrowRodReq {
    int64                 pond_id    = 1;  // 钓场id
    int32                 rig_id     = 2;  // 钓组id
    common.HookBait       hook_bait  = 3;  // 钩饵组合
    common.ThrowGridInfo  grid_info  = 4;  // 抛竿信息
    common.HookHabitParam hook_habit = 5;  // 中鱼习性参数
}

// 抛竿响应
message ThrowRodRsp {
    common.Result          ret          = 1;  // 结果
    int64                  pond_id      = 2;  // 钓场id
    int32                  rig_id       = 3;  // 钓组id
    common.HookBait        hook_bait    = 4;  // 钩饵组合
    common.FishSyncControl sync_control = 5;  // 请求同步控制
}

// 收竿请求
message CatchRodReq {
    int64                  pond_id           = 1;  // 钓场id
    int32                  rig_id            = 2;  // 钓组id
    common.HookHabitParam  hook_habit        = 3;  // 中鱼习性参数
    common.FishDamagedInfo fish_damaged_info = 5;  // 鱼受损信息         
}

// 收竿返回
message CatchRodRsp {
    int64                 pond_id     = 1; // 钓场id
    int32                 rig_id      = 2; // 钓组id
    common.FISH_RESULT    fish_result = 3; // 鱼结果
    common.FishDetailInfo fish_info   = 4; // 鱼详细信息
    bool                  new_record  = 5; // 是否是新记录
}

// 中鱼请求
message FishHookReq {
    int64                 pond_id      = 1;  // 钓场id
    int32                 rig_id       = 2;  // 钓组id
    common.ThrowGridInfo  grid_info    = 4;  // 抛竿信息
    common.HookHabitParam hook_habit   = 5;  // 中鱼习性参数
}

// 中鱼响应
message FishHookRsp {
    int64                pond_id       = 1; // 钓场id
    int32                rig_id        = 2; // 钓组id
    common.ThrowGridInfo grid_info     = 3; // 抛竿信息
    int64                next_req_time = 4; // 下次请求时间(毫秒)
    int64                fake_fish_id  = 5; // 假咬口鱼id
    common.FishInfo      fish_info     = 6; // 中鱼信息
}

// 鱼入护操作请求
message FishEntryOptReq {
    string                     fish_instance = 1; // 鱼实例id
    common.FISH_ENTRY_OPT_TYPE action        = 2; // 操作类型
}

// 鱼入护操作响应
message FishEntryOptRsp {
    common.Result              ret           = 1; // 结果
    string                     fish_instance = 2; // 鱼实例id
    common.FISH_ENTRY_OPT_TYPE action        = 3; // 操作类型
    int32                      fish_weight   = 4; // 总重量(鱼护中的鱼总重量g)
}

// 玩家鱼入护广播
message PlayerFishEntryBroadcastNtf {
    uint64 player_id      = 1; // 玩家id
    int32  fish_weight    = 2; // 当前入护鱼重量(g)
    int32  keepnet_weight = 3; // 鱼护总重量(g)
}

// 鱼护操作请求
message FishKeepnetOptReq {
    int64                        pond_id       = 1; // 钓场id
    string                       fish_instance = 2; // 鱼实例id
    common.FISH_KEEPNET_OPT_TYPE action        = 3; // 操作类型
}

// 鱼护操作响应
message FishKeepnetOptRsp {
    common.Result                ret           = 1; // 结果
    string                       fish_instance = 2; // 鱼实例id
    common.FISH_KEEPNET_OPT_TYPE action        = 3; // 操作类型
    int32                        fish_weight   = 4; // 总重量(鱼护中的鱼总重量)
}

// 鱼护中鱼详细信息查询
message KeepnetFishInfoReq {

}

// 鱼护中鱼详细信息响应
message KeepnetFishInfoRsp {
    repeated common.FishDetailInfo fish_info    = 1; // 鱼信息
}

// 查询房间所有玩家鱼护信息
message GetRoomAllPlayerInfoReq {

}

// 查询房间所有玩家鱼护信息响应
message GetRoomAllPlayerInfoRsp {
    repeated common.FisheryPlayerInfo player_info = 1; // 玩家信息
}

// 退出房间
message ExitRoomReq {
    int64  pond_id = 1; // 钓场id
    string room_id = 2; // 房间id
}

// 退出房响应
message ExitRoomRsp {
    common.Result       ret        = 1; // 结果
    ExitRoomSettleInfo  SettleInfo = 2; // 结算信息
}

// 退出房间广播
message ExitRoomBroadcastNtf {
    uint64 player_id = 1; // 玩家id
}

// 退出结算通知
message ExitRoomSettleInfo {
    int32                    total_exp   = 1; // 经验数
    repeated common.ItemBase reward_info = 2; // 奖励信息
    bool                 new_coin_record = 3; // 新金币记录
    int64                     enter_time = 4; // 进入钓场时间戳-秒
}

// 退出结算通知--（版号服特用）
message ExitRoomSettleNtf {
    int32                    total_exp   = 1; // 经验数
    repeated common.ItemBase reward_info = 2; // 奖励信息
    bool                 new_coin_record = 3; // 新金币记录
    int64                     enter_time = 4; // 进入钓场时间戳-秒
}

// 体力变化通知
message PlayerEenrgyChangeNtf {
    int32  cur_energy        = 1; // 当前体力
}

// 搏鱼请求
message FishBattleReq {
    int32              rig_id = 1; // 钓组id
    common.FISH_RESULT result = 2; // 搏鱼结果
}

// 搏鱼响应
message FishBattleRsp {
    common.Result         ret       = 1; // 结果
    int32                 rig_id    = 2; // 钓组id
    common.FISH_RESULT    result    = 3; // 搏鱼结果
    common.FishInfo       fish_info = 4; // 鱼详细信息(针对钓到鱼的情况)
}

// 切换竿组请求
message SwitchRodRigReq {
    int32 rig_id = 1; // 钓组id
}

// 切换竿组响应
message SwitchRodRigRsp {
    common.Result ret    = 1; // 结果
    int32         rig_id = 2; // 钓组id
}

// 用户体力消耗
message PlayerEnergyCostReq {
    int32  energy        = 1; // 消耗体力
}

// 用户体力消耗响应
message PlayerEnergyCostRsp {
    common.Result ret    = 1; // 结果
    int32  energy        = 2; // 消耗体力
}

// 钓鱼事件请求
message FishingEventReq {
    bool                      need_broadcast  = 1;  // 是否需要广播
    common.FishingEventInfo   event_info      = 2;  // 事件信息
}

// 钓鱼事件响应
message FishingEventRsp {
    common.Result           ret        = 1;  // 结果
    common.FishingEventInfo event_info = 2;  // 事件信息
}

// 钓鱼事件广播
message FishingEventBroadcastNtf {
    uint64                    sender_id       = 1;  // 发送者(如:祝贺者id)
    common.FishingEventInfo   event_info      = 2;  // 事件信息
}

// 中鱼开始请求
message HookStartReq {
    common.HOOK_FISH_CALC_TYPE calc_type  = 1;  // 中鱼计算类型
    int64                      pond_id    = 2;  // 钓场id
    int32                      rig_id     = 3;  // 钓组id
    common.HookBait            hook_bait  = 4;  // 钩饵组合
    common.HookHabitParam      hook_habit = 5;  // 中鱼习性参数
}

// 中鱼开始响应
message HookStartRsp {
    common.Result          ret          = 1; // 结果
    common.FishSyncControl sync_control = 2; // 请求同步控制
}

// 切线请求
message KillLineReq {
    int32           rig_id    = 1;  // 钓组id
    common.HookBait hook_bait = 2;  // 钩饵组合
}

// 切线响应
message KillLineRsp {
    common.Result   ret       = 1;  // 结果
    int32           rig_id    = 2;  // 钓组id
    common.HookBait hook_bait = 3;  // 钩饵组合
}