package google

import (
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
)

// 新账号
const (
	AuthorizationCode = "authorization_code"
	TokenURL          = "https://oauth2.googleapis.com/token"
	RedirectURI       = "https://localhost/oauth2callback"
)

const (
	googleClientId_1001     = "1042706838390-2tdjhdhrcall2rieio1nchfi1esg4epg.apps.googleusercontent.com"
	googleClientSecret_1001 = "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"
)
const (
	googleClientId_1002     = "1042706838390-2tdjhdhrcall2rieio1nchfi1esg4epg.apps.googleusercontent.com"
	googleClientSecret_1002 = "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"
)

type GoogleChannelKey struct {
	ClientId     string
	ClientSecret string
}

var GoogleChannelKeyMap = map[commonPB.CHANNEL_TYPE]GoogleChannelKey{
	commonPB.CHANNEL_TYPE_CT_MASTER: {
		ClientId:     googleClientId_1001,
		ClientSecret: googleClientSecret_1001,
	},
	commonPB.CHANNEL_TYPE_CT_GOOGLE: {
		ClientId:     googleClientId_1002,
		ClientSecret: googleClientSecret_1002,
	},
}
