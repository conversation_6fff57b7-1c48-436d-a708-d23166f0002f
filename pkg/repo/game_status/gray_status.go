package game_status

import (
	"context"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	"git.keepfancy.xyz/back-end/frameworks/lib/timex"
)

// makeupGrayValue 组合玩家信息到计算参数
func makeupGrayValue(playerId uint64, ip string, version string) map[commonPB.GRAY_STRATEGY]interface{} {
	// 返回灰度参数值
	GrayTagValueMap := map[commonPB.GRAY_STRATEGY]interface{}{
		commonPB.GRAY_STRATEGY_GS_LOCATION:    ip,
		commonPB.GRAY_STRATEGY_GS_TAIL_NUMBER: playerId,
		commonPB.GRAY_STRATEGY_GS_WHITE_LIST:  playerId,
		commonPB.GRAY_STRATEGY_GS_VERSION:     version,
	}

	return GrayTagValueMap
}

// CalcPlayerGrayTag 计算玩家灰度标签
func CalcPlayerGrayTag(ctx context.Context, playerId uint64, ip string, version string) int32 {
	entry := logx.NewLogEntry(ctx)

	// 根据配置匹配灰度标签
	grayConf := cmodel.GetGrayStrategy(consul_config.WithGrpcCtx(ctx))
	if grayConf == nil {
		entry.Errorf("gray conf is empty")
		return 0
	}

	if !grayConf.Enable {
		return 0
	}

	startTime := grayConf.StartTime
	endTime := grayConf.EndTime

	nowTime := timex.Now().Unix()
	if nowTime < startTime || nowTime > endTime {
		return 0
	}

	paramMap := makeupGrayValue(playerId, ip, version)
	if len(paramMap) == 0 {
		return 0
	}

	var grayType int32

	// 先校验白名单 白名单满足了就直接返回
	if grayConf.WhiteUidType != nil {
		grayCalcHandle := GetGrayTag(commonPB.GRAY_STRATEGY_GS_WHITE_LIST)
		if grayCalcHandle != nil {
			grayType = grayCalcHandle.CheckPlayerGrayType(ctx, paramMap[commonPB.GRAY_STRATEGY_GS_WHITE_LIST], grayConf)
			if grayType > 0 {
				entry.Debugf("playerId:%d, paramMap:%+v, in white list grayType:%d", playerId, paramMap, grayType)
				return grayType
			}
		}
	}

	// 开启列表中需要全部满足
	for _, v := range grayConf.OpenTypes {
		grayCalcHandle := GetGrayTag(commonPB.GRAY_STRATEGY(v))
		if grayCalcHandle != nil {
			grayType = grayCalcHandle.CheckPlayerGrayType(ctx, paramMap[commonPB.GRAY_STRATEGY(v)], grayConf)
			if grayType == 0 {
				return 0
			}
		}
	}

	entry.Debugf("playerId:%d, paramMap:%+v, grayType:%d", playerId, paramMap, grayType)

	return grayType
}

func CalculatePlayerGrayStatus(ctx context.Context, uid uint64, ip string, version string) commonPB.GRAY_STATUS {
	grayStatus := commonPB.GRAY_STATUS_GS_Normal

	grayType := CalcPlayerGrayTag(ctx, uid, ip, version)
	if grayType > 0 {
		grayStatus = commonPB.GRAY_STATUS_GS_Gray
	}

	return grayStatus

	// switch strategy.RuleType {
	// case int64(commonPB.GRAY_STRATEGY_GS_IP):
	// 	ipGrayArray := transform.StrSplit2Slice(strategy.IpList, dict.SysSymbolVerticalLine)

	// 	if gray_release.InIPList(ip, ipGrayArray) {
	// 		grayStatus = commonPB.GRAY_STATUS_GS_Gray
	// 	}
	// case int64(commonPB.GRAY_STRATEGY_GS_UID):
	// 	tailArray := transform.StrSplit2Slice(strategy.UidTail, dict.SysSymbolVerticalLine)
	// 	for _, s := range tailArray {
	// 		nTail := transform.Str2Int(s)
	// 		if gray_release.IsGrayByUserIDTail(uid, nTail) {
	// 			grayStatus = commonPB.GRAY_STATUS_GS_Gray
	// 			break
	// 		}
	// 	}
	// case int64(commonPB.GRAY_STRATEGY_GS_TAGS):
	// 	// TODO 根据用户标签
	// 	tailArray := transform.StrSplit2Slice(strategy.TagList, dict.SysSymbolVerticalLine)

	// 	if gray_release.InTagsList([]string{""}, tailArray) {
	// 		grayStatus = commonPB.GRAY_STATUS_GS_Gray
	// 	}
	// case int64(commonPB.GRAY_STRATEGY_GS_VERSION):
	// 	if gray_release.InGrayVersion(version, strategy.Version) {
	// 		grayStatus = commonPB.GRAY_STATUS_GS_Gray
	// 	}
	// }
	// return grayStatus
}
