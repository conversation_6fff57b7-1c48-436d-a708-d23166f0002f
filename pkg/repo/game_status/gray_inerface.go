package game_status

import (
	"context"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	"git.keepfancy.xyz/back-end/frameworks/kit/location"
	"git.keepfancy.xyz/back-end/frameworks/kit/version"
	"git.keepfancy.xyz/back-end/frameworks/lib/transform"
)

type IGrayTag interface {
	// 计算灰度标签值
	CheckPlayerGrayType(ctx context.Context, param interface{}, grayConf *cmodel.GrayStrategy) int32
}

// GetGrayTag 获取灰度标签计算器
func GetGrayTag(strategy commonPB.GRAY_STRATEGY) IGrayTag {
	switch strategy {
	case commonPB.GRAY_STRATEGY_GS_LOCATION:
		return &Location{}
	case commonPB.GRAY_STRATEGY_GS_TAIL_NUMBER:
		return &TailNumber{}
	case commonPB.GRAY_STRATEGY_GS_VERSION:
		return &Version{}
	case commonPB.GRAY_STRATEGY_GS_WHITE_LIST:
		return &WhiteList{}
	default:
		return nil
	}
}

// 位置灰度标签
type Location struct {
}

func (l *Location) CheckPlayerGrayType(ctx context.Context, param interface{}, grayConf *cmodel.GrayStrategy) int32 {
	if param == nil || grayConf == nil {
		return 0
	}

	ip, ok := param.(string)
	if !ok {
		return 0
	}

	// 查询地址位置配置
	locationConf := cmodel.GetAllLocationBlock(consul_config.WithGrpcCtx(ctx))
	if locationConf == nil {
		return 0
	}

	// 查询玩家位置信息
	locationInfo := location.GetLocationByIP(ip)
	if locationInfo == nil {
		return 0
	}

	for _, v := range locationConf {
		// 只考虑灰度配置
		if v.LocationType != int32(commonPB.LOCATION_TYPE_LT_GRAY) {
			continue
		}
		// 先校验国家是否匹配
		if v.Country == locationInfo.Country {
			// 如果配置了城市，则需要城市也匹配；否则只匹配国家即可
			if v.City == "" || v.City == locationInfo.City {
				return int32(commonPB.GRAY_STRATEGY_GS_LOCATION)
			}
		}
	}

	return 0
}

// 白名单灰度标签
type WhiteList struct {
}

func (w *WhiteList) CheckPlayerGrayType(ctx context.Context, param interface{}, grayConf *cmodel.GrayStrategy) int32 {
	if param == nil || grayConf == nil {
		return 0
	}

	playerId, ok := param.(uint64)
	if !ok {
		return 0
	}

	// 白名单配置
	whiteListConf := cmodel.GetAllWhiteList(consul_config.WithGrpcCtx(ctx))
	if len(whiteListConf) <= 0 {
		return 0
	}

	for _, v := range whiteListConf {
		if transform.Int32SliceContain(grayConf.WhiteUidType, v.WhiteType) && uint64(v.Uid) == playerId {
			return int32(commonPB.GRAY_STRATEGY_GS_WHITE_LIST)
		}
	}

	return 0
}

// 尾号灰度标签
type TailNumber struct {
}

func (t *TailNumber) CheckPlayerGrayType(ctx context.Context, param interface{}, grayConf *cmodel.GrayStrategy) int32 {
	if param == nil || grayConf == nil {
		return 0
	}

	playerId, ok := param.(uint64)
	if !ok {
		return 0
	}

	// 获取玩家id的最后一位
	tailNum := playerId % 10
	// 判断是否命中灰度配置
	if transform.Int32SliceContain(grayConf.UidTail, int32(tailNum)) {
		return int32(commonPB.GRAY_STRATEGY_GS_TAIL_NUMBER)
	}

	return 0
}

// 版本灰度标签(客户端版本)
type Version struct {
}

func (v *Version) CheckPlayerGrayType(ctx context.Context, param interface{}, grayConf *cmodel.GrayStrategy) int32 {
	if param == nil || grayConf == nil {
		return 0
	}

	ver, ok := param.(string)
	if !ok || ver == "" {
		return 0
	}

	// 判断是否大于等于配置版本号
	if version.Compare(ver, grayConf.Version) >= 0 {
		return int32(commonPB.GRAY_STRATEGY_GS_VERSION)
	}

	return 0
}