package item_kit

import commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"

// 计算item聚合

// IsSameItem 检查是否两个相同物品
func IsSameItem(check, cond *commonPB.Item) bool {
	if (check.ItemType == cond.ItemType || cond.ItemType == commonPB.ITEM_TYPE_IT_UNKNOWN) &&
		(cond.ItemId == 0 || check.ItemId == cond.ItemId) &&
		(cond.ItemExpireTime == 0 || check.ItemExpireTime == cond.ItemExpireTime) {
		// TODO: 检查拓展属性
		if cond.InstanceId == "" || cond.InstanceId == check.InstanceId {
			return true
		}
	}
	return false
}
