package analyze

import (
	"context"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/frameworks/kit/rpc/interceptor"
)

const (
	EventLogin            = "acc_login"          // 登录
	EventLogout           = "acc_logout"         // 登出
	EventRegister         = "acc_register"       // 注册
	EventItem             = "acc_item"           // 道具
	EventActClaimReward   = "act_claim_reward"   // 活动领取
	EventEnterFishingRoom = "enter_fishing_room" // 进入钓场
	EventExitFishingRoom  = "exit_fishing_room"  // 退出钓场
)

type DefaultHeader struct {
	ProductID   commonPB.PRODUCT_ID    `json:"product_id"`   // 产品id
	ChannelType commonPB.CHANNEL_TYPE  `json:"channel_type"` // 渠道
	Platform    commonPB.PLATFORM_TYPE `json:"platform"`     // 平台
	AppLanguage commonPB.LANGUAGE_TYPE `json:"app_language"` // 语言
	Country     string                 `json:"country"`      // 国家
	AccType     commonPB.ACC_TYPE      `json:"acc_type"`     // 账号类型
	AppVersion  string                 `json:"app_version"`  // 游戏版本
	PlayerId    uint64                 `json:"player_id"`    // 玩家 ID
	IP          string                 `json:"ip"`           // 玩家ip
}

type TeInfo interface {
	GetEvent() string
}

// NewTeDefaultHeaderFromCtx 从ctx中获取header
func NewTeDefaultHeaderFromCtx(ctx context.Context) *DefaultHeader {

	// 从rpc ctx取出header
	options := interceptor.GetRPCOptions(ctx)

	return &DefaultHeader{
		ProductID:   commonPB.PRODUCT_ID(options.ProductId),
		ChannelType: commonPB.CHANNEL_TYPE(options.ChannelType),
		Platform:    commonPB.PLATFORM_TYPE(options.Platform),
		AppLanguage: commonPB.LANGUAGE_TYPE(options.AppLanguage),
		Country:     options.Country,
		AccType:     commonPB.ACC_TYPE(options.AccType),
		AppVersion:  options.AppVersion,
		PlayerId:    options.PlayerId,
		// IP:          options.,
	}
}
