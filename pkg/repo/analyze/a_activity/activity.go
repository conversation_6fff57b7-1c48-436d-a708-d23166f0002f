package a_activity

import (
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/analyze"
	"time"
)

func (te *TeActClaimRewardInfo) GetEvent() string {
	return analyze.EventActClaimReward
}

type TeActClaimRewardInfo struct {
	*analyze.DefaultHeader
	*TeActClaimRewardDetail
}

type TeActClaimRewardDetail struct {
	ActivityId     int64           `json:"activity_id"`     // 活动ID
	CycleId        int32           `json:"cycle_id"`        // 周期ID
	ClaimedStages  []int32         `json:"claimed_stages"`  // 本次领取的阶段列表
	CurrentMetrics map[int32]int64 `json:"current_metrics"` // 当前指标值
	ClaimType      int32           `json:"claim_type"`      // 领取类型: 0手动，1邮件
	OperateTime    time.Time       `json:"operate_time"`    // 操作时间
}
