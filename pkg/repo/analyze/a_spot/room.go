package a_spot

import (
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/analyze"
	"time"
)

// GetEvent 获取事件名称 - 进入钓场事件
func (te *TeFishingRoomEnterInfo) GetEvent() string {
	return analyze.EventEnterFishingRoom
}

// TeFishingRoomEnterInfo 进入钓场事件信息
type TeFishingRoomEnterInfo struct {
	*analyze.DefaultHeader
	*TeFishingRoom
}

// GetEvent 获取事件名称 - 退出钓场事件
func (te *TeFishingRoomExitInfo) GetEvent() string {
	return analyze.EventExitFishingRoom
}

// TeFishingRoomExitInfo 退出钓场事件信息
type TeFishingRoomExitInfo struct {
	*analyze.DefaultHeader
	*TeFishingRoom
}

// TeFishingRoom 钓场信息结构
type TeFishingRoom struct {
	OperateTime time.Time `json:"operate_time"` // 操作时间
}
