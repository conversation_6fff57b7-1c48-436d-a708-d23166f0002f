// 钓点协议

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v5.27.0
// source: spot.proto

package spotPB

import (
	common "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 请求钓点场景信息
type GetSpotSceneReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PondId int64  `protobuf:"varint,1,opt,name=pond_id,json=pondId,proto3" json:"pond_id,omitempty"` // 钓场id
	RoomId string `protobuf:"bytes,2,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`  // 房间号
	SpotId int32  `protobuf:"varint,3,opt,name=spot_id,json=spotId,proto3" json:"spot_id,omitempty"` // 钓点id
}

func (x *GetSpotSceneReq) Reset() {
	*x = GetSpotSceneReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_spot_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSpotSceneReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSpotSceneReq) ProtoMessage() {}

func (x *GetSpotSceneReq) ProtoReflect() protoreflect.Message {
	mi := &file_spot_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSpotSceneReq.ProtoReflect.Descriptor instead.
func (*GetSpotSceneReq) Descriptor() ([]byte, []int) {
	return file_spot_proto_rawDescGZIP(), []int{0}
}

func (x *GetSpotSceneReq) GetPondId() int64 {
	if x != nil {
		return x.PondId
	}
	return 0
}

func (x *GetSpotSceneReq) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *GetSpotSceneReq) GetSpotId() int32 {
	if x != nil {
		return x.SpotId
	}
	return 0
}

// 请求钓点场景信息响应
type GetSpotSceneRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret      *common.Result           `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`                           // 结果
	PondId   int64                    `protobuf:"varint,2,opt,name=pond_id,json=pondId,proto3" json:"pond_id,omitempty"`      // 钓场id
	SpotId   int32                    `protobuf:"varint,3,opt,name=spot_id,json=spotId,proto3" json:"spot_id,omitempty"`      // 钓点id
	SyncList []*common.FisherSpotSync `protobuf:"bytes,4,rep,name=sync_list,json=syncList,proto3" json:"sync_list,omitempty"` // 玩家同步信息列表
	RigId    int32                    `protobuf:"varint,5,opt,name=rig_id,json=rigId,proto3" json:"rig_id,omitempty"`         // 钓组id
	Energy   int32                    `protobuf:"varint,6,opt,name=energy,proto3" json:"energy,omitempty"`                    // 体力
}

func (x *GetSpotSceneRsp) Reset() {
	*x = GetSpotSceneRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_spot_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSpotSceneRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSpotSceneRsp) ProtoMessage() {}

func (x *GetSpotSceneRsp) ProtoReflect() protoreflect.Message {
	mi := &file_spot_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSpotSceneRsp.ProtoReflect.Descriptor instead.
func (*GetSpotSceneRsp) Descriptor() ([]byte, []int) {
	return file_spot_proto_rawDescGZIP(), []int{1}
}

func (x *GetSpotSceneRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *GetSpotSceneRsp) GetPondId() int64 {
	if x != nil {
		return x.PondId
	}
	return 0
}

func (x *GetSpotSceneRsp) GetSpotId() int32 {
	if x != nil {
		return x.SpotId
	}
	return 0
}

func (x *GetSpotSceneRsp) GetSyncList() []*common.FisherSpotSync {
	if x != nil {
		return x.SyncList
	}
	return nil
}

func (x *GetSpotSceneRsp) GetRigId() int32 {
	if x != nil {
		return x.RigId
	}
	return 0
}

func (x *GetSpotSceneRsp) GetEnergy() int32 {
	if x != nil {
		return x.Energy
	}
	return 0
}

// 玩家进房广播
type EnterRoomBroadcastNtf struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerInfo *common.FisheryPlayerInfo `protobuf:"bytes,1,opt,name=player_info,json=playerInfo,proto3" json:"player_info,omitempty"` // 玩家信息
}

func (x *EnterRoomBroadcastNtf) Reset() {
	*x = EnterRoomBroadcastNtf{}
	if protoimpl.UnsafeEnabled {
		mi := &file_spot_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EnterRoomBroadcastNtf) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnterRoomBroadcastNtf) ProtoMessage() {}

func (x *EnterRoomBroadcastNtf) ProtoReflect() protoreflect.Message {
	mi := &file_spot_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnterRoomBroadcastNtf.ProtoReflect.Descriptor instead.
func (*EnterRoomBroadcastNtf) Descriptor() ([]byte, []int) {
	return file_spot_proto_rawDescGZIP(), []int{2}
}

func (x *EnterRoomBroadcastNtf) GetPlayerInfo() *common.FisheryPlayerInfo {
	if x != nil {
		return x.PlayerInfo
	}
	return nil
}

// 选择进入钓点请求
type ChooseSpotReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SpotId int32 `protobuf:"varint,1,opt,name=spot_id,json=spotId,proto3" json:"spot_id,omitempty"` // 钓点id
}

func (x *ChooseSpotReq) Reset() {
	*x = ChooseSpotReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_spot_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChooseSpotReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChooseSpotReq) ProtoMessage() {}

func (x *ChooseSpotReq) ProtoReflect() protoreflect.Message {
	mi := &file_spot_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChooseSpotReq.ProtoReflect.Descriptor instead.
func (*ChooseSpotReq) Descriptor() ([]byte, []int) {
	return file_spot_proto_rawDescGZIP(), []int{3}
}

func (x *ChooseSpotReq) GetSpotId() int32 {
	if x != nil {
		return x.SpotId
	}
	return 0
}

// 选择进入钓点响应
type ChooseSpotRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret    *common.Result `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`                      // 结果
	SpotId int32          `protobuf:"varint,2,opt,name=spot_id,json=spotId,proto3" json:"spot_id,omitempty"` // 钓点id
}

func (x *ChooseSpotRsp) Reset() {
	*x = ChooseSpotRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_spot_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChooseSpotRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChooseSpotRsp) ProtoMessage() {}

func (x *ChooseSpotRsp) ProtoReflect() protoreflect.Message {
	mi := &file_spot_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChooseSpotRsp.ProtoReflect.Descriptor instead.
func (*ChooseSpotRsp) Descriptor() ([]byte, []int) {
	return file_spot_proto_rawDescGZIP(), []int{4}
}

func (x *ChooseSpotRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *ChooseSpotRsp) GetSpotId() int32 {
	if x != nil {
		return x.SpotId
	}
	return 0
}

// 同步钓点信息
type SyncSpotInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SyncInfo *common.FisherSpotSync `protobuf:"bytes,1,opt,name=sync_info,json=syncInfo,proto3" json:"sync_info,omitempty"` // 同步信息
}

func (x *SyncSpotInfoReq) Reset() {
	*x = SyncSpotInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_spot_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncSpotInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncSpotInfoReq) ProtoMessage() {}

func (x *SyncSpotInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_spot_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncSpotInfoReq.ProtoReflect.Descriptor instead.
func (*SyncSpotInfoReq) Descriptor() ([]byte, []int) {
	return file_spot_proto_rawDescGZIP(), []int{5}
}

func (x *SyncSpotInfoReq) GetSyncInfo() *common.FisherSpotSync {
	if x != nil {
		return x.SyncInfo
	}
	return nil
}

// 同步钓点信息广播
type SyncSpotInfoBroadcastNtf struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SyncInfo *common.FisherSpotSync `protobuf:"bytes,1,opt,name=sync_info,json=syncInfo,proto3" json:"sync_info,omitempty"` // 同步信息
}

func (x *SyncSpotInfoBroadcastNtf) Reset() {
	*x = SyncSpotInfoBroadcastNtf{}
	if protoimpl.UnsafeEnabled {
		mi := &file_spot_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncSpotInfoBroadcastNtf) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncSpotInfoBroadcastNtf) ProtoMessage() {}

func (x *SyncSpotInfoBroadcastNtf) ProtoReflect() protoreflect.Message {
	mi := &file_spot_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncSpotInfoBroadcastNtf.ProtoReflect.Descriptor instead.
func (*SyncSpotInfoBroadcastNtf) Descriptor() ([]byte, []int) {
	return file_spot_proto_rawDescGZIP(), []int{6}
}

func (x *SyncSpotInfoBroadcastNtf) GetSyncInfo() *common.FisherSpotSync {
	if x != nil {
		return x.SyncInfo
	}
	return nil
}

// 抛竿请求
type ThrowRodReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PondId    int64                  `protobuf:"varint,1,opt,name=pond_id,json=pondId,proto3" json:"pond_id,omitempty"`         // 钓场id
	RigId     int32                  `protobuf:"varint,2,opt,name=rig_id,json=rigId,proto3" json:"rig_id,omitempty"`            // 钓组id
	HookBait  *common.HookBait       `protobuf:"bytes,3,opt,name=hook_bait,json=hookBait,proto3" json:"hook_bait,omitempty"`    // 钩饵组合
	GridInfo  *common.ThrowGridInfo  `protobuf:"bytes,4,opt,name=grid_info,json=gridInfo,proto3" json:"grid_info,omitempty"`    // 抛竿信息
	HookHabit *common.HookHabitParam `protobuf:"bytes,5,opt,name=hook_habit,json=hookHabit,proto3" json:"hook_habit,omitempty"` // 中鱼习性参数
}

func (x *ThrowRodReq) Reset() {
	*x = ThrowRodReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_spot_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ThrowRodReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThrowRodReq) ProtoMessage() {}

func (x *ThrowRodReq) ProtoReflect() protoreflect.Message {
	mi := &file_spot_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThrowRodReq.ProtoReflect.Descriptor instead.
func (*ThrowRodReq) Descriptor() ([]byte, []int) {
	return file_spot_proto_rawDescGZIP(), []int{7}
}

func (x *ThrowRodReq) GetPondId() int64 {
	if x != nil {
		return x.PondId
	}
	return 0
}

func (x *ThrowRodReq) GetRigId() int32 {
	if x != nil {
		return x.RigId
	}
	return 0
}

func (x *ThrowRodReq) GetHookBait() *common.HookBait {
	if x != nil {
		return x.HookBait
	}
	return nil
}

func (x *ThrowRodReq) GetGridInfo() *common.ThrowGridInfo {
	if x != nil {
		return x.GridInfo
	}
	return nil
}

func (x *ThrowRodReq) GetHookHabit() *common.HookHabitParam {
	if x != nil {
		return x.HookHabit
	}
	return nil
}

// 抛竿响应
type ThrowRodRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret         *common.Result          `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`                                    // 结果
	PondId      int64                   `protobuf:"varint,2,opt,name=pond_id,json=pondId,proto3" json:"pond_id,omitempty"`               // 钓场id
	RigId       int32                   `protobuf:"varint,3,opt,name=rig_id,json=rigId,proto3" json:"rig_id,omitempty"`                  // 钓组id
	HookBait    *common.HookBait        `protobuf:"bytes,4,opt,name=hook_bait,json=hookBait,proto3" json:"hook_bait,omitempty"`          // 钩饵组合
	SyncControl *common.FishSyncControl `protobuf:"bytes,5,opt,name=sync_control,json=syncControl,proto3" json:"sync_control,omitempty"` // 请求同步控制
}

func (x *ThrowRodRsp) Reset() {
	*x = ThrowRodRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_spot_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ThrowRodRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThrowRodRsp) ProtoMessage() {}

func (x *ThrowRodRsp) ProtoReflect() protoreflect.Message {
	mi := &file_spot_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThrowRodRsp.ProtoReflect.Descriptor instead.
func (*ThrowRodRsp) Descriptor() ([]byte, []int) {
	return file_spot_proto_rawDescGZIP(), []int{8}
}

func (x *ThrowRodRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *ThrowRodRsp) GetPondId() int64 {
	if x != nil {
		return x.PondId
	}
	return 0
}

func (x *ThrowRodRsp) GetRigId() int32 {
	if x != nil {
		return x.RigId
	}
	return 0
}

func (x *ThrowRodRsp) GetHookBait() *common.HookBait {
	if x != nil {
		return x.HookBait
	}
	return nil
}

func (x *ThrowRodRsp) GetSyncControl() *common.FishSyncControl {
	if x != nil {
		return x.SyncControl
	}
	return nil
}

// 收竿请求
type CatchRodReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PondId          int64                   `protobuf:"varint,1,opt,name=pond_id,json=pondId,proto3" json:"pond_id,omitempty"`                             // 钓场id
	RigId           int32                   `protobuf:"varint,2,opt,name=rig_id,json=rigId,proto3" json:"rig_id,omitempty"`                                // 钓组id
	HookHabit       *common.HookHabitParam  `protobuf:"bytes,3,opt,name=hook_habit,json=hookHabit,proto3" json:"hook_habit,omitempty"`                     // 中鱼习性参数
	FishDamagedInfo *common.FishDamagedInfo `protobuf:"bytes,5,opt,name=fish_damaged_info,json=fishDamagedInfo,proto3" json:"fish_damaged_info,omitempty"` // 鱼受损信息
}

func (x *CatchRodReq) Reset() {
	*x = CatchRodReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_spot_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CatchRodReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CatchRodReq) ProtoMessage() {}

func (x *CatchRodReq) ProtoReflect() protoreflect.Message {
	mi := &file_spot_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CatchRodReq.ProtoReflect.Descriptor instead.
func (*CatchRodReq) Descriptor() ([]byte, []int) {
	return file_spot_proto_rawDescGZIP(), []int{9}
}

func (x *CatchRodReq) GetPondId() int64 {
	if x != nil {
		return x.PondId
	}
	return 0
}

func (x *CatchRodReq) GetRigId() int32 {
	if x != nil {
		return x.RigId
	}
	return 0
}

func (x *CatchRodReq) GetHookHabit() *common.HookHabitParam {
	if x != nil {
		return x.HookHabit
	}
	return nil
}

func (x *CatchRodReq) GetFishDamagedInfo() *common.FishDamagedInfo {
	if x != nil {
		return x.FishDamagedInfo
	}
	return nil
}

// 收竿返回
type CatchRodRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PondId     int64                  `protobuf:"varint,1,opt,name=pond_id,json=pondId,proto3" json:"pond_id,omitempty"`                                     // 钓场id
	RigId      int32                  `protobuf:"varint,2,opt,name=rig_id,json=rigId,proto3" json:"rig_id,omitempty"`                                        // 钓组id
	FishResult common.FISH_RESULT     `protobuf:"varint,3,opt,name=fish_result,json=fishResult,proto3,enum=common.FISH_RESULT" json:"fish_result,omitempty"` // 鱼结果
	FishInfo   *common.FishDetailInfo `protobuf:"bytes,4,opt,name=fish_info,json=fishInfo,proto3" json:"fish_info,omitempty"`                                // 鱼详细信息
	NewRecord  bool                   `protobuf:"varint,5,opt,name=new_record,json=newRecord,proto3" json:"new_record,omitempty"`                            // 是否是新记录
}

func (x *CatchRodRsp) Reset() {
	*x = CatchRodRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_spot_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CatchRodRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CatchRodRsp) ProtoMessage() {}

func (x *CatchRodRsp) ProtoReflect() protoreflect.Message {
	mi := &file_spot_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CatchRodRsp.ProtoReflect.Descriptor instead.
func (*CatchRodRsp) Descriptor() ([]byte, []int) {
	return file_spot_proto_rawDescGZIP(), []int{10}
}

func (x *CatchRodRsp) GetPondId() int64 {
	if x != nil {
		return x.PondId
	}
	return 0
}

func (x *CatchRodRsp) GetRigId() int32 {
	if x != nil {
		return x.RigId
	}
	return 0
}

func (x *CatchRodRsp) GetFishResult() common.FISH_RESULT {
	if x != nil {
		return x.FishResult
	}
	return common.FISH_RESULT(0)
}

func (x *CatchRodRsp) GetFishInfo() *common.FishDetailInfo {
	if x != nil {
		return x.FishInfo
	}
	return nil
}

func (x *CatchRodRsp) GetNewRecord() bool {
	if x != nil {
		return x.NewRecord
	}
	return false
}

// 中鱼请求
type FishHookReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PondId    int64                  `protobuf:"varint,1,opt,name=pond_id,json=pondId,proto3" json:"pond_id,omitempty"`         // 钓场id
	RigId     int32                  `protobuf:"varint,2,opt,name=rig_id,json=rigId,proto3" json:"rig_id,omitempty"`            // 钓组id
	GridInfo  *common.ThrowGridInfo  `protobuf:"bytes,4,opt,name=grid_info,json=gridInfo,proto3" json:"grid_info,omitempty"`    // 抛竿信息
	HookHabit *common.HookHabitParam `protobuf:"bytes,5,opt,name=hook_habit,json=hookHabit,proto3" json:"hook_habit,omitempty"` // 中鱼习性参数
}

func (x *FishHookReq) Reset() {
	*x = FishHookReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_spot_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FishHookReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FishHookReq) ProtoMessage() {}

func (x *FishHookReq) ProtoReflect() protoreflect.Message {
	mi := &file_spot_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FishHookReq.ProtoReflect.Descriptor instead.
func (*FishHookReq) Descriptor() ([]byte, []int) {
	return file_spot_proto_rawDescGZIP(), []int{11}
}

func (x *FishHookReq) GetPondId() int64 {
	if x != nil {
		return x.PondId
	}
	return 0
}

func (x *FishHookReq) GetRigId() int32 {
	if x != nil {
		return x.RigId
	}
	return 0
}

func (x *FishHookReq) GetGridInfo() *common.ThrowGridInfo {
	if x != nil {
		return x.GridInfo
	}
	return nil
}

func (x *FishHookReq) GetHookHabit() *common.HookHabitParam {
	if x != nil {
		return x.HookHabit
	}
	return nil
}

// 中鱼响应
type FishHookRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PondId      int64                 `protobuf:"varint,1,opt,name=pond_id,json=pondId,proto3" json:"pond_id,omitempty"`                  // 钓场id
	RigId       int32                 `protobuf:"varint,2,opt,name=rig_id,json=rigId,proto3" json:"rig_id,omitempty"`                     // 钓组id
	GridInfo    *common.ThrowGridInfo `protobuf:"bytes,3,opt,name=grid_info,json=gridInfo,proto3" json:"grid_info,omitempty"`             // 抛竿信息
	NextReqTime int64                 `protobuf:"varint,4,opt,name=next_req_time,json=nextReqTime,proto3" json:"next_req_time,omitempty"` // 下次请求时间(毫秒)
	FakeFishId  int64                 `protobuf:"varint,5,opt,name=fake_fish_id,json=fakeFishId,proto3" json:"fake_fish_id,omitempty"`    // 假咬口鱼id
	FishInfo    *common.FishInfo      `protobuf:"bytes,6,opt,name=fish_info,json=fishInfo,proto3" json:"fish_info,omitempty"`             // 中鱼信息
}

func (x *FishHookRsp) Reset() {
	*x = FishHookRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_spot_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FishHookRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FishHookRsp) ProtoMessage() {}

func (x *FishHookRsp) ProtoReflect() protoreflect.Message {
	mi := &file_spot_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FishHookRsp.ProtoReflect.Descriptor instead.
func (*FishHookRsp) Descriptor() ([]byte, []int) {
	return file_spot_proto_rawDescGZIP(), []int{12}
}

func (x *FishHookRsp) GetPondId() int64 {
	if x != nil {
		return x.PondId
	}
	return 0
}

func (x *FishHookRsp) GetRigId() int32 {
	if x != nil {
		return x.RigId
	}
	return 0
}

func (x *FishHookRsp) GetGridInfo() *common.ThrowGridInfo {
	if x != nil {
		return x.GridInfo
	}
	return nil
}

func (x *FishHookRsp) GetNextReqTime() int64 {
	if x != nil {
		return x.NextReqTime
	}
	return 0
}

func (x *FishHookRsp) GetFakeFishId() int64 {
	if x != nil {
		return x.FakeFishId
	}
	return 0
}

func (x *FishHookRsp) GetFishInfo() *common.FishInfo {
	if x != nil {
		return x.FishInfo
	}
	return nil
}

// 鱼入护操作请求
type FishEntryOptReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FishInstance string                     `protobuf:"bytes,1,opt,name=fish_instance,json=fishInstance,proto3" json:"fish_instance,omitempty"`  // 鱼实例id
	Action       common.FISH_ENTRY_OPT_TYPE `protobuf:"varint,2,opt,name=action,proto3,enum=common.FISH_ENTRY_OPT_TYPE" json:"action,omitempty"` // 操作类型
}

func (x *FishEntryOptReq) Reset() {
	*x = FishEntryOptReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_spot_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FishEntryOptReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FishEntryOptReq) ProtoMessage() {}

func (x *FishEntryOptReq) ProtoReflect() protoreflect.Message {
	mi := &file_spot_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FishEntryOptReq.ProtoReflect.Descriptor instead.
func (*FishEntryOptReq) Descriptor() ([]byte, []int) {
	return file_spot_proto_rawDescGZIP(), []int{13}
}

func (x *FishEntryOptReq) GetFishInstance() string {
	if x != nil {
		return x.FishInstance
	}
	return ""
}

func (x *FishEntryOptReq) GetAction() common.FISH_ENTRY_OPT_TYPE {
	if x != nil {
		return x.Action
	}
	return common.FISH_ENTRY_OPT_TYPE(0)
}

// 鱼入护操作响应
type FishEntryOptRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret          *common.Result             `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`                                        // 结果
	FishInstance string                     `protobuf:"bytes,2,opt,name=fish_instance,json=fishInstance,proto3" json:"fish_instance,omitempty"`  // 鱼实例id
	Action       common.FISH_ENTRY_OPT_TYPE `protobuf:"varint,3,opt,name=action,proto3,enum=common.FISH_ENTRY_OPT_TYPE" json:"action,omitempty"` // 操作类型
	FishWeight   int32                      `protobuf:"varint,4,opt,name=fish_weight,json=fishWeight,proto3" json:"fish_weight,omitempty"`       // 总重量(鱼护中的鱼总重量g)
}

func (x *FishEntryOptRsp) Reset() {
	*x = FishEntryOptRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_spot_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FishEntryOptRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FishEntryOptRsp) ProtoMessage() {}

func (x *FishEntryOptRsp) ProtoReflect() protoreflect.Message {
	mi := &file_spot_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FishEntryOptRsp.ProtoReflect.Descriptor instead.
func (*FishEntryOptRsp) Descriptor() ([]byte, []int) {
	return file_spot_proto_rawDescGZIP(), []int{14}
}

func (x *FishEntryOptRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *FishEntryOptRsp) GetFishInstance() string {
	if x != nil {
		return x.FishInstance
	}
	return ""
}

func (x *FishEntryOptRsp) GetAction() common.FISH_ENTRY_OPT_TYPE {
	if x != nil {
		return x.Action
	}
	return common.FISH_ENTRY_OPT_TYPE(0)
}

func (x *FishEntryOptRsp) GetFishWeight() int32 {
	if x != nil {
		return x.FishWeight
	}
	return 0
}

// 玩家鱼入护广播
type PlayerFishEntryBroadcastNtf struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerId      uint64 `protobuf:"varint,1,opt,name=player_id,json=playerId,proto3" json:"player_id,omitempty"`                // 玩家id
	FishWeight    int32  `protobuf:"varint,2,opt,name=fish_weight,json=fishWeight,proto3" json:"fish_weight,omitempty"`          // 当前入护鱼重量(g)
	KeepnetWeight int32  `protobuf:"varint,3,opt,name=keepnet_weight,json=keepnetWeight,proto3" json:"keepnet_weight,omitempty"` // 鱼护总重量(g)
}

func (x *PlayerFishEntryBroadcastNtf) Reset() {
	*x = PlayerFishEntryBroadcastNtf{}
	if protoimpl.UnsafeEnabled {
		mi := &file_spot_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlayerFishEntryBroadcastNtf) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlayerFishEntryBroadcastNtf) ProtoMessage() {}

func (x *PlayerFishEntryBroadcastNtf) ProtoReflect() protoreflect.Message {
	mi := &file_spot_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlayerFishEntryBroadcastNtf.ProtoReflect.Descriptor instead.
func (*PlayerFishEntryBroadcastNtf) Descriptor() ([]byte, []int) {
	return file_spot_proto_rawDescGZIP(), []int{15}
}

func (x *PlayerFishEntryBroadcastNtf) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

func (x *PlayerFishEntryBroadcastNtf) GetFishWeight() int32 {
	if x != nil {
		return x.FishWeight
	}
	return 0
}

func (x *PlayerFishEntryBroadcastNtf) GetKeepnetWeight() int32 {
	if x != nil {
		return x.KeepnetWeight
	}
	return 0
}

// 鱼护操作请求
type FishKeepnetOptReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PondId       int64                        `protobuf:"varint,1,opt,name=pond_id,json=pondId,proto3" json:"pond_id,omitempty"`                     // 钓场id
	FishInstance string                       `protobuf:"bytes,2,opt,name=fish_instance,json=fishInstance,proto3" json:"fish_instance,omitempty"`    // 鱼实例id
	Action       common.FISH_KEEPNET_OPT_TYPE `protobuf:"varint,3,opt,name=action,proto3,enum=common.FISH_KEEPNET_OPT_TYPE" json:"action,omitempty"` // 操作类型
}

func (x *FishKeepnetOptReq) Reset() {
	*x = FishKeepnetOptReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_spot_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FishKeepnetOptReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FishKeepnetOptReq) ProtoMessage() {}

func (x *FishKeepnetOptReq) ProtoReflect() protoreflect.Message {
	mi := &file_spot_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FishKeepnetOptReq.ProtoReflect.Descriptor instead.
func (*FishKeepnetOptReq) Descriptor() ([]byte, []int) {
	return file_spot_proto_rawDescGZIP(), []int{16}
}

func (x *FishKeepnetOptReq) GetPondId() int64 {
	if x != nil {
		return x.PondId
	}
	return 0
}

func (x *FishKeepnetOptReq) GetFishInstance() string {
	if x != nil {
		return x.FishInstance
	}
	return ""
}

func (x *FishKeepnetOptReq) GetAction() common.FISH_KEEPNET_OPT_TYPE {
	if x != nil {
		return x.Action
	}
	return common.FISH_KEEPNET_OPT_TYPE(0)
}

// 鱼护操作响应
type FishKeepnetOptRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret          *common.Result               `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`                                          // 结果
	FishInstance string                       `protobuf:"bytes,2,opt,name=fish_instance,json=fishInstance,proto3" json:"fish_instance,omitempty"`    // 鱼实例id
	Action       common.FISH_KEEPNET_OPT_TYPE `protobuf:"varint,3,opt,name=action,proto3,enum=common.FISH_KEEPNET_OPT_TYPE" json:"action,omitempty"` // 操作类型
	FishWeight   int32                        `protobuf:"varint,4,opt,name=fish_weight,json=fishWeight,proto3" json:"fish_weight,omitempty"`         // 总重量(鱼护中的鱼总重量)
}

func (x *FishKeepnetOptRsp) Reset() {
	*x = FishKeepnetOptRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_spot_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FishKeepnetOptRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FishKeepnetOptRsp) ProtoMessage() {}

func (x *FishKeepnetOptRsp) ProtoReflect() protoreflect.Message {
	mi := &file_spot_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FishKeepnetOptRsp.ProtoReflect.Descriptor instead.
func (*FishKeepnetOptRsp) Descriptor() ([]byte, []int) {
	return file_spot_proto_rawDescGZIP(), []int{17}
}

func (x *FishKeepnetOptRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *FishKeepnetOptRsp) GetFishInstance() string {
	if x != nil {
		return x.FishInstance
	}
	return ""
}

func (x *FishKeepnetOptRsp) GetAction() common.FISH_KEEPNET_OPT_TYPE {
	if x != nil {
		return x.Action
	}
	return common.FISH_KEEPNET_OPT_TYPE(0)
}

func (x *FishKeepnetOptRsp) GetFishWeight() int32 {
	if x != nil {
		return x.FishWeight
	}
	return 0
}

// 鱼护中鱼详细信息查询
type KeepnetFishInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *KeepnetFishInfoReq) Reset() {
	*x = KeepnetFishInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_spot_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KeepnetFishInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KeepnetFishInfoReq) ProtoMessage() {}

func (x *KeepnetFishInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_spot_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KeepnetFishInfoReq.ProtoReflect.Descriptor instead.
func (*KeepnetFishInfoReq) Descriptor() ([]byte, []int) {
	return file_spot_proto_rawDescGZIP(), []int{18}
}

// 鱼护中鱼详细信息响应
type KeepnetFishInfoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FishInfo []*common.FishDetailInfo `protobuf:"bytes,1,rep,name=fish_info,json=fishInfo,proto3" json:"fish_info,omitempty"` // 鱼信息
}

func (x *KeepnetFishInfoRsp) Reset() {
	*x = KeepnetFishInfoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_spot_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KeepnetFishInfoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KeepnetFishInfoRsp) ProtoMessage() {}

func (x *KeepnetFishInfoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_spot_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KeepnetFishInfoRsp.ProtoReflect.Descriptor instead.
func (*KeepnetFishInfoRsp) Descriptor() ([]byte, []int) {
	return file_spot_proto_rawDescGZIP(), []int{19}
}

func (x *KeepnetFishInfoRsp) GetFishInfo() []*common.FishDetailInfo {
	if x != nil {
		return x.FishInfo
	}
	return nil
}

// 查询房间所有玩家鱼护信息
type GetRoomAllPlayerInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetRoomAllPlayerInfoReq) Reset() {
	*x = GetRoomAllPlayerInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_spot_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRoomAllPlayerInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRoomAllPlayerInfoReq) ProtoMessage() {}

func (x *GetRoomAllPlayerInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_spot_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRoomAllPlayerInfoReq.ProtoReflect.Descriptor instead.
func (*GetRoomAllPlayerInfoReq) Descriptor() ([]byte, []int) {
	return file_spot_proto_rawDescGZIP(), []int{20}
}

// 查询房间所有玩家鱼护信息响应
type GetRoomAllPlayerInfoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerInfo []*common.FisheryPlayerInfo `protobuf:"bytes,1,rep,name=player_info,json=playerInfo,proto3" json:"player_info,omitempty"` // 玩家信息
}

func (x *GetRoomAllPlayerInfoRsp) Reset() {
	*x = GetRoomAllPlayerInfoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_spot_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRoomAllPlayerInfoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRoomAllPlayerInfoRsp) ProtoMessage() {}

func (x *GetRoomAllPlayerInfoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_spot_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRoomAllPlayerInfoRsp.ProtoReflect.Descriptor instead.
func (*GetRoomAllPlayerInfoRsp) Descriptor() ([]byte, []int) {
	return file_spot_proto_rawDescGZIP(), []int{21}
}

func (x *GetRoomAllPlayerInfoRsp) GetPlayerInfo() []*common.FisheryPlayerInfo {
	if x != nil {
		return x.PlayerInfo
	}
	return nil
}

// 退出房间
type ExitRoomReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PondId int64  `protobuf:"varint,1,opt,name=pond_id,json=pondId,proto3" json:"pond_id,omitempty"` // 钓场id
	RoomId string `protobuf:"bytes,2,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`  // 房间id
}

func (x *ExitRoomReq) Reset() {
	*x = ExitRoomReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_spot_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExitRoomReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExitRoomReq) ProtoMessage() {}

func (x *ExitRoomReq) ProtoReflect() protoreflect.Message {
	mi := &file_spot_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExitRoomReq.ProtoReflect.Descriptor instead.
func (*ExitRoomReq) Descriptor() ([]byte, []int) {
	return file_spot_proto_rawDescGZIP(), []int{22}
}

func (x *ExitRoomReq) GetPondId() int64 {
	if x != nil {
		return x.PondId
	}
	return 0
}

func (x *ExitRoomReq) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

// 退出房响应
type ExitRoomRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret        *common.Result      `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`               // 结果
	SettleInfo *ExitRoomSettleInfo `protobuf:"bytes,2,opt,name=SettleInfo,proto3" json:"SettleInfo,omitempty"` // 结算信息
}

func (x *ExitRoomRsp) Reset() {
	*x = ExitRoomRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_spot_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExitRoomRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExitRoomRsp) ProtoMessage() {}

func (x *ExitRoomRsp) ProtoReflect() protoreflect.Message {
	mi := &file_spot_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExitRoomRsp.ProtoReflect.Descriptor instead.
func (*ExitRoomRsp) Descriptor() ([]byte, []int) {
	return file_spot_proto_rawDescGZIP(), []int{23}
}

func (x *ExitRoomRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *ExitRoomRsp) GetSettleInfo() *ExitRoomSettleInfo {
	if x != nil {
		return x.SettleInfo
	}
	return nil
}

// 退出房间广播
type ExitRoomBroadcastNtf struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerId uint64 `protobuf:"varint,1,opt,name=player_id,json=playerId,proto3" json:"player_id,omitempty"` // 玩家id
}

func (x *ExitRoomBroadcastNtf) Reset() {
	*x = ExitRoomBroadcastNtf{}
	if protoimpl.UnsafeEnabled {
		mi := &file_spot_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExitRoomBroadcastNtf) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExitRoomBroadcastNtf) ProtoMessage() {}

func (x *ExitRoomBroadcastNtf) ProtoReflect() protoreflect.Message {
	mi := &file_spot_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExitRoomBroadcastNtf.ProtoReflect.Descriptor instead.
func (*ExitRoomBroadcastNtf) Descriptor() ([]byte, []int) {
	return file_spot_proto_rawDescGZIP(), []int{24}
}

func (x *ExitRoomBroadcastNtf) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

// 退出结算通知
type ExitRoomSettleInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TotalExp      int32              `protobuf:"varint,1,opt,name=total_exp,json=totalExp,proto3" json:"total_exp,omitempty"`                  // 经验数
	RewardInfo    []*common.ItemBase `protobuf:"bytes,2,rep,name=reward_info,json=rewardInfo,proto3" json:"reward_info,omitempty"`             // 奖励信息
	NewCoinRecord bool               `protobuf:"varint,3,opt,name=new_coin_record,json=newCoinRecord,proto3" json:"new_coin_record,omitempty"` // 新金币记录
	EnterTime     int64              `protobuf:"varint,4,opt,name=enter_time,json=enterTime,proto3" json:"enter_time,omitempty"`               // 进入钓场时间戳-秒
}

func (x *ExitRoomSettleInfo) Reset() {
	*x = ExitRoomSettleInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_spot_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExitRoomSettleInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExitRoomSettleInfo) ProtoMessage() {}

func (x *ExitRoomSettleInfo) ProtoReflect() protoreflect.Message {
	mi := &file_spot_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExitRoomSettleInfo.ProtoReflect.Descriptor instead.
func (*ExitRoomSettleInfo) Descriptor() ([]byte, []int) {
	return file_spot_proto_rawDescGZIP(), []int{25}
}

func (x *ExitRoomSettleInfo) GetTotalExp() int32 {
	if x != nil {
		return x.TotalExp
	}
	return 0
}

func (x *ExitRoomSettleInfo) GetRewardInfo() []*common.ItemBase {
	if x != nil {
		return x.RewardInfo
	}
	return nil
}

func (x *ExitRoomSettleInfo) GetNewCoinRecord() bool {
	if x != nil {
		return x.NewCoinRecord
	}
	return false
}

func (x *ExitRoomSettleInfo) GetEnterTime() int64 {
	if x != nil {
		return x.EnterTime
	}
	return 0
}

// 退出结算通知--（版号服特用）
type ExitRoomSettleNtf struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TotalExp      int32              `protobuf:"varint,1,opt,name=total_exp,json=totalExp,proto3" json:"total_exp,omitempty"`                  // 经验数
	RewardInfo    []*common.ItemBase `protobuf:"bytes,2,rep,name=reward_info,json=rewardInfo,proto3" json:"reward_info,omitempty"`             // 奖励信息
	NewCoinRecord bool               `protobuf:"varint,3,opt,name=new_coin_record,json=newCoinRecord,proto3" json:"new_coin_record,omitempty"` // 新金币记录
	EnterTime     int64              `protobuf:"varint,4,opt,name=enter_time,json=enterTime,proto3" json:"enter_time,omitempty"`               // 进入钓场时间戳-秒
}

func (x *ExitRoomSettleNtf) Reset() {
	*x = ExitRoomSettleNtf{}
	if protoimpl.UnsafeEnabled {
		mi := &file_spot_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExitRoomSettleNtf) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExitRoomSettleNtf) ProtoMessage() {}

func (x *ExitRoomSettleNtf) ProtoReflect() protoreflect.Message {
	mi := &file_spot_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExitRoomSettleNtf.ProtoReflect.Descriptor instead.
func (*ExitRoomSettleNtf) Descriptor() ([]byte, []int) {
	return file_spot_proto_rawDescGZIP(), []int{26}
}

func (x *ExitRoomSettleNtf) GetTotalExp() int32 {
	if x != nil {
		return x.TotalExp
	}
	return 0
}

func (x *ExitRoomSettleNtf) GetRewardInfo() []*common.ItemBase {
	if x != nil {
		return x.RewardInfo
	}
	return nil
}

func (x *ExitRoomSettleNtf) GetNewCoinRecord() bool {
	if x != nil {
		return x.NewCoinRecord
	}
	return false
}

func (x *ExitRoomSettleNtf) GetEnterTime() int64 {
	if x != nil {
		return x.EnterTime
	}
	return 0
}

// 体力变化通知
type PlayerEenrgyChangeNtf struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CurEnergy int32 `protobuf:"varint,1,opt,name=cur_energy,json=curEnergy,proto3" json:"cur_energy,omitempty"` // 当前体力
}

func (x *PlayerEenrgyChangeNtf) Reset() {
	*x = PlayerEenrgyChangeNtf{}
	if protoimpl.UnsafeEnabled {
		mi := &file_spot_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlayerEenrgyChangeNtf) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlayerEenrgyChangeNtf) ProtoMessage() {}

func (x *PlayerEenrgyChangeNtf) ProtoReflect() protoreflect.Message {
	mi := &file_spot_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlayerEenrgyChangeNtf.ProtoReflect.Descriptor instead.
func (*PlayerEenrgyChangeNtf) Descriptor() ([]byte, []int) {
	return file_spot_proto_rawDescGZIP(), []int{27}
}

func (x *PlayerEenrgyChangeNtf) GetCurEnergy() int32 {
	if x != nil {
		return x.CurEnergy
	}
	return 0
}

// 搏鱼请求
type FishBattleReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RigId  int32              `protobuf:"varint,1,opt,name=rig_id,json=rigId,proto3" json:"rig_id,omitempty"`              // 钓组id
	Result common.FISH_RESULT `protobuf:"varint,2,opt,name=result,proto3,enum=common.FISH_RESULT" json:"result,omitempty"` // 搏鱼结果
}

func (x *FishBattleReq) Reset() {
	*x = FishBattleReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_spot_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FishBattleReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FishBattleReq) ProtoMessage() {}

func (x *FishBattleReq) ProtoReflect() protoreflect.Message {
	mi := &file_spot_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FishBattleReq.ProtoReflect.Descriptor instead.
func (*FishBattleReq) Descriptor() ([]byte, []int) {
	return file_spot_proto_rawDescGZIP(), []int{28}
}

func (x *FishBattleReq) GetRigId() int32 {
	if x != nil {
		return x.RigId
	}
	return 0
}

func (x *FishBattleReq) GetResult() common.FISH_RESULT {
	if x != nil {
		return x.Result
	}
	return common.FISH_RESULT(0)
}

// 搏鱼响应
type FishBattleRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret      *common.Result     `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`                                // 结果
	RigId    int32              `protobuf:"varint,2,opt,name=rig_id,json=rigId,proto3" json:"rig_id,omitempty"`              // 钓组id
	Result   common.FISH_RESULT `protobuf:"varint,3,opt,name=result,proto3,enum=common.FISH_RESULT" json:"result,omitempty"` // 搏鱼结果
	FishInfo *common.FishInfo   `protobuf:"bytes,4,opt,name=fish_info,json=fishInfo,proto3" json:"fish_info,omitempty"`      // 鱼详细信息(针对钓到鱼的情况)
}

func (x *FishBattleRsp) Reset() {
	*x = FishBattleRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_spot_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FishBattleRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FishBattleRsp) ProtoMessage() {}

func (x *FishBattleRsp) ProtoReflect() protoreflect.Message {
	mi := &file_spot_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FishBattleRsp.ProtoReflect.Descriptor instead.
func (*FishBattleRsp) Descriptor() ([]byte, []int) {
	return file_spot_proto_rawDescGZIP(), []int{29}
}

func (x *FishBattleRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *FishBattleRsp) GetRigId() int32 {
	if x != nil {
		return x.RigId
	}
	return 0
}

func (x *FishBattleRsp) GetResult() common.FISH_RESULT {
	if x != nil {
		return x.Result
	}
	return common.FISH_RESULT(0)
}

func (x *FishBattleRsp) GetFishInfo() *common.FishInfo {
	if x != nil {
		return x.FishInfo
	}
	return nil
}

// 切换竿组请求
type SwitchRodRigReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RigId int32 `protobuf:"varint,1,opt,name=rig_id,json=rigId,proto3" json:"rig_id,omitempty"` // 钓组id
}

func (x *SwitchRodRigReq) Reset() {
	*x = SwitchRodRigReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_spot_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SwitchRodRigReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SwitchRodRigReq) ProtoMessage() {}

func (x *SwitchRodRigReq) ProtoReflect() protoreflect.Message {
	mi := &file_spot_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SwitchRodRigReq.ProtoReflect.Descriptor instead.
func (*SwitchRodRigReq) Descriptor() ([]byte, []int) {
	return file_spot_proto_rawDescGZIP(), []int{30}
}

func (x *SwitchRodRigReq) GetRigId() int32 {
	if x != nil {
		return x.RigId
	}
	return 0
}

// 切换竿组响应
type SwitchRodRigRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret   *common.Result `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`                   // 结果
	RigId int32          `protobuf:"varint,2,opt,name=rig_id,json=rigId,proto3" json:"rig_id,omitempty"` // 钓组id
}

func (x *SwitchRodRigRsp) Reset() {
	*x = SwitchRodRigRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_spot_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SwitchRodRigRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SwitchRodRigRsp) ProtoMessage() {}

func (x *SwitchRodRigRsp) ProtoReflect() protoreflect.Message {
	mi := &file_spot_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SwitchRodRigRsp.ProtoReflect.Descriptor instead.
func (*SwitchRodRigRsp) Descriptor() ([]byte, []int) {
	return file_spot_proto_rawDescGZIP(), []int{31}
}

func (x *SwitchRodRigRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *SwitchRodRigRsp) GetRigId() int32 {
	if x != nil {
		return x.RigId
	}
	return 0
}

// 用户体力消耗
type PlayerEnergyCostReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Energy int32 `protobuf:"varint,1,opt,name=energy,proto3" json:"energy,omitempty"` // 消耗体力
}

func (x *PlayerEnergyCostReq) Reset() {
	*x = PlayerEnergyCostReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_spot_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlayerEnergyCostReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlayerEnergyCostReq) ProtoMessage() {}

func (x *PlayerEnergyCostReq) ProtoReflect() protoreflect.Message {
	mi := &file_spot_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlayerEnergyCostReq.ProtoReflect.Descriptor instead.
func (*PlayerEnergyCostReq) Descriptor() ([]byte, []int) {
	return file_spot_proto_rawDescGZIP(), []int{32}
}

func (x *PlayerEnergyCostReq) GetEnergy() int32 {
	if x != nil {
		return x.Energy
	}
	return 0
}

// 用户体力消耗响应
type PlayerEnergyCostRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret    *common.Result `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`        // 结果
	Energy int32          `protobuf:"varint,2,opt,name=energy,proto3" json:"energy,omitempty"` // 消耗体力
}

func (x *PlayerEnergyCostRsp) Reset() {
	*x = PlayerEnergyCostRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_spot_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlayerEnergyCostRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlayerEnergyCostRsp) ProtoMessage() {}

func (x *PlayerEnergyCostRsp) ProtoReflect() protoreflect.Message {
	mi := &file_spot_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlayerEnergyCostRsp.ProtoReflect.Descriptor instead.
func (*PlayerEnergyCostRsp) Descriptor() ([]byte, []int) {
	return file_spot_proto_rawDescGZIP(), []int{33}
}

func (x *PlayerEnergyCostRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *PlayerEnergyCostRsp) GetEnergy() int32 {
	if x != nil {
		return x.Energy
	}
	return 0
}

// 钓鱼事件请求
type FishingEventReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NeedBroadcast bool                     `protobuf:"varint,1,opt,name=need_broadcast,json=needBroadcast,proto3" json:"need_broadcast,omitempty"` // 是否需要广播
	EventInfo     *common.FishingEventInfo `protobuf:"bytes,2,opt,name=event_info,json=eventInfo,proto3" json:"event_info,omitempty"`              // 事件信息
}

func (x *FishingEventReq) Reset() {
	*x = FishingEventReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_spot_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FishingEventReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FishingEventReq) ProtoMessage() {}

func (x *FishingEventReq) ProtoReflect() protoreflect.Message {
	mi := &file_spot_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FishingEventReq.ProtoReflect.Descriptor instead.
func (*FishingEventReq) Descriptor() ([]byte, []int) {
	return file_spot_proto_rawDescGZIP(), []int{34}
}

func (x *FishingEventReq) GetNeedBroadcast() bool {
	if x != nil {
		return x.NeedBroadcast
	}
	return false
}

func (x *FishingEventReq) GetEventInfo() *common.FishingEventInfo {
	if x != nil {
		return x.EventInfo
	}
	return nil
}

// 钓鱼事件响应
type FishingEventRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret       *common.Result           `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`                              // 结果
	EventInfo *common.FishingEventInfo `protobuf:"bytes,2,opt,name=event_info,json=eventInfo,proto3" json:"event_info,omitempty"` // 事件信息
}

func (x *FishingEventRsp) Reset() {
	*x = FishingEventRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_spot_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FishingEventRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FishingEventRsp) ProtoMessage() {}

func (x *FishingEventRsp) ProtoReflect() protoreflect.Message {
	mi := &file_spot_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FishingEventRsp.ProtoReflect.Descriptor instead.
func (*FishingEventRsp) Descriptor() ([]byte, []int) {
	return file_spot_proto_rawDescGZIP(), []int{35}
}

func (x *FishingEventRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *FishingEventRsp) GetEventInfo() *common.FishingEventInfo {
	if x != nil {
		return x.EventInfo
	}
	return nil
}

// 钓鱼事件广播
type FishingEventBroadcastNtf struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SenderId  uint64                   `protobuf:"varint,1,opt,name=sender_id,json=senderId,proto3" json:"sender_id,omitempty"`   // 发送者(如:祝贺者id)
	EventInfo *common.FishingEventInfo `protobuf:"bytes,2,opt,name=event_info,json=eventInfo,proto3" json:"event_info,omitempty"` // 事件信息
}

func (x *FishingEventBroadcastNtf) Reset() {
	*x = FishingEventBroadcastNtf{}
	if protoimpl.UnsafeEnabled {
		mi := &file_spot_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FishingEventBroadcastNtf) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FishingEventBroadcastNtf) ProtoMessage() {}

func (x *FishingEventBroadcastNtf) ProtoReflect() protoreflect.Message {
	mi := &file_spot_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FishingEventBroadcastNtf.ProtoReflect.Descriptor instead.
func (*FishingEventBroadcastNtf) Descriptor() ([]byte, []int) {
	return file_spot_proto_rawDescGZIP(), []int{36}
}

func (x *FishingEventBroadcastNtf) GetSenderId() uint64 {
	if x != nil {
		return x.SenderId
	}
	return 0
}

func (x *FishingEventBroadcastNtf) GetEventInfo() *common.FishingEventInfo {
	if x != nil {
		return x.EventInfo
	}
	return nil
}

// 中鱼开始请求
type HookStartReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CalcType  common.HOOK_FISH_CALC_TYPE `protobuf:"varint,1,opt,name=calc_type,json=calcType,proto3,enum=common.HOOK_FISH_CALC_TYPE" json:"calc_type,omitempty"` // 中鱼计算类型
	PondId    int64                      `protobuf:"varint,2,opt,name=pond_id,json=pondId,proto3" json:"pond_id,omitempty"`                                       // 钓场id
	RigId     int32                      `protobuf:"varint,3,opt,name=rig_id,json=rigId,proto3" json:"rig_id,omitempty"`                                          // 钓组id
	HookBait  *common.HookBait           `protobuf:"bytes,4,opt,name=hook_bait,json=hookBait,proto3" json:"hook_bait,omitempty"`                                  // 钩饵组合
	HookHabit *common.HookHabitParam     `protobuf:"bytes,5,opt,name=hook_habit,json=hookHabit,proto3" json:"hook_habit,omitempty"`                               // 中鱼习性参数
}

func (x *HookStartReq) Reset() {
	*x = HookStartReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_spot_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HookStartReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HookStartReq) ProtoMessage() {}

func (x *HookStartReq) ProtoReflect() protoreflect.Message {
	mi := &file_spot_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HookStartReq.ProtoReflect.Descriptor instead.
func (*HookStartReq) Descriptor() ([]byte, []int) {
	return file_spot_proto_rawDescGZIP(), []int{37}
}

func (x *HookStartReq) GetCalcType() common.HOOK_FISH_CALC_TYPE {
	if x != nil {
		return x.CalcType
	}
	return common.HOOK_FISH_CALC_TYPE(0)
}

func (x *HookStartReq) GetPondId() int64 {
	if x != nil {
		return x.PondId
	}
	return 0
}

func (x *HookStartReq) GetRigId() int32 {
	if x != nil {
		return x.RigId
	}
	return 0
}

func (x *HookStartReq) GetHookBait() *common.HookBait {
	if x != nil {
		return x.HookBait
	}
	return nil
}

func (x *HookStartReq) GetHookHabit() *common.HookHabitParam {
	if x != nil {
		return x.HookHabit
	}
	return nil
}

// 中鱼开始响应
type HookStartRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret         *common.Result          `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`                                    // 结果
	SyncControl *common.FishSyncControl `protobuf:"bytes,2,opt,name=sync_control,json=syncControl,proto3" json:"sync_control,omitempty"` // 请求同步控制
}

func (x *HookStartRsp) Reset() {
	*x = HookStartRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_spot_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HookStartRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HookStartRsp) ProtoMessage() {}

func (x *HookStartRsp) ProtoReflect() protoreflect.Message {
	mi := &file_spot_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HookStartRsp.ProtoReflect.Descriptor instead.
func (*HookStartRsp) Descriptor() ([]byte, []int) {
	return file_spot_proto_rawDescGZIP(), []int{38}
}

func (x *HookStartRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *HookStartRsp) GetSyncControl() *common.FishSyncControl {
	if x != nil {
		return x.SyncControl
	}
	return nil
}

// 切线请求
type KillLineReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RigId    int32            `protobuf:"varint,1,opt,name=rig_id,json=rigId,proto3" json:"rig_id,omitempty"`         // 钓组id
	HookBait *common.HookBait `protobuf:"bytes,2,opt,name=hook_bait,json=hookBait,proto3" json:"hook_bait,omitempty"` // 钩饵组合
}

func (x *KillLineReq) Reset() {
	*x = KillLineReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_spot_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KillLineReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KillLineReq) ProtoMessage() {}

func (x *KillLineReq) ProtoReflect() protoreflect.Message {
	mi := &file_spot_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KillLineReq.ProtoReflect.Descriptor instead.
func (*KillLineReq) Descriptor() ([]byte, []int) {
	return file_spot_proto_rawDescGZIP(), []int{39}
}

func (x *KillLineReq) GetRigId() int32 {
	if x != nil {
		return x.RigId
	}
	return 0
}

func (x *KillLineReq) GetHookBait() *common.HookBait {
	if x != nil {
		return x.HookBait
	}
	return nil
}

// 切线响应
type KillLineRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret      *common.Result   `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`                           // 结果
	RigId    int32            `protobuf:"varint,2,opt,name=rig_id,json=rigId,proto3" json:"rig_id,omitempty"`         // 钓组id
	HookBait *common.HookBait `protobuf:"bytes,3,opt,name=hook_bait,json=hookBait,proto3" json:"hook_bait,omitempty"` // 钩饵组合
}

func (x *KillLineRsp) Reset() {
	*x = KillLineRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_spot_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KillLineRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KillLineRsp) ProtoMessage() {}

func (x *KillLineRsp) ProtoReflect() protoreflect.Message {
	mi := &file_spot_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KillLineRsp.ProtoReflect.Descriptor instead.
func (*KillLineRsp) Descriptor() ([]byte, []int) {
	return file_spot_proto_rawDescGZIP(), []int{40}
}

func (x *KillLineRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *KillLineRsp) GetRigId() int32 {
	if x != nil {
		return x.RigId
	}
	return 0
}

func (x *KillLineRsp) GetHookBait() *common.HookBait {
	if x != nil {
		return x.HookBait
	}
	return nil
}

var File_spot_proto protoreflect.FileDescriptor

var file_spot_proto_rawDesc = []byte{
	0x0a, 0x0a, 0x73, 0x70, 0x6f, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x06, 0x73, 0x70,
	0x6f, 0x74, 0x50, 0x42, 0x1a, 0x0a, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x0c, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0c,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x5c, 0x0a, 0x0f,
	0x47, 0x65, 0x74, 0x53, 0x70, 0x6f, 0x74, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x12,
	0x17, 0x0a, 0x07, 0x70, 0x6f, 0x6e, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x06, 0x70, 0x6f, 0x6e, 0x64, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x72, 0x6f, 0x6f, 0x6d,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49,
	0x64, 0x12, 0x17, 0x0a, 0x07, 0x73, 0x70, 0x6f, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x06, 0x73, 0x70, 0x6f, 0x74, 0x49, 0x64, 0x22, 0xc9, 0x01, 0x0a, 0x0f, 0x47,
	0x65, 0x74, 0x53, 0x70, 0x6f, 0x74, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x52, 0x73, 0x70, 0x12, 0x20,
	0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74,
	0x12, 0x17, 0x0a, 0x07, 0x70, 0x6f, 0x6e, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x06, 0x70, 0x6f, 0x6e, 0x64, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x73, 0x70, 0x6f,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x70, 0x6f, 0x74,
	0x49, 0x64, 0x12, 0x33, 0x0a, 0x09, 0x73, 0x79, 0x6e, 0x63, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x46,
	0x69, 0x73, 0x68, 0x65, 0x72, 0x53, 0x70, 0x6f, 0x74, 0x53, 0x79, 0x6e, 0x63, 0x52, 0x08, 0x73,
	0x79, 0x6e, 0x63, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x69, 0x67, 0x5f, 0x69,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x72, 0x69, 0x67, 0x49, 0x64, 0x12, 0x16,
	0x0a, 0x06, 0x65, 0x6e, 0x65, 0x72, 0x67, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06,
	0x65, 0x6e, 0x65, 0x72, 0x67, 0x79, 0x22, 0x53, 0x0a, 0x15, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x52,
	0x6f, 0x6f, 0x6d, 0x42, 0x72, 0x6f, 0x61, 0x64, 0x63, 0x61, 0x73, 0x74, 0x4e, 0x74, 0x66, 0x12,
	0x3a, 0x0a, 0x0b, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x46, 0x69,
	0x73, 0x68, 0x65, 0x72, 0x79, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x0a, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x28, 0x0a, 0x0d, 0x43,
	0x68, 0x6f, 0x6f, 0x73, 0x65, 0x53, 0x70, 0x6f, 0x74, 0x52, 0x65, 0x71, 0x12, 0x17, 0x0a, 0x07,
	0x73, 0x70, 0x6f, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73,
	0x70, 0x6f, 0x74, 0x49, 0x64, 0x22, 0x4a, 0x0a, 0x0d, 0x43, 0x68, 0x6f, 0x6f, 0x73, 0x65, 0x53,
	0x70, 0x6f, 0x74, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x73, 0x70, 0x6f, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x70, 0x6f, 0x74, 0x49,
	0x64, 0x22, 0x46, 0x0a, 0x0f, 0x53, 0x79, 0x6e, 0x63, 0x53, 0x70, 0x6f, 0x74, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x65, 0x71, 0x12, 0x33, 0x0a, 0x09, 0x73, 0x79, 0x6e, 0x63, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x46, 0x69, 0x73, 0x68, 0x65, 0x72, 0x53, 0x70, 0x6f, 0x74, 0x53, 0x79, 0x6e, 0x63, 0x52,
	0x08, 0x73, 0x79, 0x6e, 0x63, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x4f, 0x0a, 0x18, 0x53, 0x79, 0x6e,
	0x63, 0x53, 0x70, 0x6f, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x72, 0x6f, 0x61, 0x64, 0x63, 0x61,
	0x73, 0x74, 0x4e, 0x74, 0x66, 0x12, 0x33, 0x0a, 0x09, 0x73, 0x79, 0x6e, 0x63, 0x5f, 0x69, 0x6e,
	0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x46, 0x69, 0x73, 0x68, 0x65, 0x72, 0x53, 0x70, 0x6f, 0x74, 0x53, 0x79, 0x6e, 0x63,
	0x52, 0x08, 0x73, 0x79, 0x6e, 0x63, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xd7, 0x01, 0x0a, 0x0b, 0x54,
	0x68, 0x72, 0x6f, 0x77, 0x52, 0x6f, 0x64, 0x52, 0x65, 0x71, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x6f,
	0x6e, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x70, 0x6f, 0x6e,
	0x64, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x69, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x72, 0x69, 0x67, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x09, 0x68, 0x6f,
	0x6f, 0x6b, 0x5f, 0x62, 0x61, 0x69, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x48, 0x6f, 0x6f, 0x6b, 0x42, 0x61, 0x69, 0x74, 0x52,
	0x08, 0x68, 0x6f, 0x6f, 0x6b, 0x42, 0x61, 0x69, 0x74, 0x12, 0x32, 0x0a, 0x09, 0x67, 0x72, 0x69,
	0x64, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x68, 0x72, 0x6f, 0x77, 0x47, 0x72, 0x69, 0x64, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x08, 0x67, 0x72, 0x69, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x35, 0x0a,
	0x0a, 0x68, 0x6f, 0x6f, 0x6b, 0x5f, 0x68, 0x61, 0x62, 0x69, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x48, 0x6f, 0x6f, 0x6b, 0x48,
	0x61, 0x62, 0x69, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x09, 0x68, 0x6f, 0x6f, 0x6b, 0x48,
	0x61, 0x62, 0x69, 0x74, 0x22, 0xca, 0x01, 0x0a, 0x0b, 0x54, 0x68, 0x72, 0x6f, 0x77, 0x52, 0x6f,
	0x64, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x6f, 0x6e, 0x64, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x70, 0x6f, 0x6e, 0x64, 0x49, 0x64, 0x12,
	0x15, 0x0a, 0x06, 0x72, 0x69, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x05, 0x72, 0x69, 0x67, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x09, 0x68, 0x6f, 0x6f, 0x6b, 0x5f, 0x62,
	0x61, 0x69, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x48, 0x6f, 0x6f, 0x6b, 0x42, 0x61, 0x69, 0x74, 0x52, 0x08, 0x68, 0x6f, 0x6f,
	0x6b, 0x42, 0x61, 0x69, 0x74, 0x12, 0x3a, 0x0a, 0x0c, 0x73, 0x79, 0x6e, 0x63, 0x5f, 0x63, 0x6f,
	0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x46, 0x69, 0x73, 0x68, 0x53, 0x79, 0x6e, 0x63, 0x43, 0x6f, 0x6e,
	0x74, 0x72, 0x6f, 0x6c, 0x52, 0x0b, 0x73, 0x79, 0x6e, 0x63, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f,
	0x6c, 0x22, 0xb9, 0x01, 0x0a, 0x0b, 0x43, 0x61, 0x74, 0x63, 0x68, 0x52, 0x6f, 0x64, 0x52, 0x65,
	0x71, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x6f, 0x6e, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x06, 0x70, 0x6f, 0x6e, 0x64, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x69,
	0x67, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x72, 0x69, 0x67, 0x49,
	0x64, 0x12, 0x35, 0x0a, 0x0a, 0x68, 0x6f, 0x6f, 0x6b, 0x5f, 0x68, 0x61, 0x62, 0x69, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x48,
	0x6f, 0x6f, 0x6b, 0x48, 0x61, 0x62, 0x69, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x09, 0x68,
	0x6f, 0x6f, 0x6b, 0x48, 0x61, 0x62, 0x69, 0x74, 0x12, 0x43, 0x0a, 0x11, 0x66, 0x69, 0x73, 0x68,
	0x5f, 0x64, 0x61, 0x6d, 0x61, 0x67, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x46, 0x69, 0x73,
	0x68, 0x44, 0x61, 0x6d, 0x61, 0x67, 0x65, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0f, 0x66, 0x69,
	0x73, 0x68, 0x44, 0x61, 0x6d, 0x61, 0x67, 0x65, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xc7, 0x01,
	0x0a, 0x0b, 0x43, 0x61, 0x74, 0x63, 0x68, 0x52, 0x6f, 0x64, 0x52, 0x73, 0x70, 0x12, 0x17, 0x0a,
	0x07, 0x70, 0x6f, 0x6e, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06,
	0x70, 0x6f, 0x6e, 0x64, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x69, 0x67, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x72, 0x69, 0x67, 0x49, 0x64, 0x12, 0x34, 0x0a,
	0x0b, 0x66, 0x69, 0x73, 0x68, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x46, 0x49, 0x53, 0x48,
	0x5f, 0x52, 0x45, 0x53, 0x55, 0x4c, 0x54, 0x52, 0x0a, 0x66, 0x69, 0x73, 0x68, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x33, 0x0a, 0x09, 0x66, 0x69, 0x73, 0x68, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x46, 0x69, 0x73, 0x68, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08,
	0x66, 0x69, 0x73, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x6e, 0x65, 0x77, 0x5f,
	0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x6e, 0x65,
	0x77, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x22, 0xa8, 0x01, 0x0a, 0x0b, 0x46, 0x69, 0x73, 0x68,
	0x48, 0x6f, 0x6f, 0x6b, 0x52, 0x65, 0x71, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x6f, 0x6e, 0x64, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x70, 0x6f, 0x6e, 0x64, 0x49, 0x64,
	0x12, 0x15, 0x0a, 0x06, 0x72, 0x69, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x05, 0x72, 0x69, 0x67, 0x49, 0x64, 0x12, 0x32, 0x0a, 0x09, 0x67, 0x72, 0x69, 0x64, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x68, 0x72, 0x6f, 0x77, 0x47, 0x72, 0x69, 0x64, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x08, 0x67, 0x72, 0x69, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x35, 0x0a, 0x0a, 0x68,
	0x6f, 0x6f, 0x6b, 0x5f, 0x68, 0x61, 0x62, 0x69, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x48, 0x6f, 0x6f, 0x6b, 0x48, 0x61, 0x62,
	0x69, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x09, 0x68, 0x6f, 0x6f, 0x6b, 0x48, 0x61, 0x62,
	0x69, 0x74, 0x22, 0xe6, 0x01, 0x0a, 0x0b, 0x46, 0x69, 0x73, 0x68, 0x48, 0x6f, 0x6f, 0x6b, 0x52,
	0x73, 0x70, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x6f, 0x6e, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x06, 0x70, 0x6f, 0x6e, 0x64, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x72,
	0x69, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x72, 0x69, 0x67,
	0x49, 0x64, 0x12, 0x32, 0x0a, 0x09, 0x67, 0x72, 0x69, 0x64, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54,
	0x68, 0x72, 0x6f, 0x77, 0x47, 0x72, 0x69, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x67, 0x72,
	0x69, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x22, 0x0a, 0x0d, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x72,
	0x65, 0x71, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x6e,
	0x65, 0x78, 0x74, 0x52, 0x65, 0x71, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0c, 0x66, 0x61,
	0x6b, 0x65, 0x5f, 0x66, 0x69, 0x73, 0x68, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0a, 0x66, 0x61, 0x6b, 0x65, 0x46, 0x69, 0x73, 0x68, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x09,
	0x66, 0x69, 0x73, 0x68, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x46, 0x69, 0x73, 0x68, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x08, 0x66, 0x69, 0x73, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x6b, 0x0a, 0x0f, 0x46,
	0x69, 0x73, 0x68, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x4f, 0x70, 0x74, 0x52, 0x65, 0x71, 0x12, 0x23,
	0x0a, 0x0d, 0x66, 0x69, 0x73, 0x68, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x66, 0x69, 0x73, 0x68, 0x49, 0x6e, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x12, 0x33, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x46, 0x49, 0x53,
	0x48, 0x5f, 0x45, 0x4e, 0x54, 0x52, 0x59, 0x5f, 0x4f, 0x50, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xae, 0x01, 0x0a, 0x0f, 0x46, 0x69, 0x73,
	0x68, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x4f, 0x70, 0x74, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03,
	0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x23,
	0x0a, 0x0d, 0x66, 0x69, 0x73, 0x68, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x66, 0x69, 0x73, 0x68, 0x49, 0x6e, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x12, 0x33, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x46, 0x49, 0x53,
	0x48, 0x5f, 0x45, 0x4e, 0x54, 0x52, 0x59, 0x5f, 0x4f, 0x50, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x69, 0x73, 0x68,
	0x5f, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x66,
	0x69, 0x73, 0x68, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x22, 0x82, 0x01, 0x0a, 0x1b, 0x50, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x46, 0x69, 0x73, 0x68, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x42, 0x72, 0x6f,
	0x61, 0x64, 0x63, 0x61, 0x73, 0x74, 0x4e, 0x74, 0x66, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61,
	0x79, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x70, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x69, 0x73, 0x68, 0x5f, 0x77,
	0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x66, 0x69, 0x73,
	0x68, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x6b, 0x65, 0x65, 0x70, 0x6e,
	0x65, 0x74, 0x5f, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0d, 0x6b, 0x65, 0x65, 0x70, 0x6e, 0x65, 0x74, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x22, 0x88,
	0x01, 0x0a, 0x11, 0x46, 0x69, 0x73, 0x68, 0x4b, 0x65, 0x65, 0x70, 0x6e, 0x65, 0x74, 0x4f, 0x70,
	0x74, 0x52, 0x65, 0x71, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x6f, 0x6e, 0x64, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x70, 0x6f, 0x6e, 0x64, 0x49, 0x64, 0x12, 0x23, 0x0a,
	0x0d, 0x66, 0x69, 0x73, 0x68, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x66, 0x69, 0x73, 0x68, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e,
	0x63, 0x65, 0x12, 0x35, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x46, 0x49, 0x53, 0x48,
	0x5f, 0x4b, 0x45, 0x45, 0x50, 0x4e, 0x45, 0x54, 0x5f, 0x4f, 0x50, 0x54, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xb2, 0x01, 0x0a, 0x11, 0x46, 0x69,
	0x73, 0x68, 0x4b, 0x65, 0x65, 0x70, 0x6e, 0x65, 0x74, 0x4f, 0x70, 0x74, 0x52, 0x73, 0x70, 0x12,
	0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65,
	0x74, 0x12, 0x23, 0x0a, 0x0d, 0x66, 0x69, 0x73, 0x68, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e,
	0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x66, 0x69, 0x73, 0x68, 0x49, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x35, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x46, 0x49, 0x53, 0x48, 0x5f, 0x4b, 0x45, 0x45, 0x50, 0x4e, 0x45, 0x54, 0x5f, 0x4f, 0x50, 0x54,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a,
	0x0b, 0x66, 0x69, 0x73, 0x68, 0x5f, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0a, 0x66, 0x69, 0x73, 0x68, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x22, 0x14,
	0x0a, 0x12, 0x4b, 0x65, 0x65, 0x70, 0x6e, 0x65, 0x74, 0x46, 0x69, 0x73, 0x68, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x65, 0x71, 0x22, 0x49, 0x0a, 0x12, 0x4b, 0x65, 0x65, 0x70, 0x6e, 0x65, 0x74, 0x46,
	0x69, 0x73, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x12, 0x33, 0x0a, 0x09, 0x66, 0x69,
	0x73, 0x68, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x46, 0x69, 0x73, 0x68, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x66, 0x69, 0x73, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x22,
	0x19, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x52, 0x6f, 0x6f, 0x6d, 0x41, 0x6c, 0x6c, 0x50, 0x6c, 0x61,
	0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x22, 0x55, 0x0a, 0x17, 0x47, 0x65,
	0x74, 0x52, 0x6f, 0x6f, 0x6d, 0x41, 0x6c, 0x6c, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x73, 0x70, 0x12, 0x3a, 0x0a, 0x0b, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x46, 0x69, 0x73, 0x68, 0x65, 0x72, 0x79, 0x50, 0x6c, 0x61, 0x79, 0x65,
	0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x22, 0x3f, 0x0a, 0x0b, 0x45, 0x78, 0x69, 0x74, 0x52, 0x6f, 0x6f, 0x6d, 0x52, 0x65, 0x71,
	0x12, 0x17, 0x0a, 0x07, 0x70, 0x6f, 0x6e, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x06, 0x70, 0x6f, 0x6e, 0x64, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x72, 0x6f, 0x6f,
	0x6d, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x6f, 0x6f, 0x6d,
	0x49, 0x64, 0x22, 0x6b, 0x0a, 0x0b, 0x45, 0x78, 0x69, 0x74, 0x52, 0x6f, 0x6f, 0x6d, 0x52, 0x73,
	0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03,
	0x72, 0x65, 0x74, 0x12, 0x3a, 0x0a, 0x0a, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x73, 0x70, 0x6f, 0x74, 0x50, 0x42,
	0x2e, 0x45, 0x78, 0x69, 0x74, 0x52, 0x6f, 0x6f, 0x6d, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x22,
	0x33, 0x0a, 0x14, 0x45, 0x78, 0x69, 0x74, 0x52, 0x6f, 0x6f, 0x6d, 0x42, 0x72, 0x6f, 0x61, 0x64,
	0x63, 0x61, 0x73, 0x74, 0x4e, 0x74, 0x66, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x79,
	0x65, 0x72, 0x49, 0x64, 0x22, 0xab, 0x01, 0x0a, 0x12, 0x45, 0x78, 0x69, 0x74, 0x52, 0x6f, 0x6f,
	0x6d, 0x53, 0x65, 0x74, 0x74, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x65, 0x78, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x45, 0x78, 0x70, 0x12, 0x31, 0x0a, 0x0b, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x42, 0x61, 0x73, 0x65, 0x52,
	0x0a, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x26, 0x0a, 0x0f, 0x6e,
	0x65, 0x77, 0x5f, 0x63, 0x6f, 0x69, 0x6e, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x6e, 0x65, 0x77, 0x43, 0x6f, 0x69, 0x6e, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x54, 0x69,
	0x6d, 0x65, 0x22, 0xaa, 0x01, 0x0a, 0x11, 0x45, 0x78, 0x69, 0x74, 0x52, 0x6f, 0x6f, 0x6d, 0x53,
	0x65, 0x74, 0x74, 0x6c, 0x65, 0x4e, 0x74, 0x66, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x5f, 0x65, 0x78, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x45, 0x78, 0x70, 0x12, 0x31, 0x0a, 0x0b, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x42, 0x61, 0x73, 0x65, 0x52, 0x0a, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x65, 0x77, 0x5f,
	0x63, 0x6f, 0x69, 0x6e, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0d, 0x6e, 0x65, 0x77, 0x43, 0x6f, 0x69, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x22,
	0x36, 0x0a, 0x15, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x45, 0x65, 0x6e, 0x72, 0x67, 0x79, 0x43,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x4e, 0x74, 0x66, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x75, 0x72, 0x5f,
	0x65, 0x6e, 0x65, 0x72, 0x67, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x63, 0x75,
	0x72, 0x45, 0x6e, 0x65, 0x72, 0x67, 0x79, 0x22, 0x53, 0x0a, 0x0d, 0x46, 0x69, 0x73, 0x68, 0x42,
	0x61, 0x74, 0x74, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x69, 0x67, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x72, 0x69, 0x67, 0x49, 0x64, 0x12,
	0x2b, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x46, 0x49, 0x53, 0x48, 0x5f, 0x52, 0x45,
	0x53, 0x55, 0x4c, 0x54, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0xa4, 0x01, 0x0a,
	0x0d, 0x46, 0x69, 0x73, 0x68, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x52, 0x73, 0x70, 0x12, 0x20,
	0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74,
	0x12, 0x15, 0x0a, 0x06, 0x72, 0x69, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x05, 0x72, 0x69, 0x67, 0x49, 0x64, 0x12, 0x2b, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x46, 0x49, 0x53, 0x48, 0x5f, 0x52, 0x45, 0x53, 0x55, 0x4c, 0x54, 0x52, 0x06, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x2d, 0x0a, 0x09, 0x66, 0x69, 0x73, 0x68, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x46, 0x69, 0x73, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x66, 0x69, 0x73, 0x68, 0x49,
	0x6e, 0x66, 0x6f, 0x22, 0x28, 0x0a, 0x0f, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x6f, 0x64,
	0x52, 0x69, 0x67, 0x52, 0x65, 0x71, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x69, 0x67, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x72, 0x69, 0x67, 0x49, 0x64, 0x22, 0x4a, 0x0a,
	0x0f, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x6f, 0x64, 0x52, 0x69, 0x67, 0x52, 0x73, 0x70,
	0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72,
	0x65, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x69, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x05, 0x72, 0x69, 0x67, 0x49, 0x64, 0x22, 0x2d, 0x0a, 0x13, 0x50, 0x6c, 0x61,
	0x79, 0x65, 0x72, 0x45, 0x6e, 0x65, 0x72, 0x67, 0x79, 0x43, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x71,
	0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x65, 0x72, 0x67, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x06, 0x65, 0x6e, 0x65, 0x72, 0x67, 0x79, 0x22, 0x4f, 0x0a, 0x13, 0x50, 0x6c, 0x61, 0x79,
	0x65, 0x72, 0x45, 0x6e, 0x65, 0x72, 0x67, 0x79, 0x43, 0x6f, 0x73, 0x74, 0x52, 0x73, 0x70, 0x12,
	0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65,
	0x74, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x65, 0x72, 0x67, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x06, 0x65, 0x6e, 0x65, 0x72, 0x67, 0x79, 0x22, 0x71, 0x0a, 0x0f, 0x46, 0x69, 0x73,
	0x68, 0x69, 0x6e, 0x67, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x12, 0x25, 0x0a, 0x0e,
	0x6e, 0x65, 0x65, 0x64, 0x5f, 0x62, 0x72, 0x6f, 0x61, 0x64, 0x63, 0x61, 0x73, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x6e, 0x65, 0x65, 0x64, 0x42, 0x72, 0x6f, 0x61, 0x64, 0x63,
	0x61, 0x73, 0x74, 0x12, 0x37, 0x0a, 0x0a, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x46, 0x69, 0x73, 0x68, 0x69, 0x6e, 0x67, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x09, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x6c, 0x0a, 0x0f,
	0x46, 0x69, 0x73, 0x68, 0x69, 0x6e, 0x67, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x73, 0x70, 0x12,
	0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65,
	0x74, 0x12, 0x37, 0x0a, 0x0a, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x46,
	0x69, 0x73, 0x68, 0x69, 0x6e, 0x67, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x09, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x70, 0x0a, 0x18, 0x46, 0x69,
	0x73, 0x68, 0x69, 0x6e, 0x67, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x42, 0x72, 0x6f, 0x61, 0x64, 0x63,
	0x61, 0x73, 0x74, 0x4e, 0x74, 0x66, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x73, 0x65, 0x6e, 0x64, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x37, 0x0a, 0x0a, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x46, 0x69, 0x73, 0x68, 0x69, 0x6e, 0x67, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x09, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xde, 0x01, 0x0a,
	0x0c, 0x48, 0x6f, 0x6f, 0x6b, 0x53, 0x74, 0x61, 0x72, 0x74, 0x52, 0x65, 0x71, 0x12, 0x38, 0x0a,
	0x09, 0x63, 0x61, 0x6c, 0x63, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1b, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x48, 0x4f, 0x4f, 0x4b, 0x5f, 0x46,
	0x49, 0x53, 0x48, 0x5f, 0x43, 0x41, 0x4c, 0x43, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52, 0x08, 0x63,
	0x61, 0x6c, 0x63, 0x54, 0x79, 0x70, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x6f, 0x6e, 0x64, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x70, 0x6f, 0x6e, 0x64, 0x49, 0x64,
	0x12, 0x15, 0x0a, 0x06, 0x72, 0x69, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x05, 0x72, 0x69, 0x67, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x09, 0x68, 0x6f, 0x6f, 0x6b, 0x5f,
	0x62, 0x61, 0x69, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x48, 0x6f, 0x6f, 0x6b, 0x42, 0x61, 0x69, 0x74, 0x52, 0x08, 0x68, 0x6f,
	0x6f, 0x6b, 0x42, 0x61, 0x69, 0x74, 0x12, 0x35, 0x0a, 0x0a, 0x68, 0x6f, 0x6f, 0x6b, 0x5f, 0x68,
	0x61, 0x62, 0x69, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x48, 0x6f, 0x6f, 0x6b, 0x48, 0x61, 0x62, 0x69, 0x74, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x52, 0x09, 0x68, 0x6f, 0x6f, 0x6b, 0x48, 0x61, 0x62, 0x69, 0x74, 0x22, 0x6c, 0x0a,
	0x0c, 0x48, 0x6f, 0x6f, 0x6b, 0x53, 0x74, 0x61, 0x72, 0x74, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a,
	0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12,
	0x3a, 0x0a, 0x0c, 0x73, 0x79, 0x6e, 0x63, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x46,
	0x69, 0x73, 0x68, 0x53, 0x79, 0x6e, 0x63, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x52, 0x0b,
	0x73, 0x79, 0x6e, 0x63, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x22, 0x53, 0x0a, 0x0b, 0x4b,
	0x69, 0x6c, 0x6c, 0x4c, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x69,
	0x67, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x72, 0x69, 0x67, 0x49,
	0x64, 0x12, 0x2d, 0x0a, 0x09, 0x68, 0x6f, 0x6f, 0x6b, 0x5f, 0x62, 0x61, 0x69, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x48, 0x6f,
	0x6f, 0x6b, 0x42, 0x61, 0x69, 0x74, 0x52, 0x08, 0x68, 0x6f, 0x6f, 0x6b, 0x42, 0x61, 0x69, 0x74,
	0x22, 0x75, 0x0a, 0x0b, 0x4b, 0x69, 0x6c, 0x6c, 0x4c, 0x69, 0x6e, 0x65, 0x52, 0x73, 0x70, 0x12,
	0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65,
	0x74, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x69, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x05, 0x72, 0x69, 0x67, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x09, 0x68, 0x6f, 0x6f, 0x6b,
	0x5f, 0x62, 0x61, 0x69, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x48, 0x6f, 0x6f, 0x6b, 0x42, 0x61, 0x69, 0x74, 0x52, 0x08, 0x68,
	0x6f, 0x6f, 0x6b, 0x42, 0x61, 0x69, 0x74, 0x42, 0x3d, 0x5a, 0x3b, 0x67, 0x69, 0x74, 0x2e, 0x6b,
	0x65, 0x65, 0x70, 0x66, 0x61, 0x6e, 0x63, 0x79, 0x2e, 0x78, 0x79, 0x7a, 0x2f, 0x62, 0x61, 0x63,
	0x6b, 0x2d, 0x65, 0x6e, 0x64, 0x2f, 0x66, 0x61, 0x6e, 0x63, 0x79, 0x2d, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x63, 0x70, 0x62, 0x2f, 0x73, 0x70, 0x6f, 0x74, 0x3b,
	0x73, 0x70, 0x6f, 0x74, 0x50, 0x42, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_spot_proto_rawDescOnce sync.Once
	file_spot_proto_rawDescData = file_spot_proto_rawDesc
)

func file_spot_proto_rawDescGZIP() []byte {
	file_spot_proto_rawDescOnce.Do(func() {
		file_spot_proto_rawDescData = protoimpl.X.CompressGZIP(file_spot_proto_rawDescData)
	})
	return file_spot_proto_rawDescData
}

var file_spot_proto_msgTypes = make([]protoimpl.MessageInfo, 41)
var file_spot_proto_goTypes = []interface{}{
	(*GetSpotSceneReq)(nil),             // 0: spotPB.GetSpotSceneReq
	(*GetSpotSceneRsp)(nil),             // 1: spotPB.GetSpotSceneRsp
	(*EnterRoomBroadcastNtf)(nil),       // 2: spotPB.EnterRoomBroadcastNtf
	(*ChooseSpotReq)(nil),               // 3: spotPB.ChooseSpotReq
	(*ChooseSpotRsp)(nil),               // 4: spotPB.ChooseSpotRsp
	(*SyncSpotInfoReq)(nil),             // 5: spotPB.SyncSpotInfoReq
	(*SyncSpotInfoBroadcastNtf)(nil),    // 6: spotPB.SyncSpotInfoBroadcastNtf
	(*ThrowRodReq)(nil),                 // 7: spotPB.ThrowRodReq
	(*ThrowRodRsp)(nil),                 // 8: spotPB.ThrowRodRsp
	(*CatchRodReq)(nil),                 // 9: spotPB.CatchRodReq
	(*CatchRodRsp)(nil),                 // 10: spotPB.CatchRodRsp
	(*FishHookReq)(nil),                 // 11: spotPB.FishHookReq
	(*FishHookRsp)(nil),                 // 12: spotPB.FishHookRsp
	(*FishEntryOptReq)(nil),             // 13: spotPB.FishEntryOptReq
	(*FishEntryOptRsp)(nil),             // 14: spotPB.FishEntryOptRsp
	(*PlayerFishEntryBroadcastNtf)(nil), // 15: spotPB.PlayerFishEntryBroadcastNtf
	(*FishKeepnetOptReq)(nil),           // 16: spotPB.FishKeepnetOptReq
	(*FishKeepnetOptRsp)(nil),           // 17: spotPB.FishKeepnetOptRsp
	(*KeepnetFishInfoReq)(nil),          // 18: spotPB.KeepnetFishInfoReq
	(*KeepnetFishInfoRsp)(nil),          // 19: spotPB.KeepnetFishInfoRsp
	(*GetRoomAllPlayerInfoReq)(nil),     // 20: spotPB.GetRoomAllPlayerInfoReq
	(*GetRoomAllPlayerInfoRsp)(nil),     // 21: spotPB.GetRoomAllPlayerInfoRsp
	(*ExitRoomReq)(nil),                 // 22: spotPB.ExitRoomReq
	(*ExitRoomRsp)(nil),                 // 23: spotPB.ExitRoomRsp
	(*ExitRoomBroadcastNtf)(nil),        // 24: spotPB.ExitRoomBroadcastNtf
	(*ExitRoomSettleInfo)(nil),          // 25: spotPB.ExitRoomSettleInfo
	(*ExitRoomSettleNtf)(nil),           // 26: spotPB.ExitRoomSettleNtf
	(*PlayerEenrgyChangeNtf)(nil),       // 27: spotPB.PlayerEenrgyChangeNtf
	(*FishBattleReq)(nil),               // 28: spotPB.FishBattleReq
	(*FishBattleRsp)(nil),               // 29: spotPB.FishBattleRsp
	(*SwitchRodRigReq)(nil),             // 30: spotPB.SwitchRodRigReq
	(*SwitchRodRigRsp)(nil),             // 31: spotPB.SwitchRodRigRsp
	(*PlayerEnergyCostReq)(nil),         // 32: spotPB.PlayerEnergyCostReq
	(*PlayerEnergyCostRsp)(nil),         // 33: spotPB.PlayerEnergyCostRsp
	(*FishingEventReq)(nil),             // 34: spotPB.FishingEventReq
	(*FishingEventRsp)(nil),             // 35: spotPB.FishingEventRsp
	(*FishingEventBroadcastNtf)(nil),    // 36: spotPB.FishingEventBroadcastNtf
	(*HookStartReq)(nil),                // 37: spotPB.HookStartReq
	(*HookStartRsp)(nil),                // 38: spotPB.HookStartRsp
	(*KillLineReq)(nil),                 // 39: spotPB.KillLineReq
	(*KillLineRsp)(nil),                 // 40: spotPB.KillLineRsp
	(*common.Result)(nil),               // 41: common.Result
	(*common.FisherSpotSync)(nil),       // 42: common.FisherSpotSync
	(*common.FisheryPlayerInfo)(nil),    // 43: common.FisheryPlayerInfo
	(*common.HookBait)(nil),             // 44: common.HookBait
	(*common.ThrowGridInfo)(nil),        // 45: common.ThrowGridInfo
	(*common.HookHabitParam)(nil),       // 46: common.HookHabitParam
	(*common.FishSyncControl)(nil),      // 47: common.FishSyncControl
	(*common.FishDamagedInfo)(nil),      // 48: common.FishDamagedInfo
	(common.FISH_RESULT)(0),             // 49: common.FISH_RESULT
	(*common.FishDetailInfo)(nil),       // 50: common.FishDetailInfo
	(*common.FishInfo)(nil),             // 51: common.FishInfo
	(common.FISH_ENTRY_OPT_TYPE)(0),     // 52: common.FISH_ENTRY_OPT_TYPE
	(common.FISH_KEEPNET_OPT_TYPE)(0),   // 53: common.FISH_KEEPNET_OPT_TYPE
	(*common.ItemBase)(nil),             // 54: common.ItemBase
	(*common.FishingEventInfo)(nil),     // 55: common.FishingEventInfo
	(common.HOOK_FISH_CALC_TYPE)(0),     // 56: common.HOOK_FISH_CALC_TYPE
}
var file_spot_proto_depIdxs = []int32{
	41, // 0: spotPB.GetSpotSceneRsp.ret:type_name -> common.Result
	42, // 1: spotPB.GetSpotSceneRsp.sync_list:type_name -> common.FisherSpotSync
	43, // 2: spotPB.EnterRoomBroadcastNtf.player_info:type_name -> common.FisheryPlayerInfo
	41, // 3: spotPB.ChooseSpotRsp.ret:type_name -> common.Result
	42, // 4: spotPB.SyncSpotInfoReq.sync_info:type_name -> common.FisherSpotSync
	42, // 5: spotPB.SyncSpotInfoBroadcastNtf.sync_info:type_name -> common.FisherSpotSync
	44, // 6: spotPB.ThrowRodReq.hook_bait:type_name -> common.HookBait
	45, // 7: spotPB.ThrowRodReq.grid_info:type_name -> common.ThrowGridInfo
	46, // 8: spotPB.ThrowRodReq.hook_habit:type_name -> common.HookHabitParam
	41, // 9: spotPB.ThrowRodRsp.ret:type_name -> common.Result
	44, // 10: spotPB.ThrowRodRsp.hook_bait:type_name -> common.HookBait
	47, // 11: spotPB.ThrowRodRsp.sync_control:type_name -> common.FishSyncControl
	46, // 12: spotPB.CatchRodReq.hook_habit:type_name -> common.HookHabitParam
	48, // 13: spotPB.CatchRodReq.fish_damaged_info:type_name -> common.FishDamagedInfo
	49, // 14: spotPB.CatchRodRsp.fish_result:type_name -> common.FISH_RESULT
	50, // 15: spotPB.CatchRodRsp.fish_info:type_name -> common.FishDetailInfo
	45, // 16: spotPB.FishHookReq.grid_info:type_name -> common.ThrowGridInfo
	46, // 17: spotPB.FishHookReq.hook_habit:type_name -> common.HookHabitParam
	45, // 18: spotPB.FishHookRsp.grid_info:type_name -> common.ThrowGridInfo
	51, // 19: spotPB.FishHookRsp.fish_info:type_name -> common.FishInfo
	52, // 20: spotPB.FishEntryOptReq.action:type_name -> common.FISH_ENTRY_OPT_TYPE
	41, // 21: spotPB.FishEntryOptRsp.ret:type_name -> common.Result
	52, // 22: spotPB.FishEntryOptRsp.action:type_name -> common.FISH_ENTRY_OPT_TYPE
	53, // 23: spotPB.FishKeepnetOptReq.action:type_name -> common.FISH_KEEPNET_OPT_TYPE
	41, // 24: spotPB.FishKeepnetOptRsp.ret:type_name -> common.Result
	53, // 25: spotPB.FishKeepnetOptRsp.action:type_name -> common.FISH_KEEPNET_OPT_TYPE
	50, // 26: spotPB.KeepnetFishInfoRsp.fish_info:type_name -> common.FishDetailInfo
	43, // 27: spotPB.GetRoomAllPlayerInfoRsp.player_info:type_name -> common.FisheryPlayerInfo
	41, // 28: spotPB.ExitRoomRsp.ret:type_name -> common.Result
	25, // 29: spotPB.ExitRoomRsp.SettleInfo:type_name -> spotPB.ExitRoomSettleInfo
	54, // 30: spotPB.ExitRoomSettleInfo.reward_info:type_name -> common.ItemBase
	54, // 31: spotPB.ExitRoomSettleNtf.reward_info:type_name -> common.ItemBase
	49, // 32: spotPB.FishBattleReq.result:type_name -> common.FISH_RESULT
	41, // 33: spotPB.FishBattleRsp.ret:type_name -> common.Result
	49, // 34: spotPB.FishBattleRsp.result:type_name -> common.FISH_RESULT
	51, // 35: spotPB.FishBattleRsp.fish_info:type_name -> common.FishInfo
	41, // 36: spotPB.SwitchRodRigRsp.ret:type_name -> common.Result
	41, // 37: spotPB.PlayerEnergyCostRsp.ret:type_name -> common.Result
	55, // 38: spotPB.FishingEventReq.event_info:type_name -> common.FishingEventInfo
	41, // 39: spotPB.FishingEventRsp.ret:type_name -> common.Result
	55, // 40: spotPB.FishingEventRsp.event_info:type_name -> common.FishingEventInfo
	55, // 41: spotPB.FishingEventBroadcastNtf.event_info:type_name -> common.FishingEventInfo
	56, // 42: spotPB.HookStartReq.calc_type:type_name -> common.HOOK_FISH_CALC_TYPE
	44, // 43: spotPB.HookStartReq.hook_bait:type_name -> common.HookBait
	46, // 44: spotPB.HookStartReq.hook_habit:type_name -> common.HookHabitParam
	41, // 45: spotPB.HookStartRsp.ret:type_name -> common.Result
	47, // 46: spotPB.HookStartRsp.sync_control:type_name -> common.FishSyncControl
	44, // 47: spotPB.KillLineReq.hook_bait:type_name -> common.HookBait
	41, // 48: spotPB.KillLineRsp.ret:type_name -> common.Result
	44, // 49: spotPB.KillLineRsp.hook_bait:type_name -> common.HookBait
	50, // [50:50] is the sub-list for method output_type
	50, // [50:50] is the sub-list for method input_type
	50, // [50:50] is the sub-list for extension type_name
	50, // [50:50] is the sub-list for extension extendee
	0,  // [0:50] is the sub-list for field type_name
}

func init() { file_spot_proto_init() }
func file_spot_proto_init() {
	if File_spot_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_spot_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSpotSceneReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_spot_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSpotSceneRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_spot_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EnterRoomBroadcastNtf); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_spot_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChooseSpotReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_spot_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChooseSpotRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_spot_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncSpotInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_spot_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncSpotInfoBroadcastNtf); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_spot_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ThrowRodReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_spot_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ThrowRodRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_spot_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CatchRodReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_spot_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CatchRodRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_spot_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FishHookReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_spot_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FishHookRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_spot_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FishEntryOptReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_spot_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FishEntryOptRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_spot_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlayerFishEntryBroadcastNtf); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_spot_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FishKeepnetOptReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_spot_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FishKeepnetOptRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_spot_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KeepnetFishInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_spot_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KeepnetFishInfoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_spot_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRoomAllPlayerInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_spot_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRoomAllPlayerInfoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_spot_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExitRoomReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_spot_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExitRoomRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_spot_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExitRoomBroadcastNtf); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_spot_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExitRoomSettleInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_spot_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExitRoomSettleNtf); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_spot_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlayerEenrgyChangeNtf); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_spot_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FishBattleReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_spot_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FishBattleRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_spot_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SwitchRodRigReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_spot_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SwitchRodRigRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_spot_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlayerEnergyCostReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_spot_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlayerEnergyCostRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_spot_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FishingEventReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_spot_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FishingEventRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_spot_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FishingEventBroadcastNtf); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_spot_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HookStartReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_spot_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HookStartRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_spot_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KillLineReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_spot_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KillLineRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_spot_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   41,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_spot_proto_goTypes,
		DependencyIndexes: file_spot_proto_depIdxs,
		MessageInfos:      file_spot_proto_msgTypes,
	}.Build()
	File_spot_proto = out.File
	file_spot_proto_rawDesc = nil
	file_spot_proto_goTypes = nil
	file_spot_proto_depIdxs = nil
}
