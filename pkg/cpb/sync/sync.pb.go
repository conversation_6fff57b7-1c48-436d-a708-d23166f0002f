// 同步服务器

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v5.27.0
// source: sync.proto

package syncPB

import (
	common "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// SyncSpotInfoReq 钓点服信息同步请求(服务器只转发 不处理)
type SyncSpotInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RoomId   string `protobuf:"bytes,1,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`       // 房间号
	SyncData []byte `protobuf:"bytes,2,opt,name=sync_data,json=syncData,proto3" json:"sync_data,omitempty"` // 同步信息
}

func (x *SyncSpotInfoReq) Reset() {
	*x = SyncSpotInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sync_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncSpotInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncSpotInfoReq) ProtoMessage() {}

func (x *SyncSpotInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_sync_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncSpotInfoReq.ProtoReflect.Descriptor instead.
func (*SyncSpotInfoReq) Descriptor() ([]byte, []int) {
	return file_sync_proto_rawDescGZIP(), []int{0}
}

func (x *SyncSpotInfoReq) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *SyncSpotInfoReq) GetSyncData() []byte {
	if x != nil {
		return x.SyncData
	}
	return nil
}

// SyncSpotInfoBsNtf 钓点服信息同步广播
type SyncSpotInfoBsNtf struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerId uint64 `protobuf:"varint,1,opt,name=player_id,json=playerId,proto3" json:"player_id,omitempty"` // 玩家id
	SyncData []byte `protobuf:"bytes,2,opt,name=sync_data,json=syncData,proto3" json:"sync_data,omitempty"`  // 同步信息
}

func (x *SyncSpotInfoBsNtf) Reset() {
	*x = SyncSpotInfoBsNtf{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sync_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncSpotInfoBsNtf) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncSpotInfoBsNtf) ProtoMessage() {}

func (x *SyncSpotInfoBsNtf) ProtoReflect() protoreflect.Message {
	mi := &file_sync_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncSpotInfoBsNtf.ProtoReflect.Descriptor instead.
func (*SyncSpotInfoBsNtf) Descriptor() ([]byte, []int) {
	return file_sync_proto_rawDescGZIP(), []int{1}
}

func (x *SyncSpotInfoBsNtf) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

func (x *SyncSpotInfoBsNtf) GetSyncData() []byte {
	if x != nil {
		return x.SyncData
	}
	return nil
}

// SyncPosReq 同步玩家位置信息
type SyncPosReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RoomId  string                 `protobuf:"bytes,1,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`    // 房间号
	PosInfo *common.PlayerPosition `protobuf:"bytes,2,opt,name=pos_info,json=posInfo,proto3" json:"pos_info,omitempty"` // 站立位置
}

func (x *SyncPosReq) Reset() {
	*x = SyncPosReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sync_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncPosReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncPosReq) ProtoMessage() {}

func (x *SyncPosReq) ProtoReflect() protoreflect.Message {
	mi := &file_sync_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncPosReq.ProtoReflect.Descriptor instead.
func (*SyncPosReq) Descriptor() ([]byte, []int) {
	return file_sync_proto_rawDescGZIP(), []int{2}
}

func (x *SyncPosReq) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *SyncPosReq) GetPosInfo() *common.PlayerPosition {
	if x != nil {
		return x.PosInfo
	}
	return nil
}

// SyncPosReq 同步玩家位置信息广播
type SyncPosBsNtf struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PosInfo *common.PlayerPosition `protobuf:"bytes,1,opt,name=pos_info,json=posInfo,proto3" json:"pos_info,omitempty"` // 站立位置
}

func (x *SyncPosBsNtf) Reset() {
	*x = SyncPosBsNtf{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sync_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SyncPosBsNtf) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncPosBsNtf) ProtoMessage() {}

func (x *SyncPosBsNtf) ProtoReflect() protoreflect.Message {
	mi := &file_sync_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncPosBsNtf.ProtoReflect.Descriptor instead.
func (*SyncPosBsNtf) Descriptor() ([]byte, []int) {
	return file_sync_proto_rawDescGZIP(), []int{3}
}

func (x *SyncPosBsNtf) GetPosInfo() *common.PlayerPosition {
	if x != nil {
		return x.PosInfo
	}
	return nil
}

// 请求房间所有人的位置信息
type GetRoomPlayerPosReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RoomId string `protobuf:"bytes,1,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"` // 房间号
}

func (x *GetRoomPlayerPosReq) Reset() {
	*x = GetRoomPlayerPosReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sync_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRoomPlayerPosReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRoomPlayerPosReq) ProtoMessage() {}

func (x *GetRoomPlayerPosReq) ProtoReflect() protoreflect.Message {
	mi := &file_sync_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRoomPlayerPosReq.ProtoReflect.Descriptor instead.
func (*GetRoomPlayerPosReq) Descriptor() ([]byte, []int) {
	return file_sync_proto_rawDescGZIP(), []int{4}
}

func (x *GetRoomPlayerPosReq) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

// 返回房间所有人的位置信息
type GetRoomPlayerPosRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PosArr []*common.PlayerPosition `protobuf:"bytes,1,rep,name=pos_arr,json=posArr,proto3" json:"pos_arr,omitempty"` // 所有玩家站立位置信息
}

func (x *GetRoomPlayerPosRsp) Reset() {
	*x = GetRoomPlayerPosRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sync_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRoomPlayerPosRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRoomPlayerPosRsp) ProtoMessage() {}

func (x *GetRoomPlayerPosRsp) ProtoReflect() protoreflect.Message {
	mi := &file_sync_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRoomPlayerPosRsp.ProtoReflect.Descriptor instead.
func (*GetRoomPlayerPosRsp) Descriptor() ([]byte, []int) {
	return file_sync_proto_rawDescGZIP(), []int{5}
}

func (x *GetRoomPlayerPosRsp) GetPosArr() []*common.PlayerPosition {
	if x != nil {
		return x.PosArr
	}
	return nil
}

var File_sync_proto protoreflect.FileDescriptor

var file_sync_proto_rawDesc = []byte{
	0x0a, 0x0a, 0x73, 0x79, 0x6e, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x06, 0x73, 0x79,
	0x6e, 0x63, 0x50, 0x42, 0x1a, 0x0c, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0x47, 0x0a, 0x0f, 0x53, 0x79, 0x6e, 0x63, 0x53, 0x70, 0x6f, 0x74, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x65, 0x71, 0x12, 0x17, 0x0a, 0x07, 0x72, 0x6f, 0x6f, 0x6d, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x12, 0x1b,
	0x0a, 0x09, 0x73, 0x79, 0x6e, 0x63, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0c, 0x52, 0x08, 0x73, 0x79, 0x6e, 0x63, 0x44, 0x61, 0x74, 0x61, 0x22, 0x4d, 0x0a, 0x11, 0x53,
	0x79, 0x6e, 0x63, 0x53, 0x70, 0x6f, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x73, 0x4e, 0x74, 0x66,
	0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1b, 0x0a,
	0x09, 0x73, 0x79, 0x6e, 0x63, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x08, 0x73, 0x79, 0x6e, 0x63, 0x44, 0x61, 0x74, 0x61, 0x22, 0x58, 0x0a, 0x0a, 0x53, 0x79,
	0x6e, 0x63, 0x50, 0x6f, 0x73, 0x52, 0x65, 0x71, 0x12, 0x17, 0x0a, 0x07, 0x72, 0x6f, 0x6f, 0x6d,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49,
	0x64, 0x12, 0x31, 0x0a, 0x08, 0x70, 0x6f, 0x73, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x6c, 0x61,
	0x79, 0x65, 0x72, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x70, 0x6f, 0x73,
	0x49, 0x6e, 0x66, 0x6f, 0x22, 0x41, 0x0a, 0x0c, 0x53, 0x79, 0x6e, 0x63, 0x50, 0x6f, 0x73, 0x42,
	0x73, 0x4e, 0x74, 0x66, 0x12, 0x31, 0x0a, 0x08, 0x70, 0x6f, 0x73, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x07,
	0x70, 0x6f, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x2e, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x52, 0x6f,
	0x6f, 0x6d, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x50, 0x6f, 0x73, 0x52, 0x65, 0x71, 0x12, 0x17,
	0x0a, 0x07, 0x72, 0x6f, 0x6f, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x64, 0x22, 0x46, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x52, 0x6f,
	0x6f, 0x6d, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x50, 0x6f, 0x73, 0x52, 0x73, 0x70, 0x12, 0x2f,
	0x0a, 0x07, 0x70, 0x6f, 0x73, 0x5f, 0x61, 0x72, 0x72, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x50,
	0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x06, 0x70, 0x6f, 0x73, 0x41, 0x72, 0x72, 0x42,
	0x3d, 0x5a, 0x3b, 0x67, 0x69, 0x74, 0x2e, 0x6b, 0x65, 0x65, 0x70, 0x66, 0x61, 0x6e, 0x63, 0x79,
	0x2e, 0x78, 0x79, 0x7a, 0x2f, 0x62, 0x61, 0x63, 0x6b, 0x2d, 0x65, 0x6e, 0x64, 0x2f, 0x66, 0x61,
	0x6e, 0x63, 0x79, 0x2d, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x63,
	0x70, 0x62, 0x2f, 0x73, 0x79, 0x6e, 0x63, 0x3b, 0x73, 0x79, 0x6e, 0x63, 0x50, 0x42, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_sync_proto_rawDescOnce sync.Once
	file_sync_proto_rawDescData = file_sync_proto_rawDesc
)

func file_sync_proto_rawDescGZIP() []byte {
	file_sync_proto_rawDescOnce.Do(func() {
		file_sync_proto_rawDescData = protoimpl.X.CompressGZIP(file_sync_proto_rawDescData)
	})
	return file_sync_proto_rawDescData
}

var file_sync_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_sync_proto_goTypes = []interface{}{
	(*SyncSpotInfoReq)(nil),       // 0: syncPB.SyncSpotInfoReq
	(*SyncSpotInfoBsNtf)(nil),     // 1: syncPB.SyncSpotInfoBsNtf
	(*SyncPosReq)(nil),            // 2: syncPB.SyncPosReq
	(*SyncPosBsNtf)(nil),          // 3: syncPB.SyncPosBsNtf
	(*GetRoomPlayerPosReq)(nil),   // 4: syncPB.GetRoomPlayerPosReq
	(*GetRoomPlayerPosRsp)(nil),   // 5: syncPB.GetRoomPlayerPosRsp
	(*common.PlayerPosition)(nil), // 6: common.PlayerPosition
}
var file_sync_proto_depIdxs = []int32{
	6, // 0: syncPB.SyncPosReq.pos_info:type_name -> common.PlayerPosition
	6, // 1: syncPB.SyncPosBsNtf.pos_info:type_name -> common.PlayerPosition
	6, // 2: syncPB.GetRoomPlayerPosRsp.pos_arr:type_name -> common.PlayerPosition
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_sync_proto_init() }
func file_sync_proto_init() {
	if File_sync_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_sync_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncSpotInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sync_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncSpotInfoBsNtf); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sync_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncPosReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sync_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SyncPosBsNtf); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sync_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRoomPlayerPosReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sync_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRoomPlayerPosRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_sync_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_sync_proto_goTypes,
		DependencyIndexes: file_sync_proto_depIdxs,
		MessageInfos:      file_sync_proto_msgTypes,
	}.Build()
	File_sync_proto = out.File
	file_sync_proto_rawDesc = nil
	file_sync_proto_goTypes = nil
	file_sync_proto_depIdxs = nil
}
