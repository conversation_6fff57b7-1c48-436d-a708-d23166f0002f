// 活动模块协议

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v5.27.0
// source: activity.proto

package activityPB

import (
	common "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 获取活动进度请求
type GetActivityProgressReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActivityId common.ACTIVITY_TYPE `protobuf:"varint,1,opt,name=activity_id,json=activityId,proto3,enum=common.ACTIVITY_TYPE" json:"activity_id,omitempty"` // 活动ID
}

func (x *GetActivityProgressReq) Reset() {
	*x = GetActivityProgressReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_activity_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetActivityProgressReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetActivityProgressReq) ProtoMessage() {}

func (x *GetActivityProgressReq) ProtoReflect() protoreflect.Message {
	mi := &file_activity_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetActivityProgressReq.ProtoReflect.Descriptor instead.
func (*GetActivityProgressReq) Descriptor() ([]byte, []int) {
	return file_activity_proto_rawDescGZIP(), []int{0}
}

func (x *GetActivityProgressReq) GetActivityId() common.ACTIVITY_TYPE {
	if x != nil {
		return x.ActivityId
	}
	return common.ACTIVITY_TYPE(0)
}

// 获取活动进度响应
type GetActivityProgressRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret                  *common.Result      `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`                                                                 // 结果
	ActivityProgressList []*ActivityProgress `protobuf:"bytes,2,rep,name=activity_progress_list,json=activityProgressList,proto3" json:"activity_progress_list,omitempty"` // 活动进度列表
}

func (x *GetActivityProgressRsp) Reset() {
	*x = GetActivityProgressRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_activity_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetActivityProgressRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetActivityProgressRsp) ProtoMessage() {}

func (x *GetActivityProgressRsp) ProtoReflect() protoreflect.Message {
	mi := &file_activity_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetActivityProgressRsp.ProtoReflect.Descriptor instead.
func (*GetActivityProgressRsp) Descriptor() ([]byte, []int) {
	return file_activity_proto_rawDescGZIP(), []int{1}
}

func (x *GetActivityProgressRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *GetActivityProgressRsp) GetActivityProgressList() []*ActivityProgress {
	if x != nil {
		return x.ActivityProgressList
	}
	return nil
}

// 领取活动奖励请求
type ClaimActivityRewardReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActivityId common.ACTIVITY_TYPE `protobuf:"varint,1,opt,name=activity_id,json=activityId,proto3,enum=common.ACTIVITY_TYPE" json:"activity_id,omitempty"` // 活动ID
	CycleId    int32                `protobuf:"varint,2,opt,name=cycle_id,json=cycleId,proto3" json:"cycle_id,omitempty"`                                    // 周期ID
}

func (x *ClaimActivityRewardReq) Reset() {
	*x = ClaimActivityRewardReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_activity_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClaimActivityRewardReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClaimActivityRewardReq) ProtoMessage() {}

func (x *ClaimActivityRewardReq) ProtoReflect() protoreflect.Message {
	mi := &file_activity_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClaimActivityRewardReq.ProtoReflect.Descriptor instead.
func (*ClaimActivityRewardReq) Descriptor() ([]byte, []int) {
	return file_activity_proto_rawDescGZIP(), []int{2}
}

func (x *ClaimActivityRewardReq) GetActivityId() common.ACTIVITY_TYPE {
	if x != nil {
		return x.ActivityId
	}
	return common.ACTIVITY_TYPE(0)
}

func (x *ClaimActivityRewardReq) GetCycleId() int32 {
	if x != nil {
		return x.CycleId
	}
	return 0
}

// 领取活动奖励响应
type ClaimActivityRewardRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret        *common.Result       `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`                                                            // 结果
	ActivityId common.ACTIVITY_TYPE `protobuf:"varint,2,opt,name=activity_id,json=activityId,proto3,enum=common.ACTIVITY_TYPE" json:"activity_id,omitempty"` // 活动ID
	StageId    []int32              `protobuf:"varint,3,rep,packed,name=stage_id,json=stageId,proto3" json:"stage_id,omitempty"`                             // 阶段ID
	CycleId    int32                `protobuf:"varint,4,opt,name=cycle_id,json=cycleId,proto3" json:"cycle_id,omitempty"`                                    // 周期ID
}

func (x *ClaimActivityRewardRsp) Reset() {
	*x = ClaimActivityRewardRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_activity_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClaimActivityRewardRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClaimActivityRewardRsp) ProtoMessage() {}

func (x *ClaimActivityRewardRsp) ProtoReflect() protoreflect.Message {
	mi := &file_activity_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClaimActivityRewardRsp.ProtoReflect.Descriptor instead.
func (*ClaimActivityRewardRsp) Descriptor() ([]byte, []int) {
	return file_activity_proto_rawDescGZIP(), []int{3}
}

func (x *ClaimActivityRewardRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *ClaimActivityRewardRsp) GetActivityId() common.ACTIVITY_TYPE {
	if x != nil {
		return x.ActivityId
	}
	return common.ACTIVITY_TYPE(0)
}

func (x *ClaimActivityRewardRsp) GetStageId() []int32 {
	if x != nil {
		return x.StageId
	}
	return nil
}

func (x *ClaimActivityRewardRsp) GetCycleId() int32 {
	if x != nil {
		return x.CycleId
	}
	return 0
}

type ActivityProgress struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActivityId     common.ACTIVITY_TYPE `protobuf:"varint,1,opt,name=activity_id,json=activityId,proto3,enum=common.ACTIVITY_TYPE" json:"activity_id,omitempty"`                                        // 活动ID
	CurrentCycleId int32                `protobuf:"varint,2,opt,name=current_cycle_id,json=currentCycleId,proto3" json:"current_cycle_id,omitempty"`                                                    // 当前周期ID
	CycleEndTime   int64                `protobuf:"varint,3,opt,name=cycle_end_time,json=cycleEndTime,proto3" json:"cycle_end_time,omitempty"`                                                          // 当前周期结束时间戳
	Metrics        map[int32]int64      `protobuf:"bytes,4,rep,name=metrics,proto3" json:"metrics,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` // 玩家指标列表
	ClaimedRecords []int32              `protobuf:"varint,5,rep,packed,name=claimed_records,json=claimedRecords,proto3" json:"claimed_records,omitempty"`                                               // 已领取阶段列表
}

func (x *ActivityProgress) Reset() {
	*x = ActivityProgress{}
	if protoimpl.UnsafeEnabled {
		mi := &file_activity_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ActivityProgress) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActivityProgress) ProtoMessage() {}

func (x *ActivityProgress) ProtoReflect() protoreflect.Message {
	mi := &file_activity_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActivityProgress.ProtoReflect.Descriptor instead.
func (*ActivityProgress) Descriptor() ([]byte, []int) {
	return file_activity_proto_rawDescGZIP(), []int{4}
}

func (x *ActivityProgress) GetActivityId() common.ACTIVITY_TYPE {
	if x != nil {
		return x.ActivityId
	}
	return common.ACTIVITY_TYPE(0)
}

func (x *ActivityProgress) GetCurrentCycleId() int32 {
	if x != nil {
		return x.CurrentCycleId
	}
	return 0
}

func (x *ActivityProgress) GetCycleEndTime() int64 {
	if x != nil {
		return x.CycleEndTime
	}
	return 0
}

func (x *ActivityProgress) GetMetrics() map[int32]int64 {
	if x != nil {
		return x.Metrics
	}
	return nil
}

func (x *ActivityProgress) GetClaimedRecords() []int32 {
	if x != nil {
		return x.ClaimedRecords
	}
	return nil
}

var File_activity_proto protoreflect.FileDescriptor

var file_activity_proto_rawDesc = []byte{
	0x0a, 0x0e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x50, 0x42, 0x1a, 0x0c, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0a, 0x65, 0x6e, 0x75, 0x6d,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x50, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71,
	0x12, 0x36, 0x0a, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x41,
	0x43, 0x54, 0x49, 0x56, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52, 0x0a, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x49, 0x64, 0x22, 0x8e, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x74,
	0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73,
	0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x52, 0x0a, 0x16, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x79, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79,
	0x50, 0x42, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x50, 0x72, 0x6f, 0x67, 0x72,
	0x65, 0x73, 0x73, 0x52, 0x14, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x50, 0x72, 0x6f,
	0x67, 0x72, 0x65, 0x73, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x6b, 0x0a, 0x16, 0x43, 0x6c, 0x61,
	0x69, 0x6d, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x52, 0x65, 0x71, 0x12, 0x36, 0x0a, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x41, 0x43, 0x54, 0x49, 0x56, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52,
	0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x63,
	0x79, 0x63, 0x6c, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x63,
	0x79, 0x63, 0x6c, 0x65, 0x49, 0x64, 0x22, 0xa8, 0x01, 0x0a, 0x16, 0x43, 0x6c, 0x61, 0x69, 0x6d,
	0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x73,
	0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03,
	0x72, 0x65, 0x74, 0x12, 0x36, 0x0a, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x41, 0x43, 0x54, 0x49, 0x56, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52,
	0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x73,
	0x74, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x03, 0x28, 0x05, 0x52, 0x07, 0x73,
	0x74, 0x61, 0x67, 0x65, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x49,
	0x64, 0x22, 0xc4, 0x02, 0x0a, 0x10, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x50, 0x72,
	0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x12, 0x36, 0x0a, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x41, 0x43, 0x54, 0x49, 0x56, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x49, 0x64, 0x12, 0x28,
	0x0a, 0x10, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x74, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0e, 0x63, 0x79, 0x63, 0x6c,
	0x65, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0c, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x43,
	0x0a, 0x07, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x29, 0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x50, 0x42, 0x2e, 0x41, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x2e, 0x4d, 0x65,
	0x74, 0x72, 0x69, 0x63, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x6d, 0x65, 0x74, 0x72,
	0x69, 0x63, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x65, 0x64, 0x5f, 0x72,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x05, 0x52, 0x0e, 0x63, 0x6c,
	0x61, 0x69, 0x6d, 0x65, 0x64, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x1a, 0x3a, 0x0a, 0x0c,
	0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x42, 0x45, 0x5a, 0x43, 0x67, 0x69, 0x74, 0x2e,
	0x6b, 0x65, 0x65, 0x70, 0x66, 0x61, 0x6e, 0x63, 0x79, 0x2e, 0x78, 0x79, 0x7a, 0x2f, 0x62, 0x61,
	0x63, 0x6b, 0x2d, 0x65, 0x6e, 0x64, 0x2f, 0x66, 0x61, 0x6e, 0x63, 0x79, 0x2d, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x63, 0x70, 0x62, 0x2f, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x69, 0x74, 0x79, 0x3b, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x50, 0x42, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_activity_proto_rawDescOnce sync.Once
	file_activity_proto_rawDescData = file_activity_proto_rawDesc
)

func file_activity_proto_rawDescGZIP() []byte {
	file_activity_proto_rawDescOnce.Do(func() {
		file_activity_proto_rawDescData = protoimpl.X.CompressGZIP(file_activity_proto_rawDescData)
	})
	return file_activity_proto_rawDescData
}

var file_activity_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_activity_proto_goTypes = []interface{}{
	(*GetActivityProgressReq)(nil), // 0: activityPB.GetActivityProgressReq
	(*GetActivityProgressRsp)(nil), // 1: activityPB.GetActivityProgressRsp
	(*ClaimActivityRewardReq)(nil), // 2: activityPB.ClaimActivityRewardReq
	(*ClaimActivityRewardRsp)(nil), // 3: activityPB.ClaimActivityRewardRsp
	(*ActivityProgress)(nil),       // 4: activityPB.ActivityProgress
	nil,                            // 5: activityPB.ActivityProgress.MetricsEntry
	(common.ACTIVITY_TYPE)(0),      // 6: common.ACTIVITY_TYPE
	(*common.Result)(nil),          // 7: common.Result
}
var file_activity_proto_depIdxs = []int32{
	6, // 0: activityPB.GetActivityProgressReq.activity_id:type_name -> common.ACTIVITY_TYPE
	7, // 1: activityPB.GetActivityProgressRsp.ret:type_name -> common.Result
	4, // 2: activityPB.GetActivityProgressRsp.activity_progress_list:type_name -> activityPB.ActivityProgress
	6, // 3: activityPB.ClaimActivityRewardReq.activity_id:type_name -> common.ACTIVITY_TYPE
	7, // 4: activityPB.ClaimActivityRewardRsp.ret:type_name -> common.Result
	6, // 5: activityPB.ClaimActivityRewardRsp.activity_id:type_name -> common.ACTIVITY_TYPE
	6, // 6: activityPB.ActivityProgress.activity_id:type_name -> common.ACTIVITY_TYPE
	5, // 7: activityPB.ActivityProgress.metrics:type_name -> activityPB.ActivityProgress.MetricsEntry
	8, // [8:8] is the sub-list for method output_type
	8, // [8:8] is the sub-list for method input_type
	8, // [8:8] is the sub-list for extension type_name
	8, // [8:8] is the sub-list for extension extendee
	0, // [0:8] is the sub-list for field type_name
}

func init() { file_activity_proto_init() }
func file_activity_proto_init() {
	if File_activity_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_activity_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetActivityProgressReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_activity_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetActivityProgressRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_activity_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClaimActivityRewardReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_activity_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClaimActivityRewardRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_activity_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ActivityProgress); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_activity_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_activity_proto_goTypes,
		DependencyIndexes: file_activity_proto_depIdxs,
		MessageInfos:      file_activity_proto_msgTypes,
	}.Build()
	File_activity_proto = out.File
	file_activity_proto_rawDesc = nil
	file_activity_proto_goTypes = nil
	file_activity_proto_depIdxs = nil
}
