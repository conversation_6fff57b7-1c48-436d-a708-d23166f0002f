// 登录模块协议

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v5.27.0
// source: task.proto

package taskPB

import (
	common "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 获取任务列表
type GetTaskListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Category common.TASK_CATEGORY `protobuf:"varint,1,opt,name=category,proto3,enum=common.TASK_CATEGORY" json:"category,omitempty"`
}

func (x *GetTaskListReq) Reset() {
	*x = GetTaskListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_task_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTaskListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTaskListReq) ProtoMessage() {}

func (x *GetTaskListReq) ProtoReflect() protoreflect.Message {
	mi := &file_task_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTaskListReq.ProtoReflect.Descriptor instead.
func (*GetTaskListReq) Descriptor() ([]byte, []int) {
	return file_task_proto_rawDescGZIP(), []int{0}
}

func (x *GetTaskListReq) GetCategory() common.TASK_CATEGORY {
	if x != nil {
		return x.Category
	}
	return common.TASK_CATEGORY(0)
}

type GetTaskListRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret      *common.Result       `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`
	TaskList []*common.TaskInfo   `protobuf:"bytes,2,rep,name=task_list,json=taskList,proto3" json:"task_list,omitempty"`
	Category common.TASK_CATEGORY `protobuf:"varint,3,opt,name=category,proto3,enum=common.TASK_CATEGORY" json:"category,omitempty"`
}

func (x *GetTaskListRsp) Reset() {
	*x = GetTaskListRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_task_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTaskListRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTaskListRsp) ProtoMessage() {}

func (x *GetTaskListRsp) ProtoReflect() protoreflect.Message {
	mi := &file_task_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTaskListRsp.ProtoReflect.Descriptor instead.
func (*GetTaskListRsp) Descriptor() ([]byte, []int) {
	return file_task_proto_rawDescGZIP(), []int{1}
}

func (x *GetTaskListRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *GetTaskListRsp) GetTaskList() []*common.TaskInfo {
	if x != nil {
		return x.TaskList
	}
	return nil
}

func (x *GetTaskListRsp) GetCategory() common.TASK_CATEGORY {
	if x != nil {
		return x.Category
	}
	return common.TASK_CATEGORY(0)
}

// 下发推送更新
type UpdateTaskNTF struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret  *common.Result     `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`
	Info []*common.TaskInfo `protobuf:"bytes,2,rep,name=info,proto3" json:"info,omitempty"` // 任务信息
}

func (x *UpdateTaskNTF) Reset() {
	*x = UpdateTaskNTF{}
	if protoimpl.UnsafeEnabled {
		mi := &file_task_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateTaskNTF) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateTaskNTF) ProtoMessage() {}

func (x *UpdateTaskNTF) ProtoReflect() protoreflect.Message {
	mi := &file_task_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateTaskNTF.ProtoReflect.Descriptor instead.
func (*UpdateTaskNTF) Descriptor() ([]byte, []int) {
	return file_task_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateTaskNTF) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *UpdateTaskNTF) GetInfo() []*common.TaskInfo {
	if x != nil {
		return x.Info
	}
	return nil
}

// 领取奖励
type RewardTaskReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId   int64                `protobuf:"varint,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"` // 任务id
	Category common.TASK_CATEGORY `protobuf:"varint,2,opt,name=category,proto3,enum=common.TASK_CATEGORY" json:"category,omitempty"`
}

func (x *RewardTaskReq) Reset() {
	*x = RewardTaskReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_task_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardTaskReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardTaskReq) ProtoMessage() {}

func (x *RewardTaskReq) ProtoReflect() protoreflect.Message {
	mi := &file_task_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardTaskReq.ProtoReflect.Descriptor instead.
func (*RewardTaskReq) Descriptor() ([]byte, []int) {
	return file_task_proto_rawDescGZIP(), []int{3}
}

func (x *RewardTaskReq) GetTaskId() int64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *RewardTaskReq) GetCategory() common.TASK_CATEGORY {
	if x != nil {
		return x.Category
	}
	return common.TASK_CATEGORY(0)
}

type RewardTaskRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret    *common.Result   `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`
	Info   *common.TaskInfo `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"` // 任务信息
	Reward *common.Reward   `protobuf:"bytes,3,opt,name=reward,proto3" json:"reward,omitempty"`
}

func (x *RewardTaskRsp) Reset() {
	*x = RewardTaskRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_task_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardTaskRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardTaskRsp) ProtoMessage() {}

func (x *RewardTaskRsp) ProtoReflect() protoreflect.Message {
	mi := &file_task_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardTaskRsp.ProtoReflect.Descriptor instead.
func (*RewardTaskRsp) Descriptor() ([]byte, []int) {
	return file_task_proto_rawDescGZIP(), []int{4}
}

func (x *RewardTaskRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *RewardTaskRsp) GetInfo() *common.TaskInfo {
	if x != nil {
		return x.Info
	}
	return nil
}

func (x *RewardTaskRsp) GetReward() *common.Reward {
	if x != nil {
		return x.Reward
	}
	return nil
}

// 获取奖励进度请求
type TaskProgressReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Category common.TASK_CATEGORY `protobuf:"varint,1,opt,name=category,proto3,enum=common.TASK_CATEGORY" json:"category,omitempty"` // 任务类型
}

func (x *TaskProgressReq) Reset() {
	*x = TaskProgressReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_task_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskProgressReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskProgressReq) ProtoMessage() {}

func (x *TaskProgressReq) ProtoReflect() protoreflect.Message {
	mi := &file_task_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskProgressReq.ProtoReflect.Descriptor instead.
func (*TaskProgressReq) Descriptor() ([]byte, []int) {
	return file_task_proto_rawDescGZIP(), []int{5}
}

func (x *TaskProgressReq) GetCategory() common.TASK_CATEGORY {
	if x != nil {
		return x.Category
	}
	return common.TASK_CATEGORY(0)
}

// 获取奖励进度响应
type TaskProgressRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret      *common.Result         `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`
	Category common.TASK_CATEGORY   `protobuf:"varint,2,opt,name=category,proto3,enum=common.TASK_CATEGORY" json:"category,omitempty"` // 任务类型
	List     []*common.TaskProgress `protobuf:"bytes,3,rep,name=list,proto3" json:"list,omitempty"`                                    // 进度数据
}

func (x *TaskProgressRsp) Reset() {
	*x = TaskProgressRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_task_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskProgressRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskProgressRsp) ProtoMessage() {}

func (x *TaskProgressRsp) ProtoReflect() protoreflect.Message {
	mi := &file_task_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskProgressRsp.ProtoReflect.Descriptor instead.
func (*TaskProgressRsp) Descriptor() ([]byte, []int) {
	return file_task_proto_rawDescGZIP(), []int{6}
}

func (x *TaskProgressRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *TaskProgressRsp) GetCategory() common.TASK_CATEGORY {
	if x != nil {
		return x.Category
	}
	return common.TASK_CATEGORY(0)
}

func (x *TaskProgressRsp) GetList() []*common.TaskProgress {
	if x != nil {
		return x.List
	}
	return nil
}

// 广播协议
type TaskProgressNTF struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Category common.TASK_CATEGORY   `protobuf:"varint,2,opt,name=category,proto3,enum=common.TASK_CATEGORY" json:"category,omitempty"` // 任务类型
	List     []*common.TaskProgress `protobuf:"bytes,3,rep,name=list,proto3" json:"list,omitempty"`                                    // 有变化的进度
}

func (x *TaskProgressNTF) Reset() {
	*x = TaskProgressNTF{}
	if protoimpl.UnsafeEnabled {
		mi := &file_task_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskProgressNTF) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskProgressNTF) ProtoMessage() {}

func (x *TaskProgressNTF) ProtoReflect() protoreflect.Message {
	mi := &file_task_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskProgressNTF.ProtoReflect.Descriptor instead.
func (*TaskProgressNTF) Descriptor() ([]byte, []int) {
	return file_task_proto_rawDescGZIP(), []int{7}
}

func (x *TaskProgressNTF) GetCategory() common.TASK_CATEGORY {
	if x != nil {
		return x.Category
	}
	return common.TASK_CATEGORY(0)
}

func (x *TaskProgressNTF) GetList() []*common.TaskProgress {
	if x != nil {
		return x.List
	}
	return nil
}

// 领取奖励进度请求
type TaskProgressRewardReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Category common.TASK_CATEGORY `protobuf:"varint,1,opt,name=category,proto3,enum=common.TASK_CATEGORY" json:"category,omitempty"` // 任务类型
	SubId    int64                `protobuf:"varint,2,opt,name=sub_id,json=subId,proto3" json:"sub_id,omitempty"`                    // 子类型
	Index    int64                `protobuf:"varint,3,opt,name=index,proto3" json:"index,omitempty"`                                 // 领取索引
}

func (x *TaskProgressRewardReq) Reset() {
	*x = TaskProgressRewardReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_task_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskProgressRewardReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskProgressRewardReq) ProtoMessage() {}

func (x *TaskProgressRewardReq) ProtoReflect() protoreflect.Message {
	mi := &file_task_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskProgressRewardReq.ProtoReflect.Descriptor instead.
func (*TaskProgressRewardReq) Descriptor() ([]byte, []int) {
	return file_task_proto_rawDescGZIP(), []int{8}
}

func (x *TaskProgressRewardReq) GetCategory() common.TASK_CATEGORY {
	if x != nil {
		return x.Category
	}
	return common.TASK_CATEGORY(0)
}

func (x *TaskProgressRewardReq) GetSubId() int64 {
	if x != nil {
		return x.SubId
	}
	return 0
}

func (x *TaskProgressRewardReq) GetIndex() int64 {
	if x != nil {
		return x.Index
	}
	return 0
}

// 领取奖励进度返回
type TaskProgressRewardRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret    *common.Result       `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`
	Info   *common.TaskProgress `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"`     // 进度信息
	Reward *common.Reward       `protobuf:"bytes,3,opt,name=reward,proto3" json:"reward,omitempty"` // 奖励信息
}

func (x *TaskProgressRewardRsp) Reset() {
	*x = TaskProgressRewardRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_task_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskProgressRewardRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskProgressRewardRsp) ProtoMessage() {}

func (x *TaskProgressRewardRsp) ProtoReflect() protoreflect.Message {
	mi := &file_task_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskProgressRewardRsp.ProtoReflect.Descriptor instead.
func (*TaskProgressRewardRsp) Descriptor() ([]byte, []int) {
	return file_task_proto_rawDescGZIP(), []int{9}
}

func (x *TaskProgressRewardRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *TaskProgressRewardRsp) GetInfo() *common.TaskProgress {
	if x != nil {
		return x.Info
	}
	return nil
}

func (x *TaskProgressRewardRsp) GetReward() *common.Reward {
	if x != nil {
		return x.Reward
	}
	return nil
}

// 获取奖励进度请求
type ProgressReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Category []common.PROGRESS_TYPE `protobuf:"varint,1,rep,packed,name=category,proto3,enum=common.PROGRESS_TYPE" json:"category,omitempty"` // 任务类型
}

func (x *ProgressReq) Reset() {
	*x = ProgressReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_task_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProgressReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProgressReq) ProtoMessage() {}

func (x *ProgressReq) ProtoReflect() protoreflect.Message {
	mi := &file_task_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProgressReq.ProtoReflect.Descriptor instead.
func (*ProgressReq) Descriptor() ([]byte, []int) {
	return file_task_proto_rawDescGZIP(), []int{10}
}

func (x *ProgressReq) GetCategory() []common.PROGRESS_TYPE {
	if x != nil {
		return x.Category
	}
	return nil
}

// 获取奖励进度响应
type ProgressRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret  *common.Result         `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`
	List []*common.ProgressInfo `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"` // 进度数据
}

func (x *ProgressRsp) Reset() {
	*x = ProgressRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_task_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProgressRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProgressRsp) ProtoMessage() {}

func (x *ProgressRsp) ProtoReflect() protoreflect.Message {
	mi := &file_task_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProgressRsp.ProtoReflect.Descriptor instead.
func (*ProgressRsp) Descriptor() ([]byte, []int) {
	return file_task_proto_rawDescGZIP(), []int{11}
}

func (x *ProgressRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *ProgressRsp) GetList() []*common.ProgressInfo {
	if x != nil {
		return x.List
	}
	return nil
}

// 广播协议
type ProgressNTF struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*common.ProgressInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"` // 有变化的进度
}

func (x *ProgressNTF) Reset() {
	*x = ProgressNTF{}
	if protoimpl.UnsafeEnabled {
		mi := &file_task_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProgressNTF) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProgressNTF) ProtoMessage() {}

func (x *ProgressNTF) ProtoReflect() protoreflect.Message {
	mi := &file_task_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProgressNTF.ProtoReflect.Descriptor instead.
func (*ProgressNTF) Descriptor() ([]byte, []int) {
	return file_task_proto_rawDescGZIP(), []int{12}
}

func (x *ProgressNTF) GetList() []*common.ProgressInfo {
	if x != nil {
		return x.List
	}
	return nil
}

// 领取奖励进度请求
type ProgressRewardReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Category common.PROGRESS_TYPE `protobuf:"varint,1,opt,name=category,proto3,enum=common.PROGRESS_TYPE" json:"category,omitempty"` // 任务类型
	SubId    int64                `protobuf:"varint,2,opt,name=sub_id,json=subId,proto3" json:"sub_id,omitempty"`                    // 子进度索引
	Index    int64                `protobuf:"varint,3,opt,name=index,proto3" json:"index,omitempty"`                                 // 领取索引
}

func (x *ProgressRewardReq) Reset() {
	*x = ProgressRewardReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_task_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProgressRewardReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProgressRewardReq) ProtoMessage() {}

func (x *ProgressRewardReq) ProtoReflect() protoreflect.Message {
	mi := &file_task_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProgressRewardReq.ProtoReflect.Descriptor instead.
func (*ProgressRewardReq) Descriptor() ([]byte, []int) {
	return file_task_proto_rawDescGZIP(), []int{13}
}

func (x *ProgressRewardReq) GetCategory() common.PROGRESS_TYPE {
	if x != nil {
		return x.Category
	}
	return common.PROGRESS_TYPE(0)
}

func (x *ProgressRewardReq) GetSubId() int64 {
	if x != nil {
		return x.SubId
	}
	return 0
}

func (x *ProgressRewardReq) GetIndex() int64 {
	if x != nil {
		return x.Index
	}
	return 0
}

// 领取奖励进度返回
type ProgressRewardRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret    *common.Result       `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`
	Info   *common.ProgressInfo `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"`     // 进度信息
	Reward *common.Reward       `protobuf:"bytes,3,opt,name=reward,proto3" json:"reward,omitempty"` // 奖励信息
}

func (x *ProgressRewardRsp) Reset() {
	*x = ProgressRewardRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_task_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProgressRewardRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProgressRewardRsp) ProtoMessage() {}

func (x *ProgressRewardRsp) ProtoReflect() protoreflect.Message {
	mi := &file_task_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProgressRewardRsp.ProtoReflect.Descriptor instead.
func (*ProgressRewardRsp) Descriptor() ([]byte, []int) {
	return file_task_proto_rawDescGZIP(), []int{14}
}

func (x *ProgressRewardRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *ProgressRewardRsp) GetInfo() *common.ProgressInfo {
	if x != nil {
		return x.Info
	}
	return nil
}

func (x *ProgressRewardRsp) GetReward() *common.Reward {
	if x != nil {
		return x.Reward
	}
	return nil
}

var File_task_proto protoreflect.FileDescriptor

var file_task_proto_rawDesc = []byte{
	0x0a, 0x0a, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x06, 0x74, 0x61,
	0x73, 0x6b, 0x50, 0x42, 0x1a, 0x0c, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x0c, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x0a, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x43, 0x0a, 0x0e,
	0x47, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x31,
	0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x41, 0x53, 0x4b, 0x5f, 0x43,
	0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x22, 0x94, 0x01, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x2d, 0x0a, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x74, 0x61, 0x73,
	0x6b, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x31, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x54, 0x41, 0x53, 0x4b, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x52, 0x08,
	0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x22, 0x57, 0x0a, 0x0d, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x4e, 0x54, 0x46, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x24, 0x0a, 0x04, 0x69,
	0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x69, 0x6e, 0x66,
	0x6f, 0x22, 0x5b, 0x0a, 0x0d, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x61, 0x73, 0x6b, 0x52,
	0x65, 0x71, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x08, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x41, 0x53, 0x4b, 0x5f, 0x43, 0x41, 0x54, 0x45,
	0x47, 0x4f, 0x52, 0x59, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x22, 0x7f,
	0x0a, 0x0d, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x73, 0x70, 0x12,
	0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65,
	0x74, 0x12, 0x24, 0x0a, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x12, 0x26, 0x0a, 0x06, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x06, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x22,
	0x44, 0x0a, 0x0f, 0x54, 0x61, 0x73, 0x6b, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x52,
	0x65, 0x71, 0x12, 0x31, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x41,
	0x53, 0x4b, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x52, 0x08, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x22, 0x90, 0x01, 0x0a, 0x0f, 0x54, 0x61, 0x73, 0x6b, 0x50, 0x72,
	0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x31, 0x0a, 0x08, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x41, 0x53, 0x4b, 0x5f, 0x43, 0x41, 0x54, 0x45,
	0x47, 0x4f, 0x52, 0x59, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x28,
	0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65,
	0x73, 0x73, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x6e, 0x0a, 0x0f, 0x54, 0x61, 0x73, 0x6b,
	0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x4e, 0x54, 0x46, 0x12, 0x31, 0x0a, 0x08, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x41, 0x53, 0x4b, 0x5f, 0x43, 0x41, 0x54, 0x45,
	0x47, 0x4f, 0x52, 0x59, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x28,
	0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65,
	0x73, 0x73, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x77, 0x0a, 0x15, 0x54, 0x61, 0x73, 0x6b,
	0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x65,
	0x71, 0x12, 0x31, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x41, 0x53,
	0x4b, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x12, 0x15, 0x0a, 0x06, 0x73, 0x75, 0x62, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x73, 0x75, 0x62, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x69,
	0x6e, 0x64, 0x65, 0x78, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65,
	0x78, 0x22, 0x8b, 0x01, 0x0a, 0x15, 0x54, 0x61, 0x73, 0x6b, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65,
	0x73, 0x73, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72,
	0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x28, 0x0a,
	0x04, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73,
	0x73, 0x52, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x12, 0x26, 0x0a, 0x06, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x06, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x22,
	0x40, 0x0a, 0x0b, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x12, 0x31,
	0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0e,
	0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45,
	0x53, 0x53, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x22, 0x59, 0x0a, 0x0b, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x52, 0x73, 0x70,
	0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72,
	0x65, 0x74, 0x12, 0x28, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65,
	0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x37, 0x0a, 0x0b,
	0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x4e, 0x54, 0x46, 0x12, 0x28, 0x0a, 0x04, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x73, 0x0a, 0x11, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73,
	0x73, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x12, 0x31, 0x0a, 0x08, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x15, 0x0a,
	0x06, 0x73, 0x75, 0x62, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x73,
	0x75, 0x62, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x22, 0x87, 0x01, 0x0a, 0x11, 0x50,
	0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x73, 0x70,
	0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72,
	0x65, 0x74, 0x12, 0x28, 0x0a, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65,
	0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x12, 0x26, 0x0a, 0x06,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x06, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x42, 0x3d, 0x5a, 0x3b, 0x67, 0x69, 0x74, 0x2e, 0x6b, 0x65, 0x65, 0x70,
	0x66, 0x61, 0x6e, 0x63, 0x79, 0x2e, 0x78, 0x79, 0x7a, 0x2f, 0x62, 0x61, 0x63, 0x6b, 0x2d, 0x65,
	0x6e, 0x64, 0x2f, 0x66, 0x61, 0x6e, 0x63, 0x79, 0x2d, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f,
	0x70, 0x6b, 0x67, 0x2f, 0x63, 0x70, 0x62, 0x2f, 0x74, 0x61, 0x73, 0x6b, 0x3b, 0x74, 0x61, 0x73,
	0x6b, 0x50, 0x42, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_task_proto_rawDescOnce sync.Once
	file_task_proto_rawDescData = file_task_proto_rawDesc
)

func file_task_proto_rawDescGZIP() []byte {
	file_task_proto_rawDescOnce.Do(func() {
		file_task_proto_rawDescData = protoimpl.X.CompressGZIP(file_task_proto_rawDescData)
	})
	return file_task_proto_rawDescData
}

var file_task_proto_msgTypes = make([]protoimpl.MessageInfo, 15)
var file_task_proto_goTypes = []interface{}{
	(*GetTaskListReq)(nil),        // 0: taskPB.GetTaskListReq
	(*GetTaskListRsp)(nil),        // 1: taskPB.GetTaskListRsp
	(*UpdateTaskNTF)(nil),         // 2: taskPB.UpdateTaskNTF
	(*RewardTaskReq)(nil),         // 3: taskPB.RewardTaskReq
	(*RewardTaskRsp)(nil),         // 4: taskPB.RewardTaskRsp
	(*TaskProgressReq)(nil),       // 5: taskPB.TaskProgressReq
	(*TaskProgressRsp)(nil),       // 6: taskPB.TaskProgressRsp
	(*TaskProgressNTF)(nil),       // 7: taskPB.TaskProgressNTF
	(*TaskProgressRewardReq)(nil), // 8: taskPB.TaskProgressRewardReq
	(*TaskProgressRewardRsp)(nil), // 9: taskPB.TaskProgressRewardRsp
	(*ProgressReq)(nil),           // 10: taskPB.ProgressReq
	(*ProgressRsp)(nil),           // 11: taskPB.ProgressRsp
	(*ProgressNTF)(nil),           // 12: taskPB.ProgressNTF
	(*ProgressRewardReq)(nil),     // 13: taskPB.ProgressRewardReq
	(*ProgressRewardRsp)(nil),     // 14: taskPB.ProgressRewardRsp
	(common.TASK_CATEGORY)(0),     // 15: common.TASK_CATEGORY
	(*common.Result)(nil),         // 16: common.Result
	(*common.TaskInfo)(nil),       // 17: common.TaskInfo
	(*common.Reward)(nil),         // 18: common.Reward
	(*common.TaskProgress)(nil),   // 19: common.TaskProgress
	(common.PROGRESS_TYPE)(0),     // 20: common.PROGRESS_TYPE
	(*common.ProgressInfo)(nil),   // 21: common.ProgressInfo
}
var file_task_proto_depIdxs = []int32{
	15, // 0: taskPB.GetTaskListReq.category:type_name -> common.TASK_CATEGORY
	16, // 1: taskPB.GetTaskListRsp.ret:type_name -> common.Result
	17, // 2: taskPB.GetTaskListRsp.task_list:type_name -> common.TaskInfo
	15, // 3: taskPB.GetTaskListRsp.category:type_name -> common.TASK_CATEGORY
	16, // 4: taskPB.UpdateTaskNTF.ret:type_name -> common.Result
	17, // 5: taskPB.UpdateTaskNTF.info:type_name -> common.TaskInfo
	15, // 6: taskPB.RewardTaskReq.category:type_name -> common.TASK_CATEGORY
	16, // 7: taskPB.RewardTaskRsp.ret:type_name -> common.Result
	17, // 8: taskPB.RewardTaskRsp.info:type_name -> common.TaskInfo
	18, // 9: taskPB.RewardTaskRsp.reward:type_name -> common.Reward
	15, // 10: taskPB.TaskProgressReq.category:type_name -> common.TASK_CATEGORY
	16, // 11: taskPB.TaskProgressRsp.ret:type_name -> common.Result
	15, // 12: taskPB.TaskProgressRsp.category:type_name -> common.TASK_CATEGORY
	19, // 13: taskPB.TaskProgressRsp.list:type_name -> common.TaskProgress
	15, // 14: taskPB.TaskProgressNTF.category:type_name -> common.TASK_CATEGORY
	19, // 15: taskPB.TaskProgressNTF.list:type_name -> common.TaskProgress
	15, // 16: taskPB.TaskProgressRewardReq.category:type_name -> common.TASK_CATEGORY
	16, // 17: taskPB.TaskProgressRewardRsp.ret:type_name -> common.Result
	19, // 18: taskPB.TaskProgressRewardRsp.info:type_name -> common.TaskProgress
	18, // 19: taskPB.TaskProgressRewardRsp.reward:type_name -> common.Reward
	20, // 20: taskPB.ProgressReq.category:type_name -> common.PROGRESS_TYPE
	16, // 21: taskPB.ProgressRsp.ret:type_name -> common.Result
	21, // 22: taskPB.ProgressRsp.list:type_name -> common.ProgressInfo
	21, // 23: taskPB.ProgressNTF.list:type_name -> common.ProgressInfo
	20, // 24: taskPB.ProgressRewardReq.category:type_name -> common.PROGRESS_TYPE
	16, // 25: taskPB.ProgressRewardRsp.ret:type_name -> common.Result
	21, // 26: taskPB.ProgressRewardRsp.info:type_name -> common.ProgressInfo
	18, // 27: taskPB.ProgressRewardRsp.reward:type_name -> common.Reward
	28, // [28:28] is the sub-list for method output_type
	28, // [28:28] is the sub-list for method input_type
	28, // [28:28] is the sub-list for extension type_name
	28, // [28:28] is the sub-list for extension extendee
	0,  // [0:28] is the sub-list for field type_name
}

func init() { file_task_proto_init() }
func file_task_proto_init() {
	if File_task_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_task_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTaskListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_task_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTaskListRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_task_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateTaskNTF); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_task_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardTaskReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_task_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardTaskRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_task_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskProgressReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_task_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskProgressRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_task_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskProgressNTF); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_task_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskProgressRewardReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_task_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskProgressRewardRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_task_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProgressReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_task_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProgressRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_task_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProgressNTF); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_task_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProgressRewardReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_task_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProgressRewardRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_task_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   15,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_task_proto_goTypes,
		DependencyIndexes: file_task_proto_depIdxs,
		MessageInfos:      file_task_proto_msgTypes,
	}.Build()
	File_task_proto = out.File
	file_task_proto_rawDesc = nil
	file_task_proto_goTypes = nil
	file_task_proto_depIdxs = nil
}
