// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v5.27.0
// source: common.proto

package commonPB

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 客户端更新
type AppUpdateInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Version       string `protobuf:"bytes,1,opt,name=version,proto3" json:"version,omitempty"`                                    // app当前版本
	IsPrompt      bool   `protobuf:"varint,2,opt,name=isPrompt,proto3" json:"isPrompt,omitempty"`                                 // 是否提示更新版本
	IsForce       bool   `protobuf:"varint,3,opt,name=is_force,json=isForce,proto3" json:"is_force,omitempty"`                    // 是否强制更新版本
	DownloadUrl   string `protobuf:"bytes,4,opt,name=download_url,json=downloadUrl,proto3" json:"download_url,omitempty"`         // APP下载地址
	TipMsgKey     string `protobuf:"bytes,5,opt,name=tip_msg_key,json=tipMsgKey,proto3" json:"tip_msg_key,omitempty"`             // 更新描述的KEY
	MinAppVersion string `protobuf:"bytes,6,opt,name=min_app_version,json=minAppVersion,proto3" json:"min_app_version,omitempty"` // 最小兼容版本[低于这个强更]
}

func (x *AppUpdateInfo) Reset() {
	*x = AppUpdateInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppUpdateInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppUpdateInfo) ProtoMessage() {}

func (x *AppUpdateInfo) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppUpdateInfo.ProtoReflect.Descriptor instead.
func (*AppUpdateInfo) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{0}
}

func (x *AppUpdateInfo) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *AppUpdateInfo) GetIsPrompt() bool {
	if x != nil {
		return x.IsPrompt
	}
	return false
}

func (x *AppUpdateInfo) GetIsForce() bool {
	if x != nil {
		return x.IsForce
	}
	return false
}

func (x *AppUpdateInfo) GetDownloadUrl() string {
	if x != nil {
		return x.DownloadUrl
	}
	return ""
}

func (x *AppUpdateInfo) GetTipMsgKey() string {
	if x != nil {
		return x.TipMsgKey
	}
	return ""
}

func (x *AppUpdateInfo) GetMinAppVersion() string {
	if x != nil {
		return x.MinAppVersion
	}
	return ""
}

// app资源信息
type AppResourceInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ConfigMd5   string `protobuf:"bytes,1,opt,name=config_md5,json=configMd5,proto3" json:"config_md5,omitempty"`        // 配置config_md5.json的md5值
	RemoteMd5   string `protobuf:"bytes,2,opt,name=remote_md5,json=remoteMd5,proto3" json:"remote_md5,omitempty"`        // 远程资源MD5值
	RemoteForce bool   `protobuf:"varint,3,opt,name=remote_force,json=remoteForce,proto3" json:"remote_force,omitempty"` // 远程资源是否强制更新
	PatchForce  bool   `protobuf:"varint,4,opt,name=patch_force,json=patchForce,proto3" json:"patch_force,omitempty"`    // 补丁是否只更新代码
}

func (x *AppResourceInfo) Reset() {
	*x = AppResourceInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppResourceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppResourceInfo) ProtoMessage() {}

func (x *AppResourceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppResourceInfo.ProtoReflect.Descriptor instead.
func (*AppResourceInfo) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{1}
}

func (x *AppResourceInfo) GetConfigMd5() string {
	if x != nil {
		return x.ConfigMd5
	}
	return ""
}

func (x *AppResourceInfo) GetRemoteMd5() string {
	if x != nil {
		return x.RemoteMd5
	}
	return ""
}

func (x *AppResourceInfo) GetRemoteForce() bool {
	if x != nil {
		return x.RemoteForce
	}
	return false
}

func (x *AppResourceInfo) GetPatchForce() bool {
	if x != nil {
		return x.PatchForce
	}
	return false
}

// app地址信息
type AppAddressInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SrvUri       []string `protobuf:"bytes,1,rep,name=srv_uri,json=srvUri,proto3" json:"srv_uri,omitempty"`                     // 服务器uri
	CdnHost      string   `protobuf:"bytes,2,opt,name=cdn_host,json=cdnHost,proto3" json:"cdn_host,omitempty"`                  // CDN地址
	ResourceUri  string   `protobuf:"bytes,3,opt,name=resource_uri,json=resourceUri,proto3" json:"resource_uri,omitempty"`      // 下载地址的路径，绝对路径  /cdn/fancy/asset_bundles/
	ConfigUri    string   `protobuf:"bytes,4,opt,name=config_uri,json=configUri,proto3" json:"config_uri,omitempty"`            // 配置中心路径 /cdn/fancy/config/
	FbShareUri   string   `protobuf:"bytes,5,opt,name=fb_share_uri,json=fbShareUri,proto3" json:"fb_share_uri,omitempty"`       // fb分享地址
	LogUploadUri string   `protobuf:"bytes,6,opt,name=log_upload_uri,json=logUploadUri,proto3" json:"log_upload_uri,omitempty"` // log上传地址
}

func (x *AppAddressInfo) Reset() {
	*x = AppAddressInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AppAddressInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppAddressInfo) ProtoMessage() {}

func (x *AppAddressInfo) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppAddressInfo.ProtoReflect.Descriptor instead.
func (*AppAddressInfo) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{2}
}

func (x *AppAddressInfo) GetSrvUri() []string {
	if x != nil {
		return x.SrvUri
	}
	return nil
}

func (x *AppAddressInfo) GetCdnHost() string {
	if x != nil {
		return x.CdnHost
	}
	return ""
}

func (x *AppAddressInfo) GetResourceUri() string {
	if x != nil {
		return x.ResourceUri
	}
	return ""
}

func (x *AppAddressInfo) GetConfigUri() string {
	if x != nil {
		return x.ConfigUri
	}
	return ""
}

func (x *AppAddressInfo) GetFbShareUri() string {
	if x != nil {
		return x.FbShareUri
	}
	return ""
}

func (x *AppAddressInfo) GetLogUploadUri() string {
	if x != nil {
		return x.LogUploadUri
	}
	return ""
}

// 第三方登录
type ThirdLoginInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpenId string `protobuf:"bytes,1,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"` // open_id (如google登录, 传入google账号)
	Token  string `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`                 // token (如facebook登录, 传入facebook的token)
	CodeId string `protobuf:"bytes,3,opt,name=code_id,json=codeId,proto3" json:"code_id,omitempty"` // code_id
}

func (x *ThirdLoginInfo) Reset() {
	*x = ThirdLoginInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ThirdLoginInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThirdLoginInfo) ProtoMessage() {}

func (x *ThirdLoginInfo) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThirdLoginInfo.ProtoReflect.Descriptor instead.
func (*ThirdLoginInfo) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{3}
}

func (x *ThirdLoginInfo) GetOpenId() string {
	if x != nil {
		return x.OpenId
	}
	return ""
}

func (x *ThirdLoginInfo) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *ThirdLoginInfo) GetCodeId() string {
	if x != nil {
		return x.CodeId
	}
	return ""
}

// 账号信息 (针对账号密码登录)
type AccountInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Account  string `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`   // 账号
	Password string `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"` // 密码
}

func (x *AccountInfo) Reset() {
	*x = AccountInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccountInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountInfo) ProtoMessage() {}

func (x *AccountInfo) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountInfo.ProtoReflect.Descriptor instead.
func (*AccountInfo) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{4}
}

func (x *AccountInfo) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *AccountInfo) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

// 玩家设备信息
type DeviceInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DeviceModel  string        `protobuf:"bytes,1,opt,name=device_model,json=deviceModel,proto3" json:"device_model,omitempty"`                            // 设备型号，eg: iphone X, huawei mate40
	DeviceBrand  string        `protobuf:"bytes,2,opt,name=device_brand,json=deviceBrand,proto3" json:"device_brand,omitempty"`                            // 设备品牌，eg: Apple, Huawei，Xiaomi
	Os           string        `protobuf:"bytes,3,opt,name=os,proto3" json:"os,omitempty"`                                                                 // 操作系统 ios14 / android10
	OsLanguage   string        `protobuf:"bytes,4,opt,name=os_language,json=osLanguage,proto3" json:"os_language,omitempty"`                               // os 语言 玩家设备设置的语言
	AppLanguage  LANGUAGE_TYPE `protobuf:"varint,5,opt,name=app_language,json=appLanguage,proto3,enum=common.LANGUAGE_TYPE" json:"app_language,omitempty"` // APP内设置语言，APP支持的国家化语言
	Resolution   string        `protobuf:"bytes,6,opt,name=resolution,proto3" json:"resolution,omitempty"`                                                 // 设备分辨率 (暂无使用)
	AdjustId     string        `protobuf:"bytes,7,opt,name=adjust_id,json=adjustId,proto3" json:"adjust_id,omitempty"`                                     // 设备 Adjust ID
	Idfa         string        `protobuf:"bytes,8,opt,name=idfa,proto3" json:"idfa,omitempty"`                                                             // iOS-设备广告ID
	TimeZone     string        `protobuf:"bytes,9,opt,name=time_zone,json=timeZone,proto3" json:"time_zone,omitempty"`                                     // 时区
	DeviceName   string        `protobuf:"bytes,10,opt,name=device_name,json=deviceName,proto3" json:"device_name,omitempty"`                              // 登录设备名
	DeviceCode   string        `protobuf:"bytes,11,opt,name=device_code,json=deviceCode,proto3" json:"device_code,omitempty"`                              // 设备唯一标识符
	Mvno         string        `protobuf:"bytes,12,opt,name=mvno,proto3" json:"mvno,omitempty"`                                                            // 运营商 (移动虚拟运营商 Mobile virtual network operator)
	Android      string        `protobuf:"bytes,13,opt,name=android,proto3" json:"android,omitempty"`                                                      // 安卓Id (暂无使用)
	Cpu          string        `protobuf:"bytes,14,opt,name=cpu,proto3" json:"cpu,omitempty"`                                                              // cpu  (暂无使用)
	Directx      string        `protobuf:"bytes,15,opt,name=directx,proto3" json:"directx,omitempty"`                                                      // 显卡 (暂无使用)
	Ram          string        `protobuf:"bytes,16,opt,name=ram,proto3" json:"ram,omitempty"`                                                              // 内存 (暂无使用)
	VideoAdapter string        `protobuf:"bytes,17,opt,name=video_adapter,json=videoAdapter,proto3" json:"video_adapter,omitempty"`                        // 显卡 (暂无使用)
	ClientIp     string        `protobuf:"bytes,18,opt,name=client_ip,json=clientIp,proto3" json:"client_ip,omitempty"`                                    // 客户端IP
}

func (x *DeviceInfo) Reset() {
	*x = DeviceInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeviceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceInfo) ProtoMessage() {}

func (x *DeviceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceInfo.ProtoReflect.Descriptor instead.
func (*DeviceInfo) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{5}
}

func (x *DeviceInfo) GetDeviceModel() string {
	if x != nil {
		return x.DeviceModel
	}
	return ""
}

func (x *DeviceInfo) GetDeviceBrand() string {
	if x != nil {
		return x.DeviceBrand
	}
	return ""
}

func (x *DeviceInfo) GetOs() string {
	if x != nil {
		return x.Os
	}
	return ""
}

func (x *DeviceInfo) GetOsLanguage() string {
	if x != nil {
		return x.OsLanguage
	}
	return ""
}

func (x *DeviceInfo) GetAppLanguage() LANGUAGE_TYPE {
	if x != nil {
		return x.AppLanguage
	}
	return LANGUAGE_TYPE_LT_INIT
}

func (x *DeviceInfo) GetResolution() string {
	if x != nil {
		return x.Resolution
	}
	return ""
}

func (x *DeviceInfo) GetAdjustId() string {
	if x != nil {
		return x.AdjustId
	}
	return ""
}

func (x *DeviceInfo) GetIdfa() string {
	if x != nil {
		return x.Idfa
	}
	return ""
}

func (x *DeviceInfo) GetTimeZone() string {
	if x != nil {
		return x.TimeZone
	}
	return ""
}

func (x *DeviceInfo) GetDeviceName() string {
	if x != nil {
		return x.DeviceName
	}
	return ""
}

func (x *DeviceInfo) GetDeviceCode() string {
	if x != nil {
		return x.DeviceCode
	}
	return ""
}

func (x *DeviceInfo) GetMvno() string {
	if x != nil {
		return x.Mvno
	}
	return ""
}

func (x *DeviceInfo) GetAndroid() string {
	if x != nil {
		return x.Android
	}
	return ""
}

func (x *DeviceInfo) GetCpu() string {
	if x != nil {
		return x.Cpu
	}
	return ""
}

func (x *DeviceInfo) GetDirectx() string {
	if x != nil {
		return x.Directx
	}
	return ""
}

func (x *DeviceInfo) GetRam() string {
	if x != nil {
		return x.Ram
	}
	return ""
}

func (x *DeviceInfo) GetVideoAdapter() string {
	if x != nil {
		return x.VideoAdapter
	}
	return ""
}

func (x *DeviceInfo) GetClientIp() string {
	if x != nil {
		return x.ClientIp
	}
	return ""
}

// 玩家全量信息 (外部包装信息不可外发给其他用户)
type RichUserInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BriefUserInfo  *BriefUserInfo  `protobuf:"bytes,1,opt,name=brief_user_info,json=briefUserInfo,proto3" json:"brief_user_info,omitempty"`                     // 玩家简要信息
	BanAccountInfo *BanAccountInfo `protobuf:"bytes,2,opt,name=ban_account_info,json=banAccountInfo,proto3" json:"ban_account_info,omitempty"`                  // 封号信息
	AppVersion     string          `protobuf:"bytes,3,opt,name=app_version,json=appVersion,proto3" json:"app_version,omitempty"`                                // 版本号
	AccType        ACC_TYPE        `protobuf:"varint,5,opt,name=acc_type,json=accType,proto3,enum=common.ACC_TYPE" json:"acc_type,omitempty"`                   // 账号类型，区别于登录类型
	RegisterTime   int64           `protobuf:"varint,7,opt,name=register_time,json=registerTime,proto3" json:"register_time,omitempty"`                         // 注册时间
	Platform       PLATFORM_TYPE   `protobuf:"varint,9,opt,name=platform,proto3,enum=common.PLATFORM_TYPE" json:"platform,omitempty"`                           // 平台类型
	AppLanguage    LANGUAGE_TYPE   `protobuf:"varint,12,opt,name=app_language,json=appLanguage,proto3,enum=common.LANGUAGE_TYPE" json:"app_language,omitempty"` // 多语言
	RealNameAuth   bool            `protobuf:"varint,13,opt,name=real_name_auth,json=realNameAuth,proto3" json:"real_name_auth,omitempty"`                      // 实名状态 TODO:delete
	ExtendUserInfo *ExtendUserInfo `protobuf:"bytes,14,opt,name=extend_user_info,json=extendUserInfo,proto3" json:"extend_user_info,omitempty"`                 // 玩家拓展信息
}

func (x *RichUserInfo) Reset() {
	*x = RichUserInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RichUserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RichUserInfo) ProtoMessage() {}

func (x *RichUserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RichUserInfo.ProtoReflect.Descriptor instead.
func (*RichUserInfo) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{6}
}

func (x *RichUserInfo) GetBriefUserInfo() *BriefUserInfo {
	if x != nil {
		return x.BriefUserInfo
	}
	return nil
}

func (x *RichUserInfo) GetBanAccountInfo() *BanAccountInfo {
	if x != nil {
		return x.BanAccountInfo
	}
	return nil
}

func (x *RichUserInfo) GetAppVersion() string {
	if x != nil {
		return x.AppVersion
	}
	return ""
}

func (x *RichUserInfo) GetAccType() ACC_TYPE {
	if x != nil {
		return x.AccType
	}
	return ACC_TYPE_AT_INIT
}

func (x *RichUserInfo) GetRegisterTime() int64 {
	if x != nil {
		return x.RegisterTime
	}
	return 0
}

func (x *RichUserInfo) GetPlatform() PLATFORM_TYPE {
	if x != nil {
		return x.Platform
	}
	return PLATFORM_TYPE_PT_INIT
}

func (x *RichUserInfo) GetAppLanguage() LANGUAGE_TYPE {
	if x != nil {
		return x.AppLanguage
	}
	return LANGUAGE_TYPE_LT_INIT
}

func (x *RichUserInfo) GetRealNameAuth() bool {
	if x != nil {
		return x.RealNameAuth
	}
	return false
}

func (x *RichUserInfo) GetExtendUserInfo() *ExtendUserInfo {
	if x != nil {
		return x.ExtendUserInfo
	}
	return nil
}

// 玩家简要信息 (公共通讯信息，如好友，公告等直接使用)
type BriefUserInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerId       uint64 `protobuf:"varint,1,opt,name=player_id,json=playerId,proto3" json:"player_id,omitempty"`                      // 玩家id
	Name           string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                                               // 名字
	Avatar         int64  `protobuf:"varint,3,opt,name=avatar,proto3" json:"avatar,omitempty"`                                          // 游戏内置头像编号
	AvatarUrl      string `protobuf:"bytes,4,opt,name=avatar_url,json=avatarUrl,proto3" json:"avatar_url,omitempty"`                    // 第三方头像地址
	Frame          int64  `protobuf:"varint,7,opt,name=frame,proto3" json:"frame,omitempty"`                                            // 游戏内头像框
	Lev            int32  `protobuf:"varint,8,opt,name=lev,proto3" json:"lev,omitempty"`                                                // 用户等级
	Country        string `protobuf:"bytes,9,opt,name=country,proto3" json:"country,omitempty"`                                         // 国家
	LastLoginTime  int64  `protobuf:"varint,10,opt,name=last_login_time,json=lastLoginTime,proto3" json:"last_login_time,omitempty"`    // 最后登录时间
	LastLogoutTime int64  `protobuf:"varint,11,opt,name=last_logout_time,json=lastLogoutTime,proto3" json:"last_logout_time,omitempty"` // 最后退出游戏时间
	LastDeviceCode string `protobuf:"bytes,12,opt,name=last_device_code,json=lastDeviceCode,proto3" json:"last_device_code,omitempty"`  // 上次登录设备码
}

func (x *BriefUserInfo) Reset() {
	*x = BriefUserInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BriefUserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BriefUserInfo) ProtoMessage() {}

func (x *BriefUserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BriefUserInfo.ProtoReflect.Descriptor instead.
func (*BriefUserInfo) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{7}
}

func (x *BriefUserInfo) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

func (x *BriefUserInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BriefUserInfo) GetAvatar() int64 {
	if x != nil {
		return x.Avatar
	}
	return 0
}

func (x *BriefUserInfo) GetAvatarUrl() string {
	if x != nil {
		return x.AvatarUrl
	}
	return ""
}

func (x *BriefUserInfo) GetFrame() int64 {
	if x != nil {
		return x.Frame
	}
	return 0
}

func (x *BriefUserInfo) GetLev() int32 {
	if x != nil {
		return x.Lev
	}
	return 0
}

func (x *BriefUserInfo) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *BriefUserInfo) GetLastLoginTime() int64 {
	if x != nil {
		return x.LastLoginTime
	}
	return 0
}

func (x *BriefUserInfo) GetLastLogoutTime() int64 {
	if x != nil {
		return x.LastLogoutTime
	}
	return 0
}

func (x *BriefUserInfo) GetLastDeviceCode() string {
	if x != nil {
		return x.LastDeviceCode
	}
	return ""
}

type RegionInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Country string `protobuf:"bytes,1,opt,name=Country,proto3" json:"Country,omitempty"` // 国家
	City    string `protobuf:"bytes,2,opt,name=City,proto3" json:"City,omitempty"`       // 城市
}

func (x *RegionInfo) Reset() {
	*x = RegionInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegionInfo) ProtoMessage() {}

func (x *RegionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegionInfo.ProtoReflect.Descriptor instead.
func (*RegionInfo) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{8}
}

func (x *RegionInfo) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *RegionInfo) GetCity() string {
	if x != nil {
		return x.City
	}
	return ""
}

// 玩家拓展信息
type ExtendUserInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NoviceGuide int32 `protobuf:"varint,1,opt,name=novice_guide,json=noviceGuide,proto3" json:"novice_guide,omitempty"` // 新手引导进度
}

func (x *ExtendUserInfo) Reset() {
	*x = ExtendUserInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExtendUserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExtendUserInfo) ProtoMessage() {}

func (x *ExtendUserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExtendUserInfo.ProtoReflect.Descriptor instead.
func (*ExtendUserInfo) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{9}
}

func (x *ExtendUserInfo) GetNoviceGuide() int32 {
	if x != nil {
		return x.NoviceGuide
	}
	return 0
}

// 封号信息
type BanAccountInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerId     uint64              `protobuf:"varint,1,opt,name=player_id,json=playerId,proto3" json:"player_id,omitempty"`                   // 玩家id
	BanReason    BAN_ACC_REASON_TYPE `protobuf:"varint,2,opt,name=BanReason,proto3,enum=common.BAN_ACC_REASON_TYPE" json:"BanReason,omitempty"` // 封禁原因
	BanLoginTime int64               `protobuf:"varint,3,opt,name=BanLoginTime,proto3" json:"BanLoginTime,omitempty"`                           // 用户封号的结束时间
}

func (x *BanAccountInfo) Reset() {
	*x = BanAccountInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BanAccountInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BanAccountInfo) ProtoMessage() {}

func (x *BanAccountInfo) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BanAccountInfo.ProtoReflect.Descriptor instead.
func (*BanAccountInfo) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{10}
}

func (x *BanAccountInfo) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

func (x *BanAccountInfo) GetBanReason() BAN_ACC_REASON_TYPE {
	if x != nil {
		return x.BanReason
	}
	return BAN_ACC_REASON_TYPE_BAR_TYPE_INIT
}

func (x *BanAccountInfo) GetBanLoginTime() int64 {
	if x != nil {
		return x.BanLoginTime
	}
	return 0
}

// 钓鱼佬
type Fisher struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BriefUserInfo *BriefUserInfo `protobuf:"bytes,1,opt,name=brief_user_info,json=briefUserInfo,proto3" json:"brief_user_info,omitempty"` // 玩家简要信息
	EnterCoin     uint64         `protobuf:"varint,2,opt,name=enter_coin,json=enterCoin,proto3" json:"enter_coin,omitempty"`              // 携带金币
}

func (x *Fisher) Reset() {
	*x = Fisher{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Fisher) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Fisher) ProtoMessage() {}

func (x *Fisher) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Fisher.ProtoReflect.Descriptor instead.
func (*Fisher) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{11}
}

func (x *Fisher) GetBriefUserInfo() *BriefUserInfo {
	if x != nil {
		return x.BriefUserInfo
	}
	return nil
}

func (x *Fisher) GetEnterCoin() uint64 {
	if x != nil {
		return x.EnterCoin
	}
	return 0
}

// 房间信息
type RoomInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RoomId   string    `protobuf:"bytes,1,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`                              // 房间id
	RoomType ROOM_TYPE `protobuf:"varint,2,opt,name=room_type,json=roomType,proto3,enum=common.ROOM_TYPE" json:"room_type,omitempty"` // 房间类型
	PondId   int64     `protobuf:"varint,3,opt,name=pond_id,json=pondId,proto3" json:"pond_id,omitempty"`                             // 钓场id
	RoomCode uint32    `protobuf:"varint,4,opt,name=room_code,json=roomCode,proto3" json:"room_code,omitempty"`                       // 房间编号，客户端输入用这个字段
	SpotId   int32     `protobuf:"varint,5,opt,name=spot_id,json=spotId,proto3" json:"spot_id,omitempty"`                             // 钓点id
}

func (x *RoomInfo) Reset() {
	*x = RoomInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RoomInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RoomInfo) ProtoMessage() {}

func (x *RoomInfo) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RoomInfo.ProtoReflect.Descriptor instead.
func (*RoomInfo) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{12}
}

func (x *RoomInfo) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *RoomInfo) GetRoomType() ROOM_TYPE {
	if x != nil {
		return x.RoomType
	}
	return ROOM_TYPE_RT_UNKNOWN
}

func (x *RoomInfo) GetPondId() int64 {
	if x != nil {
		return x.PondId
	}
	return 0
}

func (x *RoomInfo) GetRoomCode() uint32 {
	if x != nil {
		return x.RoomCode
	}
	return 0
}

func (x *RoomInfo) GetSpotId() int32 {
	if x != nil {
		return x.SpotId
	}
	return 0
}

// 坐标信息
type Position struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PosX float32 `protobuf:"fixed32,1,opt,name=pos_x,json=posX,proto3" json:"pos_x,omitempty"` // x坐标
	PosY float32 `protobuf:"fixed32,2,opt,name=pos_y,json=posY,proto3" json:"pos_y,omitempty"` // y坐标
	PosZ float32 `protobuf:"fixed32,3,opt,name=pos_z,json=posZ,proto3" json:"pos_z,omitempty"` // z坐标 水深(单位:米)
}

func (x *Position) Reset() {
	*x = Position{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Position) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Position) ProtoMessage() {}

func (x *Position) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Position.ProtoReflect.Descriptor instead.
func (*Position) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{13}
}

func (x *Position) GetPosX() float32 {
	if x != nil {
		return x.PosX
	}
	return 0
}

func (x *Position) GetPosY() float32 {
	if x != nil {
		return x.PosY
	}
	return 0
}

func (x *Position) GetPosZ() float32 {
	if x != nil {
		return x.PosZ
	}
	return 0
}

// 方向
type Direction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DirX float32 `protobuf:"fixed32,1,opt,name=dir_x,json=dirX,proto3" json:"dir_x,omitempty"` // x分量
	DirY float32 `protobuf:"fixed32,2,opt,name=dir_y,json=dirY,proto3" json:"dir_y,omitempty"` // y分量
}

func (x *Direction) Reset() {
	*x = Direction{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Direction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Direction) ProtoMessage() {}

func (x *Direction) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Direction.ProtoReflect.Descriptor instead.
func (*Direction) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{14}
}

func (x *Direction) GetDirX() float32 {
	if x != nil {
		return x.DirX
	}
	return 0
}

func (x *Direction) GetDirY() float32 {
	if x != nil {
		return x.DirY
	}
	return 0
}

// 玩家钓点同步信息
type FisherSpotSync struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerId   uint64      `protobuf:"varint,1,opt,name=player_id,json=playerId,proto3" json:"player_id,omitempty"`                               // 玩家id
	SyncData   []byte      `protobuf:"bytes,2,opt,name=sync_data,json=syncData,proto3" json:"sync_data,omitempty"`                                // 同步信息
	SyncNum    uint64      `protobuf:"varint,3,opt,name=sync_num,json=syncNum,proto3" json:"sync_num,omitempty"`                                  // 同步次数
	FishStatus FISH_STATUS `protobuf:"varint,4,opt,name=fish_status,json=fishStatus,proto3,enum=common.FISH_STATUS" json:"fish_status,omitempty"` // 钓鱼状态
}

func (x *FisherSpotSync) Reset() {
	*x = FisherSpotSync{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FisherSpotSync) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FisherSpotSync) ProtoMessage() {}

func (x *FisherSpotSync) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FisherSpotSync.ProtoReflect.Descriptor instead.
func (*FisherSpotSync) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{15}
}

func (x *FisherSpotSync) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

func (x *FisherSpotSync) GetSyncData() []byte {
	if x != nil {
		return x.SyncData
	}
	return nil
}

func (x *FisherSpotSync) GetSyncNum() uint64 {
	if x != nil {
		return x.SyncNum
	}
	return 0
}

func (x *FisherSpotSync) GetFishStatus() FISH_STATUS {
	if x != nil {
		return x.FishStatus
	}
	return FISH_STATUS_FS_UNKNOWN
}

// 鱼信息
type FishInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FishId      int64  `protobuf:"varint,1,opt,name=fish_id,json=fishId,proto3" json:"fish_id,omitempty"`                 // 鱼id
	Name        string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                                    // 鱼名称
	Length      int32  `protobuf:"varint,3,opt,name=length,proto3" json:"length,omitempty"`                               // 鱼长度(cm)
	Weight      int32  `protobuf:"varint,4,opt,name=weight,proto3" json:"weight,omitempty"`                               // 鱼重量(g)
	AwardNum    int32  `protobuf:"varint,5,opt,name=award_num,json=awardNum,proto3" json:"award_num,omitempty"`           // 奖励数量
	Exp         int32  `protobuf:"varint,6,opt,name=exp,proto3" json:"exp,omitempty"`                                     // 鱼经验
	Special     int64  `protobuf:"varint,7,opt,name=special,proto3" json:"special,omitempty"`                             // 品种
	Somatotype  int32  `protobuf:"varint,8,opt,name=somatotype,proto3" json:"somatotype,omitempty"`                       // 体型
	Quality     int32  `protobuf:"varint,9,opt,name=quality,proto3" json:"quality,omitempty"`                             // 品质
	Genus       int32  `protobuf:"varint,10,opt,name=genus,proto3" json:"genus,omitempty"`                                // 属科
	UnhookValue int32  `protobuf:"varint,11,opt,name=unhook_value,json=unhookValue,proto3" json:"unhook_value,omitempty"` // 鱼的脱钩值
}

func (x *FishInfo) Reset() {
	*x = FishInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FishInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FishInfo) ProtoMessage() {}

func (x *FishInfo) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FishInfo.ProtoReflect.Descriptor instead.
func (*FishInfo) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{16}
}

func (x *FishInfo) GetFishId() int64 {
	if x != nil {
		return x.FishId
	}
	return 0
}

func (x *FishInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *FishInfo) GetLength() int32 {
	if x != nil {
		return x.Length
	}
	return 0
}

func (x *FishInfo) GetWeight() int32 {
	if x != nil {
		return x.Weight
	}
	return 0
}

func (x *FishInfo) GetAwardNum() int32 {
	if x != nil {
		return x.AwardNum
	}
	return 0
}

func (x *FishInfo) GetExp() int32 {
	if x != nil {
		return x.Exp
	}
	return 0
}

func (x *FishInfo) GetSpecial() int64 {
	if x != nil {
		return x.Special
	}
	return 0
}

func (x *FishInfo) GetSomatotype() int32 {
	if x != nil {
		return x.Somatotype
	}
	return 0
}

func (x *FishInfo) GetQuality() int32 {
	if x != nil {
		return x.Quality
	}
	return 0
}

func (x *FishInfo) GetGenus() int32 {
	if x != nil {
		return x.Genus
	}
	return 0
}

func (x *FishInfo) GetUnhookValue() int32 {
	if x != nil {
		return x.UnhookValue
	}
	return 0
}

// 鱼详细信息
type FishDetailInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InstanceId      string           `protobuf:"bytes,1,opt,name=instance_id,json=instanceId,proto3" json:"instance_id,omitempty"`                  // 实例id
	Freshness       int32            `protobuf:"varint,2,opt,name=freshness,proto3" json:"freshness,omitempty"`                                     // 新鲜度
	HookTime        int64            `protobuf:"varint,3,opt,name=hook_time,json=hookTime,proto3" json:"hook_time,omitempty"`                       // 钓上时间(时间戳)
	AwardPer        float32          `protobuf:"fixed32,4,opt,name=award_per,json=awardPer,proto3" json:"award_per,omitempty"`                      // 奖励百分比
	FishInfo        *FishInfo        `protobuf:"bytes,5,opt,name=fish_info,json=fishInfo,proto3" json:"fish_info,omitempty"`                        // 鱼信息
	IsFirst         bool             `protobuf:"varint,6,opt,name=is_first,json=isFirst,proto3" json:"is_first,omitempty"`                          // 是否第一次
	BaitId          int64            `protobuf:"varint,7,opt,name=bait_id,json=baitId,proto3" json:"bait_id,omitempty"`                             // 钓饵id
	FishDamagedInfo *FishDamagedInfo `protobuf:"bytes,8,opt,name=fish_damaged_info,json=fishDamagedInfo,proto3" json:"fish_damaged_info,omitempty"` // 鱼破损信息
}

func (x *FishDetailInfo) Reset() {
	*x = FishDetailInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FishDetailInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FishDetailInfo) ProtoMessage() {}

func (x *FishDetailInfo) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FishDetailInfo.ProtoReflect.Descriptor instead.
func (*FishDetailInfo) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{17}
}

func (x *FishDetailInfo) GetInstanceId() string {
	if x != nil {
		return x.InstanceId
	}
	return ""
}

func (x *FishDetailInfo) GetFreshness() int32 {
	if x != nil {
		return x.Freshness
	}
	return 0
}

func (x *FishDetailInfo) GetHookTime() int64 {
	if x != nil {
		return x.HookTime
	}
	return 0
}

func (x *FishDetailInfo) GetAwardPer() float32 {
	if x != nil {
		return x.AwardPer
	}
	return 0
}

func (x *FishDetailInfo) GetFishInfo() *FishInfo {
	if x != nil {
		return x.FishInfo
	}
	return nil
}

func (x *FishDetailInfo) GetIsFirst() bool {
	if x != nil {
		return x.IsFirst
	}
	return false
}

func (x *FishDetailInfo) GetBaitId() int64 {
	if x != nil {
		return x.BaitId
	}
	return 0
}

func (x *FishDetailInfo) GetFishDamagedInfo() *FishDamagedInfo {
	if x != nil {
		return x.FishDamagedInfo
	}
	return nil
}

// 中鱼同步控制
type FishSyncControl struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IntervalTime int64 `protobuf:"varint,1,opt,name=interval_time,json=intervalTime,proto3" json:"interval_time,omitempty"` // 请求间隔时间(毫秒)
	StopTime     int64 `protobuf:"varint,2,opt,name=stop_time,json=stopTime,proto3" json:"stop_time,omitempty"`             // 结束时间(毫秒) 超过该时间就自动结束
}

func (x *FishSyncControl) Reset() {
	*x = FishSyncControl{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FishSyncControl) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FishSyncControl) ProtoMessage() {}

func (x *FishSyncControl) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FishSyncControl.ProtoReflect.Descriptor instead.
func (*FishSyncControl) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{18}
}

func (x *FishSyncControl) GetIntervalTime() int64 {
	if x != nil {
		return x.IntervalTime
	}
	return 0
}

func (x *FishSyncControl) GetStopTime() int64 {
	if x != nil {
		return x.StopTime
	}
	return 0
}

// 中鱼饵姿态习性参数
type HookBaitTypePose struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PoseType FISHING_BAIT_TRICK_TYPE `protobuf:"varint,1,opt,name=pose_type,json=poseType,proto3,enum=common.FISHING_BAIT_TRICK_TYPE" json:"pose_type,omitempty"` // 姿态类型
	Score    int32                   `protobuf:"varint,2,opt,name=score,proto3" json:"score,omitempty"`                                                           // 分数
}

func (x *HookBaitTypePose) Reset() {
	*x = HookBaitTypePose{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HookBaitTypePose) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HookBaitTypePose) ProtoMessage() {}

func (x *HookBaitTypePose) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HookBaitTypePose.ProtoReflect.Descriptor instead.
func (*HookBaitTypePose) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{19}
}

func (x *HookBaitTypePose) GetPoseType() FISHING_BAIT_TRICK_TYPE {
	if x != nil {
		return x.PoseType
	}
	return FISHING_BAIT_TRICK_TYPE_FBTT_NONE
}

func (x *HookBaitTypePose) GetScore() int32 {
	if x != nil {
		return x.Score
	}
	return 0
}

// 中鱼请求时的习性参数
type HookHabitParam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WaterTemp     int32                   `protobuf:"varint,1,opt,name=water_temp,json=waterTemp,proto3" json:"water_temp,omitempty"`                                                      // 水温
	LayerList     []MAP_WATER_LAYER_TYPE  `protobuf:"varint,2,rep,packed,name=layer_list,json=layerList,proto3,enum=common.MAP_WATER_LAYER_TYPE" json:"layer_list,omitempty"`              // 水层列表
	StructureList []UNDER_WATER_STRUCTURE `protobuf:"varint,3,rep,packed,name=structure_list,json=structureList,proto3,enum=common.UNDER_WATER_STRUCTURE" json:"structure_list,omitempty"` // 结构列表
	BaitPoseInfo  *HookBaitTypePose       `protobuf:"bytes,4,opt,name=bait_pose_info,json=baitPoseInfo,proto3" json:"bait_pose_info,omitempty"`                                            // 饵姿态信息
	LogLight      float64                 `protobuf:"fixed64,5,opt,name=log_light,json=logLight,proto3" json:"log_light,omitempty"`                                                        // 日照强度
}

func (x *HookHabitParam) Reset() {
	*x = HookHabitParam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HookHabitParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HookHabitParam) ProtoMessage() {}

func (x *HookHabitParam) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HookHabitParam.ProtoReflect.Descriptor instead.
func (*HookHabitParam) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{20}
}

func (x *HookHabitParam) GetWaterTemp() int32 {
	if x != nil {
		return x.WaterTemp
	}
	return 0
}

func (x *HookHabitParam) GetLayerList() []MAP_WATER_LAYER_TYPE {
	if x != nil {
		return x.LayerList
	}
	return nil
}

func (x *HookHabitParam) GetStructureList() []UNDER_WATER_STRUCTURE {
	if x != nil {
		return x.StructureList
	}
	return nil
}

func (x *HookHabitParam) GetBaitPoseInfo() *HookBaitTypePose {
	if x != nil {
		return x.BaitPoseInfo
	}
	return nil
}

func (x *HookHabitParam) GetLogLight() float64 {
	if x != nil {
		return x.LogLight
	}
	return 0
}

// 时间
type TimeOfDay struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Hour   int32 `protobuf:"varint,1,opt,name=hour,proto3" json:"hour,omitempty"`     // 时
	Minute int32 `protobuf:"varint,2,opt,name=minute,proto3" json:"minute,omitempty"` // 分
}

func (x *TimeOfDay) Reset() {
	*x = TimeOfDay{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimeOfDay) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimeOfDay) ProtoMessage() {}

func (x *TimeOfDay) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimeOfDay.ProtoReflect.Descriptor instead.
func (*TimeOfDay) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{21}
}

func (x *TimeOfDay) GetHour() int32 {
	if x != nil {
		return x.Hour
	}
	return 0
}

func (x *TimeOfDay) GetMinute() int32 {
	if x != nil {
		return x.Minute
	}
	return 0
}

// 抛竿格子信息
type ThrowGridInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pos          *Position       `protobuf:"bytes,1,opt,name=pos,proto3" json:"pos,omitempty"`                                                                  // 坐标
	AreaType     WATER_AREA_TYPE `protobuf:"varint,2,opt,name=area_type,json=areaType,proto3,enum=common.WATER_AREA_TYPE" json:"area_type,omitempty"`           // 水域类型
	ObstacleType OBSTACLE_TYPE   `protobuf:"varint,3,opt,name=obstacle_type,json=obstacleType,proto3,enum=common.OBSTACLE_TYPE" json:"obstacle_type,omitempty"` // 障碍物类型
}

func (x *ThrowGridInfo) Reset() {
	*x = ThrowGridInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ThrowGridInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThrowGridInfo) ProtoMessage() {}

func (x *ThrowGridInfo) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThrowGridInfo.ProtoReflect.Descriptor instead.
func (*ThrowGridInfo) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{22}
}

func (x *ThrowGridInfo) GetPos() *Position {
	if x != nil {
		return x.Pos
	}
	return nil
}

func (x *ThrowGridInfo) GetAreaType() WATER_AREA_TYPE {
	if x != nil {
		return x.AreaType
	}
	return WATER_AREA_TYPE_WAT_UNKNOWN
}

func (x *ThrowGridInfo) GetObstacleType() OBSTACLE_TYPE {
	if x != nil {
		return x.ObstacleType
	}
	return OBSTACLE_TYPE_OT_NONE
}

// 钓场玩家信息
type FisheryPlayerInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserInfo   *BriefUserInfo `protobuf:"bytes,1,opt,name=user_info,json=userInfo,proto3" json:"user_info,omitempty"`        // 玩家信息
	FishWeight int32          `protobuf:"varint,2,opt,name=fish_weight,json=fishWeight,proto3" json:"fish_weight,omitempty"` // 鱼护重鱼重量(g)
	SpotId     int32          `protobuf:"varint,3,opt,name=spot_id,json=spotId,proto3" json:"spot_id,omitempty"`             // 钓点id
	ExpLevel   int32          `protobuf:"varint,4,opt,name=exp_level,json=expLevel,proto3" json:"exp_level,omitempty"`       // 经验等级
	Energy     int32          `protobuf:"varint,5,opt,name=energy,proto3" json:"energy,omitempty"`                           // 体力值
}

func (x *FisheryPlayerInfo) Reset() {
	*x = FisheryPlayerInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FisheryPlayerInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FisheryPlayerInfo) ProtoMessage() {}

func (x *FisheryPlayerInfo) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FisheryPlayerInfo.ProtoReflect.Descriptor instead.
func (*FisheryPlayerInfo) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{23}
}

func (x *FisheryPlayerInfo) GetUserInfo() *BriefUserInfo {
	if x != nil {
		return x.UserInfo
	}
	return nil
}

func (x *FisheryPlayerInfo) GetFishWeight() int32 {
	if x != nil {
		return x.FishWeight
	}
	return 0
}

func (x *FisheryPlayerInfo) GetSpotId() int32 {
	if x != nil {
		return x.SpotId
	}
	return 0
}

func (x *FisheryPlayerInfo) GetExpLevel() int32 {
	if x != nil {
		return x.ExpLevel
	}
	return 0
}

func (x *FisheryPlayerInfo) GetEnergy() int32 {
	if x != nil {
		return x.Energy
	}
	return 0
}

// 玩家基础信息
type PlayerBaseInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerId uint64 `protobuf:"varint,1,opt,name=player_id,json=playerId,proto3" json:"player_id,omitempty"` // 玩家id
	ExpNum   int64  `protobuf:"varint,2,opt,name=exp_num,json=expNum,proto3" json:"exp_num,omitempty"`       // 经验值
	Coins    int64  `protobuf:"varint,3,opt,name=coins,proto3" json:"coins,omitempty"`                       // 金币
	Diamond  int64  `protobuf:"varint,4,opt,name=diamond,proto3" json:"diamond,omitempty"`                   // 钻石
	ExpLevel int32  `protobuf:"varint,5,opt,name=expLevel,proto3" json:"expLevel,omitempty"`                 // 经验等级
	Name     string `protobuf:"bytes,6,opt,name=name,proto3" json:"name,omitempty"`                          // 用户名
	Avatar   int64  `protobuf:"varint,7,opt,name=avatar,proto3" json:"avatar,omitempty"`                     // 头像
	Frame    int64  `protobuf:"varint,8,opt,name=frame,proto3" json:"frame,omitempty"`                       // 头像框
	Country  int64  `protobuf:"varint,9,opt,name=country,proto3" json:"country,omitempty"`                   // 地区
	ShowId   string `protobuf:"bytes,10,opt,name=show_id,json=showId,proto3" json:"show_id,omitempty"`       // showId
}

func (x *PlayerBaseInfo) Reset() {
	*x = PlayerBaseInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlayerBaseInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlayerBaseInfo) ProtoMessage() {}

func (x *PlayerBaseInfo) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlayerBaseInfo.ProtoReflect.Descriptor instead.
func (*PlayerBaseInfo) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{24}
}

func (x *PlayerBaseInfo) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

func (x *PlayerBaseInfo) GetExpNum() int64 {
	if x != nil {
		return x.ExpNum
	}
	return 0
}

func (x *PlayerBaseInfo) GetCoins() int64 {
	if x != nil {
		return x.Coins
	}
	return 0
}

func (x *PlayerBaseInfo) GetDiamond() int64 {
	if x != nil {
		return x.Diamond
	}
	return 0
}

func (x *PlayerBaseInfo) GetExpLevel() int32 {
	if x != nil {
		return x.ExpLevel
	}
	return 0
}

func (x *PlayerBaseInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PlayerBaseInfo) GetAvatar() int64 {
	if x != nil {
		return x.Avatar
	}
	return 0
}

func (x *PlayerBaseInfo) GetFrame() int64 {
	if x != nil {
		return x.Frame
	}
	return 0
}

func (x *PlayerBaseInfo) GetCountry() int64 {
	if x != nil {
		return x.Country
	}
	return 0
}

func (x *PlayerBaseInfo) GetShowId() string {
	if x != nil {
		return x.ShowId
	}
	return ""
}

// 经验等级变化信息
type ExpLevelChangeInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExpLevel int32       `protobuf:"varint,1,opt,name=exp_level,json=expLevel,proto3" json:"exp_level,omitempty"` // 经验等级
	ItemList []*ItemBase `protobuf:"bytes,2,rep,name=item_list,json=itemList,proto3" json:"item_list,omitempty"`  // 奖励道具列表
}

func (x *ExpLevelChangeInfo) Reset() {
	*x = ExpLevelChangeInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpLevelChangeInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpLevelChangeInfo) ProtoMessage() {}

func (x *ExpLevelChangeInfo) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpLevelChangeInfo.ProtoReflect.Descriptor instead.
func (*ExpLevelChangeInfo) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{25}
}

func (x *ExpLevelChangeInfo) GetExpLevel() int32 {
	if x != nil {
		return x.ExpLevel
	}
	return 0
}

func (x *ExpLevelChangeInfo) GetItemList() []*ItemBase {
	if x != nil {
		return x.ItemList
	}
	return nil
}

// 钓场事件变化投递
type PondEventChangeInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EventId   POND_EVENT_CHANGE_TYPE `protobuf:"varint,1,opt,name=event_id,json=eventId,proto3,enum=common.POND_EVENT_CHANGE_TYPE" json:"event_id,omitempty"` // 事件id
	BeforeNum int64                  `protobuf:"varint,2,opt,name=before_num,json=beforeNum,proto3" json:"before_num,omitempty"`                              // 变化前数量
	AfterNum  int64                  `protobuf:"varint,3,opt,name=after_num,json=afterNum,proto3" json:"after_num,omitempty"`                                 // 变化后数量
	ChangeNum int64                  `protobuf:"varint,4,opt,name=change_num,json=changeNum,proto3" json:"change_num,omitempty"`                              // 变化数量
}

func (x *PondEventChangeInfo) Reset() {
	*x = PondEventChangeInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PondEventChangeInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PondEventChangeInfo) ProtoMessage() {}

func (x *PondEventChangeInfo) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PondEventChangeInfo.ProtoReflect.Descriptor instead.
func (*PondEventChangeInfo) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{26}
}

func (x *PondEventChangeInfo) GetEventId() POND_EVENT_CHANGE_TYPE {
	if x != nil {
		return x.EventId
	}
	return POND_EVENT_CHANGE_TYPE_PECV_UNKNOWN
}

func (x *PondEventChangeInfo) GetBeforeNum() int64 {
	if x != nil {
		return x.BeforeNum
	}
	return 0
}

func (x *PondEventChangeInfo) GetAfterNum() int64 {
	if x != nil {
		return x.AfterNum
	}
	return 0
}

func (x *PondEventChangeInfo) GetChangeNum() int64 {
	if x != nil {
		return x.ChangeNum
	}
	return 0
}

// 钓鱼事件信息
type FishingEventInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EventType  FISHING_EVENT_TYPE `protobuf:"varint,1,opt,name=event_type,json=eventType,proto3,enum=common.FISHING_EVENT_TYPE" json:"event_type,omitempty"`                                                             // 事件类型
	PlayerId   uint64             `protobuf:"varint,2,opt,name=player_id,json=playerId,proto3" json:"player_id,omitempty"`                                                                                               // 玩家id(如:中鱼者id)
	IntData    map[int32]int64    `protobuf:"bytes,3,rep,name=int_data,json=intData,proto3" json:"int_data,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`         // 整形事件参数 key:EVENT_INT_KEY value:int data
	StringData map[int32]string   `protobuf:"bytes,4,rep,name=string_data,json=stringData,proto3" json:"string_data,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 字符串事件参数 key:EVENT_STRING_KEY value:string data
}

func (x *FishingEventInfo) Reset() {
	*x = FishingEventInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FishingEventInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FishingEventInfo) ProtoMessage() {}

func (x *FishingEventInfo) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FishingEventInfo.ProtoReflect.Descriptor instead.
func (*FishingEventInfo) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{27}
}

func (x *FishingEventInfo) GetEventType() FISHING_EVENT_TYPE {
	if x != nil {
		return x.EventType
	}
	return FISHING_EVENT_TYPE_FET_UNKNOWN
}

func (x *FishingEventInfo) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

func (x *FishingEventInfo) GetIntData() map[int32]int64 {
	if x != nil {
		return x.IntData
	}
	return nil
}

func (x *FishingEventInfo) GetStringData() map[int32]string {
	if x != nil {
		return x.StringData
	}
	return nil
}

// 鱼破损信息
type FishDamagedInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FishDamagedValue int32           `protobuf:"varint,1,opt,name=fish_damaged_value,json=fishDamagedValue,proto3" json:"fish_damaged_value,omitempty"`                    // 鱼破损程度价值百分比，服务器校验值位于(0,100]区间内
	FishDamagedLv    FISH_DAMAGED_LV `protobuf:"varint,2,opt,name=fish_damaged_lv,json=fishDamagedLv,proto3,enum=common.FISH_DAMAGED_LV" json:"fish_damaged_lv,omitempty"` // 鱼破损程度等级
}

func (x *FishDamagedInfo) Reset() {
	*x = FishDamagedInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FishDamagedInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FishDamagedInfo) ProtoMessage() {}

func (x *FishDamagedInfo) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FishDamagedInfo.ProtoReflect.Descriptor instead.
func (*FishDamagedInfo) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{28}
}

func (x *FishDamagedInfo) GetFishDamagedValue() int32 {
	if x != nil {
		return x.FishDamagedValue
	}
	return 0
}

func (x *FishDamagedInfo) GetFishDamagedLv() FISH_DAMAGED_LV {
	if x != nil {
		return x.FishDamagedLv
	}
	return FISH_DAMAGED_LV_FDL_INTACT
}

// 钩饵组合
type HookBait struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HookId int64 `protobuf:"varint,1,opt,name=hook_id,json=hookId,proto3" json:"hook_id,omitempty"` // 钩子id
	BaitId int64 `protobuf:"varint,2,opt,name=bait_id,json=baitId,proto3" json:"bait_id,omitempty"` // 饵料id
}

func (x *HookBait) Reset() {
	*x = HookBait{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HookBait) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HookBait) ProtoMessage() {}

func (x *HookBait) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HookBait.ProtoReflect.Descriptor instead.
func (*HookBait) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{29}
}

func (x *HookBait) GetHookId() int64 {
	if x != nil {
		return x.HookId
	}
	return 0
}

func (x *HookBait) GetBaitId() int64 {
	if x != nil {
		return x.BaitId
	}
	return 0
}

// Item
type Item struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ItemId         int64           `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`                                                                           // 物品id required
	ItemCategory   ITEM_CATEGORY   `protobuf:"varint,2,opt,name=item_category,json=itemCategory,proto3,enum=common.ITEM_CATEGORY" json:"item_category,omitempty"`                               // 大类别 required
	ItemType       ITEM_TYPE       `protobuf:"varint,3,opt,name=item_type,json=itemType,proto3,enum=common.ITEM_TYPE" json:"item_type,omitempty"`                                               // 物品类型
	ItemSubType    int32           `protobuf:"varint,4,opt,name=item_sub_type,json=itemSubType,proto3" json:"item_sub_type,omitempty"`                                                          // 物品子类型
	ItemLevel      int32           `protobuf:"varint,6,opt,name=item_level,json=itemLevel,proto3" json:"item_level,omitempty"`                                                                  // 物品等级
	ItemExpireTime int64           `protobuf:"varint,7,opt,name=item_expire_time,json=itemExpireTime,proto3" json:"item_expire_time,omitempty"`                                                 // 有效时间
	InstanceId     string          `protobuf:"bytes,8,opt,name=instanceId,proto3" json:"instanceId,omitempty"`                                                                                  // 实例id
	UpdateTime     int64           `protobuf:"varint,9,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`                                                               // 更新时间
	Extra          map[int32]int64 `protobuf:"bytes,10,rep,name=extra,proto3" json:"extra,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` // 拓展信息
}

func (x *Item) Reset() {
	*x = Item{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Item) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Item) ProtoMessage() {}

func (x *Item) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Item.ProtoReflect.Descriptor instead.
func (*Item) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{30}
}

func (x *Item) GetItemId() int64 {
	if x != nil {
		return x.ItemId
	}
	return 0
}

func (x *Item) GetItemCategory() ITEM_CATEGORY {
	if x != nil {
		return x.ItemCategory
	}
	return ITEM_CATEGORY_IC_UNKNOWN
}

func (x *Item) GetItemType() ITEM_TYPE {
	if x != nil {
		return x.ItemType
	}
	return ITEM_TYPE_IT_UNKNOWN
}

func (x *Item) GetItemSubType() int32 {
	if x != nil {
		return x.ItemSubType
	}
	return 0
}

func (x *Item) GetItemLevel() int32 {
	if x != nil {
		return x.ItemLevel
	}
	return 0
}

func (x *Item) GetItemExpireTime() int64 {
	if x != nil {
		return x.ItemExpireTime
	}
	return 0
}

func (x *Item) GetInstanceId() string {
	if x != nil {
		return x.InstanceId
	}
	return ""
}

func (x *Item) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *Item) GetExtra() map[int32]int64 {
	if x != nil {
		return x.Extra
	}
	return nil
}

// ItemInfo
type ItemInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Item           *Item `protobuf:"bytes,1,opt,name=item,proto3" json:"item,omitempty"`
	ItemCount      int64 `protobuf:"varint,2,opt,name=item_count,json=itemCount,proto3" json:"item_count,omitempty"`                  // 存量
	ItemDeltaCount int64 `protobuf:"varint,3,opt,name=item_delta_count,json=itemDeltaCount,proto3" json:"item_delta_count,omitempty"` // 增量
}

func (x *ItemInfo) Reset() {
	*x = ItemInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ItemInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ItemInfo) ProtoMessage() {}

func (x *ItemInfo) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ItemInfo.ProtoReflect.Descriptor instead.
func (*ItemInfo) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{31}
}

func (x *ItemInfo) GetItem() *Item {
	if x != nil {
		return x.Item
	}
	return nil
}

func (x *ItemInfo) GetItemCount() int64 {
	if x != nil {
		return x.ItemCount
	}
	return 0
}

func (x *ItemInfo) GetItemDeltaCount() int64 {
	if x != nil {
		return x.ItemDeltaCount
	}
	return 0
}

// 玩家物品信息
type PlayerItemInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerId        uint64      `protobuf:"varint,1,opt,name=player_id,json=playerId,proto3" json:"player_id,omitempty"`                       // 玩家id
	PropList        []*ItemInfo `protobuf:"bytes,2,rep,name=prop_list,json=propList,proto3" json:"prop_list,omitempty"`                        // 物品列表
	BagList         []*ItemInfo `protobuf:"bytes,3,rep,name=bag_list,json=bagList,proto3" json:"bag_list,omitempty"`                           // 背包列表
	EquipTackleList []*ItemInfo `protobuf:"bytes,4,rep,name=equip_tackle_list,json=equipTackleList,proto3" json:"equip_tackle_list,omitempty"` // tackle 列表
}

func (x *PlayerItemInfo) Reset() {
	*x = PlayerItemInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlayerItemInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlayerItemInfo) ProtoMessage() {}

func (x *PlayerItemInfo) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlayerItemInfo.ProtoReflect.Descriptor instead.
func (*PlayerItemInfo) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{32}
}

func (x *PlayerItemInfo) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

func (x *PlayerItemInfo) GetPropList() []*ItemInfo {
	if x != nil {
		return x.PropList
	}
	return nil
}

func (x *PlayerItemInfo) GetBagList() []*ItemInfo {
	if x != nil {
		return x.BagList
	}
	return nil
}

func (x *PlayerItemInfo) GetEquipTackleList() []*ItemInfo {
	if x != nil {
		return x.EquipTackleList
	}
	return nil
}

// 发奖前Item
type OriginLoot struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Item  *Item `protobuf:"bytes,1,opt,name=item,proto3" json:"item,omitempty"`    // 物品
	Value int64 `protobuf:"varint,2,opt,name=value,proto3" json:"value,omitempty"` // 数量
}

func (x *OriginLoot) Reset() {
	*x = OriginLoot{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OriginLoot) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OriginLoot) ProtoMessage() {}

func (x *OriginLoot) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OriginLoot.ProtoReflect.Descriptor instead.
func (*OriginLoot) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{33}
}

func (x *OriginLoot) GetItem() *Item {
	if x != nil {
		return x.Item
	}
	return nil
}

func (x *OriginLoot) GetValue() int64 {
	if x != nil {
		return x.Value
	}
	return 0
}

type OriginLoots struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Loots  []*OriginLoot    `protobuf:"bytes,1,rep,name=loots,proto3" json:"loots,omitempty"`
	Source ITEM_SOURCE_TYPE `protobuf:"varint,3,opt,name=source,proto3,enum=common.ITEM_SOURCE_TYPE" json:"source,omitempty"` // 来源
}

func (x *OriginLoots) Reset() {
	*x = OriginLoots{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OriginLoots) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OriginLoots) ProtoMessage() {}

func (x *OriginLoots) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OriginLoots.ProtoReflect.Descriptor instead.
func (*OriginLoots) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{34}
}

func (x *OriginLoots) GetLoots() []*OriginLoot {
	if x != nil {
		return x.Loots
	}
	return nil
}

func (x *OriginLoots) GetSource() ITEM_SOURCE_TYPE {
	if x != nil {
		return x.Source
	}
	return ITEM_SOURCE_TYPE_IST_UNKNOWN
}

// 奖励信息
type Reward struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClaimID    string           `protobuf:"bytes,1,opt,name=claimID,proto3" json:"claimID,omitempty"`                                                       // 领奖唯一ID
	SourceType ITEM_SOURCE_TYPE `protobuf:"varint,2,opt,name=source_type,json=sourceType,proto3,enum=common.ITEM_SOURCE_TYPE" json:"source_type,omitempty"` // 奖励来源
	Timestamp  int64            `protobuf:"varint,3,opt,name=timestamp,proto3" json:"timestamp,omitempty"`                                                  // 时间戳
	ShowType   REWARD_SHOW_TYPE `protobuf:"varint,4,opt,name=show_type,json=showType,proto3,enum=common.REWARD_SHOW_TYPE" json:"show_type,omitempty"`       // 奖励展示方式
	ItemList   []*ItemInfo      `protobuf:"bytes,5,rep,name=item_list,json=itemList,proto3" json:"item_list,omitempty"`                                     // 物品列表
}

func (x *Reward) Reset() {
	*x = Reward{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Reward) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Reward) ProtoMessage() {}

func (x *Reward) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Reward.ProtoReflect.Descriptor instead.
func (*Reward) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{35}
}

func (x *Reward) GetClaimID() string {
	if x != nil {
		return x.ClaimID
	}
	return ""
}

func (x *Reward) GetSourceType() ITEM_SOURCE_TYPE {
	if x != nil {
		return x.SourceType
	}
	return ITEM_SOURCE_TYPE_IST_UNKNOWN
}

func (x *Reward) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *Reward) GetShowType() REWARD_SHOW_TYPE {
	if x != nil {
		return x.ShowType
	}
	return REWARD_SHOW_TYPE_RST_UNKNOWN
}

func (x *Reward) GetItemList() []*ItemInfo {
	if x != nil {
		return x.ItemList
	}
	return nil
}

// 商品购买信息
type GoodsBuyInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GoodsId     int64 `protobuf:"varint,1,opt,name=goods_id,json=goodsId,proto3" json:"goods_id,omitempty"`               // 商品id
	BuyTimes    int32 `protobuf:"varint,2,opt,name=buy_times,json=buyTimes,proto3" json:"buy_times,omitempty"`            // 购买次数
	LastBuyTime int64 `protobuf:"varint,3,opt,name=last_buy_time,json=lastBuyTime,proto3" json:"last_buy_time,omitempty"` // 上次购买时间(时间戳)
}

func (x *GoodsBuyInfo) Reset() {
	*x = GoodsBuyInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GoodsBuyInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoodsBuyInfo) ProtoMessage() {}

func (x *GoodsBuyInfo) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoodsBuyInfo.ProtoReflect.Descriptor instead.
func (*GoodsBuyInfo) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{36}
}

func (x *GoodsBuyInfo) GetGoodsId() int64 {
	if x != nil {
		return x.GoodsId
	}
	return 0
}

func (x *GoodsBuyInfo) GetBuyTimes() int32 {
	if x != nil {
		return x.BuyTimes
	}
	return 0
}

func (x *GoodsBuyInfo) GetLastBuyTime() int64 {
	if x != nil {
		return x.LastBuyTime
	}
	return 0
}

// 道具基础信息(服务器内部rpc使用 转发到hall服务器添加)
type ItemBase struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ItemId     int64  `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`            // 物品id
	ItemCount  int64  `protobuf:"varint,2,opt,name=item_count,json=itemCount,proto3" json:"item_count,omitempty"`   // 数量
	InstanceId string `protobuf:"bytes,3,opt,name=instance_id,json=instanceId,proto3" json:"instance_id,omitempty"` // 实例id
}

func (x *ItemBase) Reset() {
	*x = ItemBase{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ItemBase) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ItemBase) ProtoMessage() {}

func (x *ItemBase) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ItemBase.ProtoReflect.Descriptor instead.
func (*ItemBase) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{37}
}

func (x *ItemBase) GetItemId() int64 {
	if x != nil {
		return x.ItemId
	}
	return 0
}

func (x *ItemBase) GetItemCount() int64 {
	if x != nil {
		return x.ItemCount
	}
	return 0
}

func (x *ItemBase) GetInstanceId() string {
	if x != nil {
		return x.InstanceId
	}
	return ""
}

type ItemBaseList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ItemList []*ItemBase `protobuf:"bytes,1,rep,name=item_list,json=itemList,proto3" json:"item_list,omitempty"`
}

func (x *ItemBaseList) Reset() {
	*x = ItemBaseList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ItemBaseList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ItemBaseList) ProtoMessage() {}

func (x *ItemBaseList) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ItemBaseList.ProtoReflect.Descriptor instead.
func (*ItemBaseList) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{38}
}

func (x *ItemBaseList) GetItemList() []*ItemBase {
	if x != nil {
		return x.ItemList
	}
	return nil
}

// 道具冷却信息
type ItemCdInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ItemId      int64 `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`                  // 道具id
	LastUseTime int64 `protobuf:"varint,2,opt,name=last_use_time,json=lastUseTime,proto3" json:"last_use_time,omitempty"` // 上次使用时间
}

func (x *ItemCdInfo) Reset() {
	*x = ItemCdInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ItemCdInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ItemCdInfo) ProtoMessage() {}

func (x *ItemCdInfo) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ItemCdInfo.ProtoReflect.Descriptor instead.
func (*ItemCdInfo) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{39}
}

func (x *ItemCdInfo) GetItemId() int64 {
	if x != nil {
		return x.ItemId
	}
	return 0
}

func (x *ItemCdInfo) GetLastUseTime() int64 {
	if x != nil {
		return x.LastUseTime
	}
	return 0
}

type PondGoods struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GoodId   int64 `protobuf:"varint,1,opt,name=good_id,json=goodId,proto3" json:"good_id,omitempty"` // 商品id
	Bought   int32 `protobuf:"varint,2,opt,name=bought,proto3" json:"bought,omitempty"`               // 已购买数量
	Nums     int32 `protobuf:"varint,3,opt,name=nums,proto3" json:"nums,omitempty"`                   // 可购买数量
	Discount int32 `protobuf:"varint,4,opt,name=discount,proto3" json:"discount,omitempty"`           // *折扣  具体金额向下取整
}

func (x *PondGoods) Reset() {
	*x = PondGoods{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PondGoods) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PondGoods) ProtoMessage() {}

func (x *PondGoods) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PondGoods.ProtoReflect.Descriptor instead.
func (*PondGoods) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{40}
}

func (x *PondGoods) GetGoodId() int64 {
	if x != nil {
		return x.GoodId
	}
	return 0
}

func (x *PondGoods) GetBought() int32 {
	if x != nil {
		return x.Bought
	}
	return 0
}

func (x *PondGoods) GetNums() int32 {
	if x != nil {
		return x.Nums
	}
	return 0
}

func (x *PondGoods) GetDiscount() int32 {
	if x != nil {
		return x.Discount
	}
	return 0
}

// 道具批次
type PondBatch struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BatchId int64        `protobuf:"varint,1,opt,name=batch_id,json=batchId,proto3" json:"batch_id,omitempty"` // 批次id (配置id
	TsStart int64        `protobuf:"varint,2,opt,name=ts_start,json=tsStart,proto3" json:"ts_start,omitempty"` // 上架时间
	TsEnd   int64        `protobuf:"varint,3,opt,name=ts_end,json=tsEnd,proto3" json:"ts_end,omitempty"`       // 下架时间
	List    []*PondGoods `protobuf:"bytes,4,rep,name=list,proto3" json:"list,omitempty"`                       // 道具列表
}

func (x *PondBatch) Reset() {
	*x = PondBatch{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PondBatch) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PondBatch) ProtoMessage() {}

func (x *PondBatch) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PondBatch.ProtoReflect.Descriptor instead.
func (*PondBatch) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{41}
}

func (x *PondBatch) GetBatchId() int64 {
	if x != nil {
		return x.BatchId
	}
	return 0
}

func (x *PondBatch) GetTsStart() int64 {
	if x != nil {
		return x.TsStart
	}
	return 0
}

func (x *PondBatch) GetTsEnd() int64 {
	if x != nil {
		return x.TsEnd
	}
	return 0
}

func (x *PondBatch) GetList() []*PondGoods {
	if x != nil {
		return x.List
	}
	return nil
}

type PondStore struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PondId int64        `protobuf:"varint,1,opt,name=pond_id,json=pondId,proto3" json:"pond_id,omitempty"` // 场次id
	List   []*PondBatch `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`                    // 批次列表
}

func (x *PondStore) Reset() {
	*x = PondStore{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PondStore) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PondStore) ProtoMessage() {}

func (x *PondStore) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PondStore.ProtoReflect.Descriptor instead.
func (*PondStore) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{42}
}

func (x *PondStore) GetPondId() int64 {
	if x != nil {
		return x.PondId
	}
	return 0
}

func (x *PondStore) GetList() []*PondBatch {
	if x != nil {
		return x.List
	}
	return nil
}

// 订单信息
type PurchaseOrderInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProductID   PRODUCT_ID `protobuf:"varint,1,opt,name=ProductID,proto3,enum=common.PRODUCT_ID" json:"ProductID,omitempty"` // 产品ID
	PurchaseID  int32      `protobuf:"varint,2,opt,name=PurchaseID,proto3" json:"PurchaseID,omitempty"`                      // 支付ID(对应配置文件)
	CommodityID string     `protobuf:"bytes,3,opt,name=CommodityID,proto3" json:"CommodityID,omitempty"`                     // 第三方商品ID,对应配置的渠道商品ID
	OrderID     int64      `protobuf:"varint,4,opt,name=OrderID,proto3" json:"OrderID,omitempty"`                            // 订单ID
}

func (x *PurchaseOrderInfo) Reset() {
	*x = PurchaseOrderInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PurchaseOrderInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PurchaseOrderInfo) ProtoMessage() {}

func (x *PurchaseOrderInfo) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PurchaseOrderInfo.ProtoReflect.Descriptor instead.
func (*PurchaseOrderInfo) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{43}
}

func (x *PurchaseOrderInfo) GetProductID() PRODUCT_ID {
	if x != nil {
		return x.ProductID
	}
	return PRODUCT_ID_PID_INIT
}

func (x *PurchaseOrderInfo) GetPurchaseID() int32 {
	if x != nil {
		return x.PurchaseID
	}
	return 0
}

func (x *PurchaseOrderInfo) GetCommodityID() string {
	if x != nil {
		return x.CommodityID
	}
	return ""
}

func (x *PurchaseOrderInfo) GetOrderID() int64 {
	if x != nil {
		return x.OrderID
	}
	return 0
}

// 天气分段信息
type WeatherPeriod struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Period            int64          `protobuf:"varint,1,opt,name=period,proto3" json:"period,omitempty"`                                                               // 第n时间段
	AirTemperature    int64          `protobuf:"varint,2,opt,name=air_temperature,json=airTemperature,proto3" json:"air_temperature,omitempty"`                         // 气温
	WaterTemperature  int64          `protobuf:"varint,3,opt,name=water_temperature,json=waterTemperature,proto3" json:"water_temperature,omitempty"`                   // 水温
	WindSpeed         int64          `protobuf:"varint,4,opt,name=wind_speed,json=windSpeed,proto3" json:"wind_speed,omitempty"`                                        // 风速
	WindDirection     DIRECTION_TYPE `protobuf:"varint,5,opt,name=wind_direction,json=windDirection,proto3,enum=common.DIRECTION_TYPE" json:"wind_direction,omitempty"` // 风向
	Sky               WEATHER_TYPE   `protobuf:"varint,6,opt,name=sky,proto3,enum=common.WEATHER_TYPE" json:"sky,omitempty"`                                            // 天气
	LuxInExposure     int64          `protobuf:"varint,7,opt,name=lux_in_exposure,json=luxInExposure,proto3" json:"lux_in_exposure,omitempty"`                          // 光照强度
	LuxInShadow       int64          `protobuf:"varint,8,opt,name=lux_in_shadow,json=luxInShadow,proto3" json:"lux_in_shadow,omitempty"`                                // 阴影光照强度
	PressureInfluence int64          `protobuf:"varint,9,opt,name=pressure_influence,json=pressureInfluence,proto3" json:"pressure_influence,omitempty"`                // 气压影响
	CfgId             int64          `protobuf:"varint,10,opt,name=cfg_id,json=cfgId,proto3" json:"cfg_id,omitempty"`                                                   // 配置id
}

func (x *WeatherPeriod) Reset() {
	*x = WeatherPeriod{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WeatherPeriod) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WeatherPeriod) ProtoMessage() {}

func (x *WeatherPeriod) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WeatherPeriod.ProtoReflect.Descriptor instead.
func (*WeatherPeriod) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{44}
}

func (x *WeatherPeriod) GetPeriod() int64 {
	if x != nil {
		return x.Period
	}
	return 0
}

func (x *WeatherPeriod) GetAirTemperature() int64 {
	if x != nil {
		return x.AirTemperature
	}
	return 0
}

func (x *WeatherPeriod) GetWaterTemperature() int64 {
	if x != nil {
		return x.WaterTemperature
	}
	return 0
}

func (x *WeatherPeriod) GetWindSpeed() int64 {
	if x != nil {
		return x.WindSpeed
	}
	return 0
}

func (x *WeatherPeriod) GetWindDirection() DIRECTION_TYPE {
	if x != nil {
		return x.WindDirection
	}
	return DIRECTION_TYPE_D_UNKNOWN
}

func (x *WeatherPeriod) GetSky() WEATHER_TYPE {
	if x != nil {
		return x.Sky
	}
	return WEATHER_TYPE_WT_UNKNOWN
}

func (x *WeatherPeriod) GetLuxInExposure() int64 {
	if x != nil {
		return x.LuxInExposure
	}
	return 0
}

func (x *WeatherPeriod) GetLuxInShadow() int64 {
	if x != nil {
		return x.LuxInShadow
	}
	return 0
}

func (x *WeatherPeriod) GetPressureInfluence() int64 {
	if x != nil {
		return x.PressureInfluence
	}
	return 0
}

func (x *WeatherPeriod) GetCfgId() int64 {
	if x != nil {
		return x.CfgId
	}
	return 0
}

// 每日天气信息
type WeatherDay struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Index   int64            `protobuf:"varint,1,opt,name=index,proto3" json:"index,omitempty"`    // 天数索引 1-90
	Periods []*WeatherPeriod `protobuf:"bytes,2,rep,name=periods,proto3" json:"periods,omitempty"` // 天气分段信息
}

func (x *WeatherDay) Reset() {
	*x = WeatherDay{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WeatherDay) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WeatherDay) ProtoMessage() {}

func (x *WeatherDay) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WeatherDay.ProtoReflect.Descriptor instead.
func (*WeatherDay) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{45}
}

func (x *WeatherDay) GetIndex() int64 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *WeatherDay) GetPeriods() []*WeatherPeriod {
	if x != nil {
		return x.Periods
	}
	return nil
}

// 游戏时间
type GameTime struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Day    int32 `protobuf:"varint,1,opt,name=Day,proto3" json:"Day,omitempty"`       // 天
	Hour   int32 `protobuf:"varint,2,opt,name=Hour,proto3" json:"Hour,omitempty"`     // 小时
	Min    int32 `protobuf:"varint,3,opt,name=Min,proto3" json:"Min,omitempty"`       // 分钟
	Period int32 `protobuf:"varint,4,opt,name=Period,proto3" json:"Period,omitempty"` // 时段
}

func (x *GameTime) Reset() {
	*x = GameTime{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GameTime) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GameTime) ProtoMessage() {}

func (x *GameTime) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GameTime.ProtoReflect.Descriptor instead.
func (*GameTime) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{46}
}

func (x *GameTime) GetDay() int32 {
	if x != nil {
		return x.Day
	}
	return 0
}

func (x *GameTime) GetHour() int32 {
	if x != nil {
		return x.Hour
	}
	return 0
}

func (x *GameTime) GetMin() int32 {
	if x != nil {
		return x.Min
	}
	return 0
}

func (x *GameTime) GetPeriod() int32 {
	if x != nil {
		return x.Period
	}
	return 0
}

// ************************************************************************
type RodRigInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RigId    int32  `protobuf:"varint,1,opt,name=rig_id,json=rigId,proto3" json:"rig_id,omitempty"`          // 钓组id(从1开始)
	Name     string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                          // 名称
	RodId    int64  `protobuf:"varint,3,opt,name=rod_id,json=rodId,proto3" json:"rod_id,omitempty"`          // 竿id
	ReelId   int64  `protobuf:"varint,4,opt,name=reel_id,json=reelId,proto3" json:"reel_id,omitempty"`       // 轮id
	LineId   int64  `protobuf:"varint,5,opt,name=line_id,json=lineId,proto3" json:"line_id,omitempty"`       // 主线id
	LeaderId int64  `protobuf:"varint,6,opt,name=leader_id,json=leaderId,proto3" json:"leader_id,omitempty"` // 子线id
	BaitId   int64  `protobuf:"varint,7,opt,name=bait_id,json=baitId,proto3" json:"bait_id,omitempty"`       // 鱼饵id
	FloatId  int64  `protobuf:"varint,8,opt,name=float_id,json=floatId,proto3" json:"float_id,omitempty"`    // 浮漂id
	HookId   int64  `protobuf:"varint,9,opt,name=hook_id,json=hookId,proto3" json:"hook_id,omitempty"`       // 鱼钩id
}

func (x *RodRigInfo) Reset() {
	*x = RodRigInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RodRigInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RodRigInfo) ProtoMessage() {}

func (x *RodRigInfo) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RodRigInfo.ProtoReflect.Descriptor instead.
func (*RodRigInfo) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{47}
}

func (x *RodRigInfo) GetRigId() int32 {
	if x != nil {
		return x.RigId
	}
	return 0
}

func (x *RodRigInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RodRigInfo) GetRodId() int64 {
	if x != nil {
		return x.RodId
	}
	return 0
}

func (x *RodRigInfo) GetReelId() int64 {
	if x != nil {
		return x.ReelId
	}
	return 0
}

func (x *RodRigInfo) GetLineId() int64 {
	if x != nil {
		return x.LineId
	}
	return 0
}

func (x *RodRigInfo) GetLeaderId() int64 {
	if x != nil {
		return x.LeaderId
	}
	return 0
}

func (x *RodRigInfo) GetBaitId() int64 {
	if x != nil {
		return x.BaitId
	}
	return 0
}

func (x *RodRigInfo) GetFloatId() int64 {
	if x != nil {
		return x.FloatId
	}
	return 0
}

func (x *RodRigInfo) GetHookId() int64 {
	if x != nil {
		return x.HookId
	}
	return 0
}

// 统计信息
type StatInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id  int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`   // 统计id
	Val int64 `protobuf:"varint,2,opt,name=val,proto3" json:"val,omitempty"` // 统计值
}

func (x *StatInfo) Reset() {
	*x = StatInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StatInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatInfo) ProtoMessage() {}

func (x *StatInfo) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatInfo.ProtoReflect.Descriptor instead.
func (*StatInfo) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{48}
}

func (x *StatInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *StatInfo) GetVal() int64 {
	if x != nil {
		return x.Val
	}
	return 0
}

// 统计规则信息
type StatsRuleInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Typ        int32   `protobuf:"varint,1,opt,name=typ,proto3" json:"typ,omitempty"`                                            // 类型
	Field      int64   `protobuf:"varint,2,opt,name=field,proto3" json:"field,omitempty"`                                        // 字段
	Target     int64   `protobuf:"varint,3,opt,name=target,proto3" json:"target,omitempty"`                                      // 目标值
	AddType    SUM_ADD `protobuf:"varint,4,opt,name=add_type,json=addType,proto3,enum=common.SUM_ADD" json:"add_type,omitempty"` // 统计规则
	Val        int64   `protobuf:"varint,5,opt,name=val,proto3" json:"val,omitempty"`                                            // 统计值
	CondKey    int32   `protobuf:"varint,6,opt,name=cond_key,json=condKey,proto3" json:"cond_key,omitempty"`                     // 条件key
	CondVal    int64   `protobuf:"varint,7,opt,name=cond_val,json=condVal,proto3" json:"cond_val,omitempty"`                     // 条件值
	UpdateTime int64   `protobuf:"varint,8,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`            // 更新时间
}

func (x *StatsRuleInfo) Reset() {
	*x = StatsRuleInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StatsRuleInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatsRuleInfo) ProtoMessage() {}

func (x *StatsRuleInfo) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatsRuleInfo.ProtoReflect.Descriptor instead.
func (*StatsRuleInfo) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{49}
}

func (x *StatsRuleInfo) GetTyp() int32 {
	if x != nil {
		return x.Typ
	}
	return 0
}

func (x *StatsRuleInfo) GetField() int64 {
	if x != nil {
		return x.Field
	}
	return 0
}

func (x *StatsRuleInfo) GetTarget() int64 {
	if x != nil {
		return x.Target
	}
	return 0
}

func (x *StatsRuleInfo) GetAddType() SUM_ADD {
	if x != nil {
		return x.AddType
	}
	return SUM_ADD_SA_UNKNOWN
}

func (x *StatsRuleInfo) GetVal() int64 {
	if x != nil {
		return x.Val
	}
	return 0
}

func (x *StatsRuleInfo) GetCondKey() int32 {
	if x != nil {
		return x.CondKey
	}
	return 0
}

func (x *StatsRuleInfo) GetCondVal() int64 {
	if x != nil {
		return x.CondVal
	}
	return 0
}

func (x *StatsRuleInfo) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

type StatsRuleDesc struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Typ     int32   `protobuf:"varint,1,opt,name=typ,proto3" json:"typ,omitempty"`                                            // 类型
	Field   int64   `protobuf:"varint,2,opt,name=field,proto3" json:"field,omitempty"`                                        // 字段
	Target  int64   `protobuf:"varint,3,opt,name=target,proto3" json:"target,omitempty"`                                      // 目标值
	AddType SUM_ADD `protobuf:"varint,4,opt,name=add_type,json=addType,proto3,enum=common.SUM_ADD" json:"add_type,omitempty"` // 统计规则
	CondKey int32   `protobuf:"varint,6,opt,name=cond_key,json=condKey,proto3" json:"cond_key,omitempty"`                     // 条件key
	CondVal int64   `protobuf:"varint,7,opt,name=cond_val,json=condVal,proto3" json:"cond_val,omitempty"`                     // 条件值
}

func (x *StatsRuleDesc) Reset() {
	*x = StatsRuleDesc{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StatsRuleDesc) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatsRuleDesc) ProtoMessage() {}

func (x *StatsRuleDesc) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatsRuleDesc.ProtoReflect.Descriptor instead.
func (*StatsRuleDesc) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{50}
}

func (x *StatsRuleDesc) GetTyp() int32 {
	if x != nil {
		return x.Typ
	}
	return 0
}

func (x *StatsRuleDesc) GetField() int64 {
	if x != nil {
		return x.Field
	}
	return 0
}

func (x *StatsRuleDesc) GetTarget() int64 {
	if x != nil {
		return x.Target
	}
	return 0
}

func (x *StatsRuleDesc) GetAddType() SUM_ADD {
	if x != nil {
		return x.AddType
	}
	return SUM_ADD_SA_UNKNOWN
}

func (x *StatsRuleDesc) GetCondKey() int32 {
	if x != nil {
		return x.CondKey
	}
	return 0
}

func (x *StatsRuleDesc) GetCondVal() int64 {
	if x != nil {
		return x.CondVal
	}
	return 0
}

// 旅途钓组信息
type RodBagInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       int32           `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                                                                             // 钓组id
	Info     map[int32]*Item `protobuf:"bytes,2,rep,name=info,proto3" json:"info,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // key定义 见enum #TRIP_ROD_SIT
	Name     string          `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`                                                                                          // 钓组名称
	BagIndex int32           `protobuf:"varint,4,opt,name=bag_index,json=bagIndex,proto3" json:"bag_index,omitempty"`                                                                 // 背包位置：0在仓库 1,2,3... 对应背包位置
}

func (x *RodBagInfo) Reset() {
	*x = RodBagInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RodBagInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RodBagInfo) ProtoMessage() {}

func (x *RodBagInfo) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RodBagInfo.ProtoReflect.Descriptor instead.
func (*RodBagInfo) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{51}
}

func (x *RodBagInfo) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RodBagInfo) GetInfo() map[int32]*Item {
	if x != nil {
		return x.Info
	}
	return nil
}

func (x *RodBagInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RodBagInfo) GetBagIndex() int32 {
	if x != nil {
		return x.BagIndex
	}
	return 0
}

// 简要邮件信息
type MailBrief struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MailId      uint64          `protobuf:"varint,1,opt,name=mail_id,json=mailId,proto3" json:"mail_id,omitempty"`                                                                            // 邮件ID
	Title       string          `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`                                                                                             // 邮件标题
	IconUrl     string          `protobuf:"bytes,3,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`                                                                          // 邮件配图  (未实现)
	MailType    MAIL_TYPE       `protobuf:"varint,4,opt,name=mail_type,json=mailType,proto3,enum=common.MAIL_TYPE" json:"mail_type,omitempty"`                                                // 邮件类型
	CreateTime  int64           `protobuf:"varint,5,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`                                                                // 创建时间
	ExpiresTime int64           `protobuf:"varint,6,opt,name=expires_time,json=expiresTime,proto3" json:"expires_time,omitempty"`                                                             // 到期时间
	Content     string          `protobuf:"bytes,7,opt,name=content,proto3" json:"content,omitempty"`                                                                                         // 邮件内容
	Sender      string          `protobuf:"bytes,8,opt,name=sender,proto3" json:"sender,omitempty"`                                                                                           // 发件人
	Extend      map[int32]int64 `protobuf:"bytes,9,rep,name=extend,proto3" json:"extend,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` // 拓展参数 key: MAIL_EXTEND_KEY Value: int64
}

func (x *MailBrief) Reset() {
	*x = MailBrief{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MailBrief) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MailBrief) ProtoMessage() {}

func (x *MailBrief) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MailBrief.ProtoReflect.Descriptor instead.
func (*MailBrief) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{52}
}

func (x *MailBrief) GetMailId() uint64 {
	if x != nil {
		return x.MailId
	}
	return 0
}

func (x *MailBrief) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *MailBrief) GetIconUrl() string {
	if x != nil {
		return x.IconUrl
	}
	return ""
}

func (x *MailBrief) GetMailType() MAIL_TYPE {
	if x != nil {
		return x.MailType
	}
	return MAIL_TYPE_MT_UNKNOWN
}

func (x *MailBrief) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *MailBrief) GetExpiresTime() int64 {
	if x != nil {
		return x.ExpiresTime
	}
	return 0
}

func (x *MailBrief) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *MailBrief) GetSender() string {
	if x != nil {
		return x.Sender
	}
	return ""
}

func (x *MailBrief) GetExtend() map[int32]int64 {
	if x != nil {
		return x.Extend
	}
	return nil
}

type MailAttachInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AttachStatus MAIL_ATTACH_STATUS       `protobuf:"varint,1,opt,name=attach_status,json=attachStatus,proto3,enum=common.MAIL_ATTACH_STATUS" json:"attach_status,omitempty"`    // 附件状态
	ClaimStatus  MAIL_ATTACH_CLAIM_STATUS `protobuf:"varint,2,opt,name=claim_status,json=claimStatus,proto3,enum=common.MAIL_ATTACH_CLAIM_STATUS" json:"claim_status,omitempty"` // 附件领取状态
	Rewards      *ItemBaseList            `protobuf:"bytes,3,opt,name=rewards,proto3" json:"rewards,omitempty"`                                                                  // 附件奖励
}

func (x *MailAttachInfo) Reset() {
	*x = MailAttachInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MailAttachInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MailAttachInfo) ProtoMessage() {}

func (x *MailAttachInfo) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MailAttachInfo.ProtoReflect.Descriptor instead.
func (*MailAttachInfo) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{53}
}

func (x *MailAttachInfo) GetAttachStatus() MAIL_ATTACH_STATUS {
	if x != nil {
		return x.AttachStatus
	}
	return MAIL_ATTACH_STATUS_MAS_NOT_ATTACH
}

func (x *MailAttachInfo) GetClaimStatus() MAIL_ATTACH_CLAIM_STATUS {
	if x != nil {
		return x.ClaimStatus
	}
	return MAIL_ATTACH_CLAIM_STATUS_MACS_NOT_CLAIM
}

func (x *MailAttachInfo) GetRewards() *ItemBaseList {
	if x != nil {
		return x.Rewards
	}
	return nil
}

// 详细邮件信息
type MailDetailInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Brief      *MailBrief      `protobuf:"bytes,1,opt,name=brief,proto3" json:"brief,omitempty"`
	ReadYet    MAIL_STATUS     `protobuf:"varint,2,opt,name=read_yet,json=readYet,proto3,enum=common.MAIL_STATUS" json:"read_yet,omitempty"` // 是否已读
	AttachInfo *MailAttachInfo `protobuf:"bytes,3,opt,name=attach_info,json=attachInfo,proto3" json:"attach_info,omitempty"`                 // 附件信息
}

func (x *MailDetailInfo) Reset() {
	*x = MailDetailInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MailDetailInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MailDetailInfo) ProtoMessage() {}

func (x *MailDetailInfo) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MailDetailInfo.ProtoReflect.Descriptor instead.
func (*MailDetailInfo) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{54}
}

func (x *MailDetailInfo) GetBrief() *MailBrief {
	if x != nil {
		return x.Brief
	}
	return nil
}

func (x *MailDetailInfo) GetReadYet() MAIL_STATUS {
	if x != nil {
		return x.ReadYet
	}
	return MAIL_STATUS_MS_UNREAD
}

func (x *MailDetailInfo) GetAttachInfo() *MailAttachInfo {
	if x != nil {
		return x.AttachInfo
	}
	return nil
}

// 邮件装配
type MailAssembly struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TemplateId  int64           `protobuf:"varint,1,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`                                                                // 邮件模板id
	MailType    MAIL_TYPE       `protobuf:"varint,2,opt,name=mail_type,json=mailType,proto3,enum=common.MAIL_TYPE" json:"mail_type,omitempty"`                                                // 邮件类型
	Rewards     *ItemBaseList   `protobuf:"bytes,3,opt,name=rewards,proto3" json:"rewards,omitempty"`                                                                                         // 附件奖励
	ExpiresTime int64           `protobuf:"varint,4,opt,name=expires_time,json=expiresTime,proto3" json:"expires_time,omitempty"`                                                             // 过期时间
	CreateTime  int64           `protobuf:"varint,5,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`                                                                // 创建时间
	Extend      map[int32]int64 `protobuf:"bytes,6,rep,name=extend,proto3" json:"extend,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` // 拓展参数 key: MAIL_EXTEND_KEY Value: int64
	IconUrl     string          `protobuf:"bytes,7,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`                                                                          // 邮件配图  (未实现)
}

func (x *MailAssembly) Reset() {
	*x = MailAssembly{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MailAssembly) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MailAssembly) ProtoMessage() {}

func (x *MailAssembly) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MailAssembly.ProtoReflect.Descriptor instead.
func (*MailAssembly) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{55}
}

func (x *MailAssembly) GetTemplateId() int64 {
	if x != nil {
		return x.TemplateId
	}
	return 0
}

func (x *MailAssembly) GetMailType() MAIL_TYPE {
	if x != nil {
		return x.MailType
	}
	return MAIL_TYPE_MT_UNKNOWN
}

func (x *MailAssembly) GetRewards() *ItemBaseList {
	if x != nil {
		return x.Rewards
	}
	return nil
}

func (x *MailAssembly) GetExpiresTime() int64 {
	if x != nil {
		return x.ExpiresTime
	}
	return 0
}

func (x *MailAssembly) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *MailAssembly) GetExtend() map[int32]int64 {
	if x != nil {
		return x.Extend
	}
	return nil
}

func (x *MailAssembly) GetIconUrl() string {
	if x != nil {
		return x.IconUrl
	}
	return ""
}

// 详细广播信息
type MsgBroadcastDetailInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BcType     MSG_BROADCAST_TYPE `protobuf:"varint,1,opt,name=bc_type,json=bcType,proto3,enum=common.MSG_BROADCAST_TYPE" json:"bc_type,omitempty"` // 广播类型
	TemplateId int64              `protobuf:"varint,2,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`                    // 模板id
	Params     []string           `protobuf:"bytes,3,rep,name=params,proto3" json:"params,omitempty"`                                               // 参数
	CreateTime int64              `protobuf:"varint,4,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`                    // 创建时间
}

func (x *MsgBroadcastDetailInfo) Reset() {
	*x = MsgBroadcastDetailInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MsgBroadcastDetailInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MsgBroadcastDetailInfo) ProtoMessage() {}

func (x *MsgBroadcastDetailInfo) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MsgBroadcastDetailInfo.ProtoReflect.Descriptor instead.
func (*MsgBroadcastDetailInfo) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{56}
}

func (x *MsgBroadcastDetailInfo) GetBcType() MSG_BROADCAST_TYPE {
	if x != nil {
		return x.BcType
	}
	return MSG_BROADCAST_TYPE_MB_UNKNOWN
}

func (x *MsgBroadcastDetailInfo) GetTemplateId() int64 {
	if x != nil {
		return x.TemplateId
	}
	return 0
}

func (x *MsgBroadcastDetailInfo) GetParams() []string {
	if x != nil {
		return x.Params
	}
	return nil
}

func (x *MsgBroadcastDetailInfo) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

type TaskCond struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CondId   int64 `protobuf:"varint,1,opt,name=cond_id,json=condId,proto3" json:"cond_id,omitempty"`
	Progress int64 `protobuf:"varint,2,opt,name=progress,proto3" json:"progress,omitempty"` // 任务进度
}

func (x *TaskCond) Reset() {
	*x = TaskCond{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskCond) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskCond) ProtoMessage() {}

func (x *TaskCond) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskCond.ProtoReflect.Descriptor instead.
func (*TaskCond) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{57}
}

func (x *TaskCond) GetCondId() int64 {
	if x != nil {
		return x.CondId
	}
	return 0
}

func (x *TaskCond) GetProgress() int64 {
	if x != nil {
		return x.Progress
	}
	return 0
}

type TaskInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId   int64       `protobuf:"varint,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`           // 任务id  对应配置表
	Progress []*TaskCond `protobuf:"bytes,2,rep,name=progress,proto3" json:"progress,omitempty"`                      // 任务进度
	Status   TASK_STATUS `protobuf:"varint,3,opt,name=status,proto3,enum=common.TASK_STATUS" json:"status,omitempty"` // 任务状态
}

func (x *TaskInfo) Reset() {
	*x = TaskInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskInfo) ProtoMessage() {}

func (x *TaskInfo) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskInfo.ProtoReflect.Descriptor instead.
func (*TaskInfo) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{58}
}

func (x *TaskInfo) GetTaskId() int64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *TaskInfo) GetProgress() []*TaskCond {
	if x != nil {
		return x.Progress
	}
	return nil
}

func (x *TaskInfo) GetStatus() TASK_STATUS {
	if x != nil {
		return x.Status
	}
	return TASK_STATUS_TS_UNKNOWN
}

// 进度奖励
type TaskProgress struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Category TASK_CATEGORY `protobuf:"varint,1,opt,name=category,proto3,enum=common.TASK_CATEGORY" json:"category,omitempty"` // 任务类型
	SubId    int64         `protobuf:"varint,2,opt,name=sub_id,json=subId,proto3" json:"sub_id,omitempty"`                    // 子任务id
	Rewarded []int64       `protobuf:"varint,3,rep,packed,name=rewarded,proto3" json:"rewarded,omitempty"`                    // 已领取索引
	Score    int64         `protobuf:"varint,4,opt,name=score,proto3" json:"score,omitempty"`                                 // 现在多少分
}

func (x *TaskProgress) Reset() {
	*x = TaskProgress{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskProgress) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskProgress) ProtoMessage() {}

func (x *TaskProgress) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskProgress.ProtoReflect.Descriptor instead.
func (*TaskProgress) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{59}
}

func (x *TaskProgress) GetCategory() TASK_CATEGORY {
	if x != nil {
		return x.Category
	}
	return TASK_CATEGORY_TC_UNKNOWN
}

func (x *TaskProgress) GetSubId() int64 {
	if x != nil {
		return x.SubId
	}
	return 0
}

func (x *TaskProgress) GetRewarded() []int64 {
	if x != nil {
		return x.Rewarded
	}
	return nil
}

func (x *TaskProgress) GetScore() int64 {
	if x != nil {
		return x.Score
	}
	return 0
}

type ProgressInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Category PROGRESS_TYPE `protobuf:"varint,1,opt,name=category,proto3,enum=common.PROGRESS_TYPE" json:"category,omitempty"` // 任务类型
	SubId    int64         `protobuf:"varint,2,opt,name=sub_id,json=subId,proto3" json:"sub_id,omitempty"`                    // 子进度索引
	Rewarded []int64       `protobuf:"varint,3,rep,packed,name=rewarded,proto3" json:"rewarded,omitempty"`                    // 已领取索引
	Score    int64         `protobuf:"varint,4,opt,name=score,proto3" json:"score,omitempty"`                                 // 现在多少分
}

func (x *ProgressInfo) Reset() {
	*x = ProgressInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProgressInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProgressInfo) ProtoMessage() {}

func (x *ProgressInfo) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProgressInfo.ProtoReflect.Descriptor instead.
func (*ProgressInfo) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{60}
}

func (x *ProgressInfo) GetCategory() PROGRESS_TYPE {
	if x != nil {
		return x.Category
	}
	return PROGRESS_TYPE_PXT_UNKNOWN
}

func (x *ProgressInfo) GetSubId() int64 {
	if x != nil {
		return x.SubId
	}
	return 0
}

func (x *ProgressInfo) GetRewarded() []int64 {
	if x != nil {
		return x.Rewarded
	}
	return nil
}

func (x *ProgressInfo) GetScore() int64 {
	if x != nil {
		return x.Score
	}
	return 0
}

// 个人排行数据
type RankPlayer struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	User *BriefUserInfo  `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`                                                                                           // 用户数据
	Rank int32           `protobuf:"varint,2,opt,name=rank,proto3" json:"rank,omitempty"`                                                                                          // 名次
	Info map[int32]int64 `protobuf:"bytes,4,rep,name=info,proto3" json:"info,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` // 通用封装数据 key定义 见enum #RANK_INFO_KEY
}

func (x *RankPlayer) Reset() {
	*x = RankPlayer{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RankPlayer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RankPlayer) ProtoMessage() {}

func (x *RankPlayer) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RankPlayer.ProtoReflect.Descriptor instead.
func (*RankPlayer) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{61}
}

func (x *RankPlayer) GetUser() *BriefUserInfo {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *RankPlayer) GetRank() int32 {
	if x != nil {
		return x.Rank
	}
	return 0
}

func (x *RankPlayer) GetInfo() map[int32]int64 {
	if x != nil {
		return x.Info
	}
	return nil
}

// 内容结构体
type AnnContent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ImageUrl string `protobuf:"bytes,1,opt,name=image_url,json=imageUrl,proto3" json:"image_url,omitempty"` // 图像地址
}

func (x *AnnContent) Reset() {
	*x = AnnContent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AnnContent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnnContent) ProtoMessage() {}

func (x *AnnContent) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnnContent.ProtoReflect.Descriptor instead.
func (*AnnContent) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{62}
}

func (x *AnnContent) GetImageUrl() string {
	if x != nil {
		return x.ImageUrl
	}
	return ""
}

// 动作结构体
type AnnAction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	JumpType ANN_JUMP_TYPE `protobuf:"varint,1,opt,name=jump_type,json=jumpType,proto3,enum=common.ANN_JUMP_TYPE" json:"jump_type,omitempty"` // 跳转类型
	JumpArgs string        `protobuf:"bytes,2,opt,name=jump_args,json=jumpArgs,proto3" json:"jump_args,omitempty"`                            // 跳转参数
}

func (x *AnnAction) Reset() {
	*x = AnnAction{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AnnAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnnAction) ProtoMessage() {}

func (x *AnnAction) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnnAction.ProtoReflect.Descriptor instead.
func (*AnnAction) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{63}
}

func (x *AnnAction) GetJumpType() ANN_JUMP_TYPE {
	if x != nil {
		return x.JumpType
	}
	return ANN_JUMP_TYPE_JUMP_NONE
}

func (x *AnnAction) GetJumpArgs() string {
	if x != nil {
		return x.JumpArgs
	}
	return ""
}

// 条件结构体
type AnnConditions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DisplayStrategy ANN_DISPLAY_TYPE `protobuf:"varint,1,opt,name=display_strategy,json=displayStrategy,proto3,enum=common.ANN_DISPLAY_TYPE" json:"display_strategy,omitempty"` // 展示策略
	MinVersion      string           `protobuf:"bytes,2,opt,name=min_version,json=minVersion,proto3" json:"min_version,omitempty"`                                              // 最小版本号
}

func (x *AnnConditions) Reset() {
	*x = AnnConditions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AnnConditions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnnConditions) ProtoMessage() {}

func (x *AnnConditions) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnnConditions.ProtoReflect.Descriptor instead.
func (*AnnConditions) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{64}
}

func (x *AnnConditions) GetDisplayStrategy() ANN_DISPLAY_TYPE {
	if x != nil {
		return x.DisplayStrategy
	}
	return ANN_DISPLAY_TYPE_DISPLAY_NONE
}

func (x *AnnConditions) GetMinVersion() string {
	if x != nil {
		return x.MinVersion
	}
	return ""
}

// 主响应结构体
type AnnPopupInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id            int32          `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                           // 公告id
	Priority      int32          `protobuf:"varint,2,opt,name=priority,proto3" json:"priority,omitempty"`                               // 优先级
	ChannelId     int32          `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`            // 渠道
	AnnContent    *AnnContent    `protobuf:"bytes,4,opt,name=ann_content,json=annContent,proto3" json:"ann_content,omitempty"`          // 内容
	PopStyle      int32          `protobuf:"varint,5,opt,name=pop_style,json=popStyle,proto3" json:"pop_style,omitempty"`               // 默认值语法
	AnnAction     *AnnAction     `protobuf:"bytes,6,opt,name=ann_action,json=annAction,proto3" json:"ann_action,omitempty"`             // 动作
	AnnConditions *AnnConditions `protobuf:"bytes,7,opt,name=ann_conditions,json=annConditions,proto3" json:"ann_conditions,omitempty"` // 条件
	StartTime     int64          `protobuf:"varint,8,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`            // 生效时间
	EndTime       int64          `protobuf:"varint,9,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`                  // 结束时间
}

func (x *AnnPopupInfo) Reset() {
	*x = AnnPopupInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_common_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AnnPopupInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnnPopupInfo) ProtoMessage() {}

func (x *AnnPopupInfo) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnnPopupInfo.ProtoReflect.Descriptor instead.
func (*AnnPopupInfo) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{65}
}

func (x *AnnPopupInfo) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AnnPopupInfo) GetPriority() int32 {
	if x != nil {
		return x.Priority
	}
	return 0
}

func (x *AnnPopupInfo) GetChannelId() int32 {
	if x != nil {
		return x.ChannelId
	}
	return 0
}

func (x *AnnPopupInfo) GetAnnContent() *AnnContent {
	if x != nil {
		return x.AnnContent
	}
	return nil
}

func (x *AnnPopupInfo) GetPopStyle() int32 {
	if x != nil {
		return x.PopStyle
	}
	return 0
}

func (x *AnnPopupInfo) GetAnnAction() *AnnAction {
	if x != nil {
		return x.AnnAction
	}
	return nil
}

func (x *AnnPopupInfo) GetAnnConditions() *AnnConditions {
	if x != nil {
		return x.AnnConditions
	}
	return nil
}

func (x *AnnPopupInfo) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *AnnPopupInfo) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

var File_common_proto protoreflect.FileDescriptor

var file_common_proto_rawDesc = []byte{
	0x0a, 0x0c, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x06,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x1a, 0x0a, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0xcb, 0x01, 0x0a, 0x0d, 0x41, 0x70, 0x70, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1a,
	0x0a, 0x08, 0x69, 0x73, 0x50, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x08, 0x69, 0x73, 0x50, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x73,
	0x5f, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x69, 0x73,
	0x46, 0x6f, 0x72, 0x63, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61,
	0x64, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x6f, 0x77,
	0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72, 0x6c, 0x12, 0x1e, 0x0a, 0x0b, 0x74, 0x69, 0x70, 0x5f,
	0x6d, 0x73, 0x67, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74,
	0x69, 0x70, 0x4d, 0x73, 0x67, 0x4b, 0x65, 0x79, 0x12, 0x26, 0x0a, 0x0f, 0x6d, 0x69, 0x6e, 0x5f,
	0x61, 0x70, 0x70, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x6d, 0x69, 0x6e, 0x41, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x22, 0x93, 0x01, 0x0a, 0x0f, 0x41, 0x70, 0x70, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x6d,
	0x64, 0x35, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x4d, 0x64, 0x35, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x5f, 0x6d, 0x64,
	0x35, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x4d,
	0x64, 0x35, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x5f, 0x66, 0x6f, 0x72,
	0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65,
	0x46, 0x6f, 0x72, 0x63, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x66,
	0x6f, 0x72, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x70, 0x61, 0x74, 0x63,
	0x68, 0x46, 0x6f, 0x72, 0x63, 0x65, 0x22, 0xce, 0x01, 0x0a, 0x0e, 0x41, 0x70, 0x70, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x17, 0x0a, 0x07, 0x73, 0x72, 0x76,
	0x5f, 0x75, 0x72, 0x69, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x73, 0x72, 0x76, 0x55,
	0x72, 0x69, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x64, 0x6e, 0x5f, 0x68, 0x6f, 0x73, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x64, 0x6e, 0x48, 0x6f, 0x73, 0x74, 0x12, 0x21, 0x0a,
	0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x75, 0x72, 0x69, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x55, 0x72, 0x69,
	0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x75, 0x72, 0x69, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x55, 0x72, 0x69, 0x12,
	0x20, 0x0a, 0x0c, 0x66, 0x62, 0x5f, 0x73, 0x68, 0x61, 0x72, 0x65, 0x5f, 0x75, 0x72, 0x69, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x66, 0x62, 0x53, 0x68, 0x61, 0x72, 0x65, 0x55, 0x72,
	0x69, 0x12, 0x24, 0x0a, 0x0e, 0x6c, 0x6f, 0x67, 0x5f, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f,
	0x75, 0x72, 0x69, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6c, 0x6f, 0x67, 0x55, 0x70,
	0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72, 0x69, 0x22, 0x58, 0x0a, 0x0e, 0x54, 0x68, 0x69, 0x72, 0x64,
	0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x70, 0x65,
	0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e,
	0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x6f, 0x64, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x6f, 0x64, 0x65, 0x49,
	0x64, 0x22, 0x43, 0x0a, 0x0b, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x18, 0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61,
	0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61,
	0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x22, 0x9b, 0x04, 0x0a, 0x0a, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x42, 0x72, 0x61, 0x6e, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x6f,
	0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x6f, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x6f,
	0x73, 0x5f, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x6f, 0x73, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x38, 0x0a, 0x0c,
	0x61, 0x70, 0x70, 0x5f, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4c, 0x41, 0x4e, 0x47,
	0x55, 0x41, 0x47, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x4c, 0x61,
	0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x73, 0x6f, 0x6c, 0x75,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x6f,
	0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x64, 0x6a, 0x75, 0x73, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x64, 0x6a, 0x75, 0x73,
	0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x64, 0x66, 0x61, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x69, 0x64, 0x66, 0x61, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x5f,
	0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x69, 0x6d, 0x65,
	0x5a, 0x6f, 0x6e, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6d, 0x76, 0x6e, 0x6f, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6d, 0x76, 0x6e, 0x6f, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x6e,
	0x64, 0x72, 0x6f, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x6e, 0x64,
	0x72, 0x6f, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x70, 0x75, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x63, 0x70, 0x75, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74,
	0x78, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x78,
	0x12, 0x10, 0x0a, 0x03, 0x72, 0x61, 0x6d, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x72,
	0x61, 0x6d, 0x12, 0x23, 0x0a, 0x0d, 0x76, 0x69, 0x64, 0x65, 0x6f, 0x5f, 0x61, 0x64, 0x61, 0x70,
	0x74, 0x65, 0x72, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x76, 0x69, 0x64, 0x65, 0x6f,
	0x41, 0x64, 0x61, 0x70, 0x74, 0x65, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x5f, 0x69, 0x70, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x49, 0x70, 0x22, 0xd7, 0x03, 0x0a, 0x0c, 0x52, 0x69, 0x63, 0x68, 0x55, 0x73, 0x65,
	0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3d, 0x0a, 0x0f, 0x62, 0x72, 0x69, 0x65, 0x66, 0x5f, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x72, 0x69, 0x65, 0x66, 0x55, 0x73, 0x65,
	0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0d, 0x62, 0x72, 0x69, 0x65, 0x66, 0x55, 0x73, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x40, 0x0a, 0x10, 0x62, 0x61, 0x6e, 0x5f, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x61, 0x6e, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0e, 0x62, 0x61, 0x6e, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x70, 0x70, 0x5f, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x70, 0x70,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x2b, 0x0a, 0x08, 0x61, 0x63, 0x63, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x41, 0x43, 0x43, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52, 0x07, 0x61, 0x63, 0x63,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x72, 0x65, 0x67,
	0x69, 0x73, 0x74, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x31, 0x0a, 0x08, 0x70, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x4c, 0x41, 0x54, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x38, 0x0a, 0x0c,
	0x61, 0x70, 0x70, 0x5f, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4c, 0x41, 0x4e, 0x47,
	0x55, 0x41, 0x47, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x4c, 0x61,
	0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x72, 0x65, 0x61, 0x6c, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c,
	0x72, 0x65, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x41, 0x75, 0x74, 0x68, 0x12, 0x40, 0x0a, 0x10,
	0x65, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0e,
	0x65, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xb5,
	0x02, 0x0a, 0x0d, 0x42, 0x72, 0x69, 0x65, 0x66, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x76, 0x61,
	0x74, 0x61, 0x72, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61,
	0x76, 0x61, 0x74, 0x61, 0x72, 0x55, 0x72, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x66, 0x72, 0x61, 0x6d,
	0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x12, 0x10,
	0x0a, 0x03, 0x6c, 0x65, 0x76, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6c, 0x65, 0x76,
	0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x26, 0x0a, 0x0f, 0x6c, 0x61,
	0x73, 0x74, 0x5f, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0d, 0x6c, 0x61, 0x73, 0x74, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6c, 0x6f, 0x67, 0x6f, 0x75,
	0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x6c, 0x61,
	0x73, 0x74, 0x4c, 0x6f, 0x67, 0x6f, 0x75, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x10,
	0x6c, 0x61, 0x73, 0x74, 0x5f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6c, 0x61, 0x73, 0x74, 0x44, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x22, 0x3a, 0x0a, 0x0a, 0x52, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x18, 0x0a, 0x07, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x12,
	0x0a, 0x04, 0x43, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x43, 0x69,
	0x74, 0x79, 0x22, 0x33, 0x0a, 0x0e, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x55, 0x73, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x21, 0x0a, 0x0c, 0x6e, 0x6f, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x67,
	0x75, 0x69, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6e, 0x6f, 0x76, 0x69,
	0x63, 0x65, 0x47, 0x75, 0x69, 0x64, 0x65, 0x22, 0x8c, 0x01, 0x0a, 0x0e, 0x42, 0x61, 0x6e, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x70,
	0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x09, 0x42, 0x61, 0x6e, 0x52, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x41, 0x4e, 0x5f, 0x41, 0x43, 0x43, 0x5f, 0x52, 0x45, 0x41, 0x53,
	0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52, 0x09, 0x42, 0x61, 0x6e, 0x52, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x12, 0x22, 0x0a, 0x0c, 0x42, 0x61, 0x6e, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x54, 0x69,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x42, 0x61, 0x6e, 0x4c, 0x6f, 0x67,
	0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x66, 0x0a, 0x06, 0x46, 0x69, 0x73, 0x68, 0x65, 0x72,
	0x12, 0x3d, 0x0a, 0x0f, 0x62, 0x72, 0x69, 0x65, 0x66, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69,
	0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x42, 0x72, 0x69, 0x65, 0x66, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x0d, 0x62, 0x72, 0x69, 0x65, 0x66, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x1d, 0x0a, 0x0a, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x69, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x09, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x69, 0x6e, 0x22, 0xa2,
	0x01, 0x0a, 0x08, 0x52, 0x6f, 0x6f, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x17, 0x0a, 0x07, 0x72,
	0x6f, 0x6f, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x6f,
	0x6f, 0x6d, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x09, 0x72, 0x6f, 0x6f, 0x6d, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x52, 0x4f, 0x4f, 0x4d, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52, 0x08, 0x72, 0x6f, 0x6f, 0x6d,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x6f, 0x6e, 0x64, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x70, 0x6f, 0x6e, 0x64, 0x49, 0x64, 0x12, 0x1b, 0x0a,
	0x09, 0x72, 0x6f, 0x6f, 0x6d, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x08, 0x72, 0x6f, 0x6f, 0x6d, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x73, 0x70,
	0x6f, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x70, 0x6f,
	0x74, 0x49, 0x64, 0x22, 0x49, 0x0a, 0x08, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x13, 0x0a, 0x05, 0x70, 0x6f, 0x73, 0x5f, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x04,
	0x70, 0x6f, 0x73, 0x58, 0x12, 0x13, 0x0a, 0x05, 0x70, 0x6f, 0x73, 0x5f, 0x79, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x04, 0x70, 0x6f, 0x73, 0x59, 0x12, 0x13, 0x0a, 0x05, 0x70, 0x6f, 0x73,
	0x5f, 0x7a, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x04, 0x70, 0x6f, 0x73, 0x5a, 0x22, 0x35,
	0x0a, 0x09, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x13, 0x0a, 0x05, 0x64,
	0x69, 0x72, 0x5f, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x04, 0x64, 0x69, 0x72, 0x58,
	0x12, 0x13, 0x0a, 0x05, 0x64, 0x69, 0x72, 0x5f, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x04, 0x64, 0x69, 0x72, 0x59, 0x22, 0x9b, 0x01, 0x0a, 0x0e, 0x46, 0x69, 0x73, 0x68, 0x65, 0x72,
	0x53, 0x70, 0x6f, 0x74, 0x53, 0x79, 0x6e, 0x63, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x79,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x70, 0x6c, 0x61,
	0x79, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x79, 0x6e, 0x63, 0x5f, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x73, 0x79, 0x6e, 0x63, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x79, 0x6e, 0x63, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x73, 0x79, 0x6e, 0x63, 0x4e, 0x75, 0x6d, 0x12, 0x34, 0x0a,
	0x0b, 0x66, 0x69, 0x73, 0x68, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x46, 0x49, 0x53, 0x48,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x52, 0x0a, 0x66, 0x69, 0x73, 0x68, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x22, 0xa3, 0x02, 0x0a, 0x08, 0x46, 0x69, 0x73, 0x68, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x17, 0x0a, 0x07, 0x66, 0x69, 0x73, 0x68, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x06, 0x66, 0x69, 0x73, 0x68, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x6c, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6c,
	0x65, 0x6e, 0x67, 0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x1b, 0x0a,
	0x09, 0x61, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x08, 0x61, 0x77, 0x61, 0x72, 0x64, 0x4e, 0x75, 0x6d, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x78,
	0x70, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x65, 0x78, 0x70, 0x12, 0x18, 0x0a, 0x07,
	0x73, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x73,
	0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x6f, 0x6d, 0x61, 0x74, 0x6f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x73, 0x6f, 0x6d, 0x61,
	0x74, 0x6f, 0x74, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x71, 0x75, 0x61, 0x6c, 0x69, 0x74,
	0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x71, 0x75, 0x61, 0x6c, 0x69, 0x74, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x67, 0x65, 0x6e, 0x75, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x05, 0x67, 0x65, 0x6e, 0x75, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x75, 0x6e, 0x68, 0x6f, 0x6f, 0x6b,
	0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x75, 0x6e,
	0x68, 0x6f, 0x6f, 0x6b, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22, 0xb1, 0x02, 0x0a, 0x0e, 0x46, 0x69,
	0x73, 0x68, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1f, 0x0a, 0x0b,
	0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x1c, 0x0a,
	0x09, 0x66, 0x72, 0x65, 0x73, 0x68, 0x6e, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x09, 0x66, 0x72, 0x65, 0x73, 0x68, 0x6e, 0x65, 0x73, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x68,
	0x6f, 0x6f, 0x6b, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08,
	0x68, 0x6f, 0x6f, 0x6b, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x77, 0x61, 0x72,
	0x64, 0x5f, 0x70, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x52, 0x08, 0x61, 0x77, 0x61,
	0x72, 0x64, 0x50, 0x65, 0x72, 0x12, 0x2d, 0x0a, 0x09, 0x66, 0x69, 0x73, 0x68, 0x5f, 0x69, 0x6e,
	0x66, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x46, 0x69, 0x73, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x66, 0x69, 0x73, 0x68,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x73, 0x5f, 0x66, 0x69, 0x72, 0x73, 0x74,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x69, 0x73, 0x46, 0x69, 0x72, 0x73, 0x74, 0x12,
	0x17, 0x0a, 0x07, 0x62, 0x61, 0x69, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x06, 0x62, 0x61, 0x69, 0x74, 0x49, 0x64, 0x12, 0x43, 0x0a, 0x11, 0x66, 0x69, 0x73, 0x68,
	0x5f, 0x64, 0x61, 0x6d, 0x61, 0x67, 0x65, 0x64, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x46, 0x69, 0x73,
	0x68, 0x44, 0x61, 0x6d, 0x61, 0x67, 0x65, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0f, 0x66, 0x69,
	0x73, 0x68, 0x44, 0x61, 0x6d, 0x61, 0x67, 0x65, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x53, 0x0a,
	0x0f, 0x46, 0x69, 0x73, 0x68, 0x53, 0x79, 0x6e, 0x63, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c,
	0x12, 0x23, 0x0a, 0x0d, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61,
	0x6c, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x74, 0x6f, 0x70, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x73, 0x74, 0x6f, 0x70, 0x54, 0x69,
	0x6d, 0x65, 0x22, 0x66, 0x0a, 0x10, 0x48, 0x6f, 0x6f, 0x6b, 0x42, 0x61, 0x69, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x50, 0x6f, 0x73, 0x65, 0x12, 0x3c, 0x0a, 0x09, 0x70, 0x6f, 0x73, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x46, 0x49, 0x53, 0x48, 0x49, 0x4e, 0x47, 0x5f, 0x42, 0x41, 0x49, 0x54, 0x5f,
	0x54, 0x52, 0x49, 0x43, 0x4b, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52, 0x08, 0x70, 0x6f, 0x73, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x22, 0x8f, 0x02, 0x0a, 0x0e, 0x48,
	0x6f, 0x6f, 0x6b, 0x48, 0x61, 0x62, 0x69, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x12, 0x1d, 0x0a,
	0x0a, 0x77, 0x61, 0x74, 0x65, 0x72, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x09, 0x77, 0x61, 0x74, 0x65, 0x72, 0x54, 0x65, 0x6d, 0x70, 0x12, 0x3b, 0x0a, 0x0a,
	0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e,
	0x32, 0x1c, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4d, 0x41, 0x50, 0x5f, 0x57, 0x41,
	0x54, 0x45, 0x52, 0x5f, 0x4c, 0x41, 0x59, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52, 0x09,
	0x6c, 0x61, 0x79, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x44, 0x0a, 0x0e, 0x73, 0x74, 0x72,
	0x75, 0x63, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0e, 0x32, 0x1d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x55, 0x4e, 0x44, 0x45, 0x52,
	0x5f, 0x57, 0x41, 0x54, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x52, 0x55, 0x43, 0x54, 0x55, 0x52, 0x45,
	0x52, 0x0d, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x75, 0x72, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12,
	0x3e, 0x0a, 0x0e, 0x62, 0x61, 0x69, 0x74, 0x5f, 0x70, 0x6f, 0x73, 0x65, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x48, 0x6f, 0x6f, 0x6b, 0x42, 0x61, 0x69, 0x74, 0x54, 0x79, 0x70, 0x65, 0x50, 0x6f, 0x73,
	0x65, 0x52, 0x0c, 0x62, 0x61, 0x69, 0x74, 0x50, 0x6f, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x1b, 0x0a, 0x09, 0x6c, 0x6f, 0x67, 0x5f, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x08, 0x6c, 0x6f, 0x67, 0x4c, 0x69, 0x67, 0x68, 0x74, 0x22, 0x37, 0x0a, 0x09,
	0x54, 0x69, 0x6d, 0x65, 0x4f, 0x66, 0x44, 0x61, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x6f, 0x75,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x68, 0x6f, 0x75, 0x72, 0x12, 0x16, 0x0a,
	0x06, 0x6d, 0x69, 0x6e, 0x75, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6d,
	0x69, 0x6e, 0x75, 0x74, 0x65, 0x22, 0xa5, 0x01, 0x0a, 0x0d, 0x54, 0x68, 0x72, 0x6f, 0x77, 0x47,
	0x72, 0x69, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x22, 0x0a, 0x03, 0x70, 0x6f, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x6f,
	0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x03, 0x70, 0x6f, 0x73, 0x12, 0x34, 0x0a, 0x09, 0x61,
	0x72, 0x65, 0x61, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x57, 0x41, 0x54, 0x45, 0x52, 0x5f, 0x41, 0x52,
	0x45, 0x41, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52, 0x08, 0x61, 0x72, 0x65, 0x61, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x3a, 0x0a, 0x0d, 0x6f, 0x62, 0x73, 0x74, 0x61, 0x63, 0x6c, 0x65, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x4f, 0x42, 0x53, 0x54, 0x41, 0x43, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52,
	0x0c, 0x6f, 0x62, 0x73, 0x74, 0x61, 0x63, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x22, 0xb6, 0x01,
	0x0a, 0x11, 0x46, 0x69, 0x73, 0x68, 0x65, 0x72, 0x79, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x32, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x42, 0x72, 0x69, 0x65, 0x66, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x75,
	0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x69, 0x73, 0x68, 0x5f,
	0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x66, 0x69,
	0x73, 0x68, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x73, 0x70, 0x6f, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x70, 0x6f, 0x74, 0x49,
	0x64, 0x12, 0x1b, 0x0a, 0x09, 0x65, 0x78, 0x70, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x65, 0x78, 0x70, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x16,
	0x0a, 0x06, 0x65, 0x6e, 0x65, 0x72, 0x67, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06,
	0x65, 0x6e, 0x65, 0x72, 0x67, 0x79, 0x22, 0x87, 0x02, 0x0a, 0x0e, 0x50, 0x6c, 0x61, 0x79, 0x65,
	0x72, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61,
	0x79, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x70, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x65, 0x78, 0x70, 0x5f, 0x6e, 0x75,
	0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x65, 0x78, 0x70, 0x4e, 0x75, 0x6d, 0x12,
	0x14, 0x0a, 0x05, 0x63, 0x6f, 0x69, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05,
	0x63, 0x6f, 0x69, 0x6e, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x69, 0x61, 0x6d, 0x6f, 0x6e, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x64, 0x69, 0x61, 0x6d, 0x6f, 0x6e, 0x64, 0x12,
	0x1a, 0x0a, 0x08, 0x65, 0x78, 0x70, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x08, 0x65, 0x78, 0x70, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x66, 0x72, 0x61, 0x6d, 0x65,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x17, 0x0a, 0x07, 0x73, 0x68, 0x6f, 0x77, 0x5f,
	0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x68, 0x6f, 0x77, 0x49, 0x64,
	0x22, 0x60, 0x0a, 0x12, 0x45, 0x78, 0x70, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x43, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x65, 0x78, 0x70, 0x5f, 0x6c, 0x65,
	0x76, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x65, 0x78, 0x70, 0x4c, 0x65,
	0x76, 0x65, 0x6c, 0x12, 0x2d, 0x0a, 0x09, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x6c, 0x69, 0x73, 0x74,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x49, 0x74, 0x65, 0x6d, 0x42, 0x61, 0x73, 0x65, 0x52, 0x08, 0x69, 0x74, 0x65, 0x6d, 0x4c, 0x69,
	0x73, 0x74, 0x22, 0xab, 0x01, 0x0a, 0x13, 0x50, 0x6f, 0x6e, 0x64, 0x45, 0x76, 0x65, 0x6e, 0x74,
	0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x39, 0x0a, 0x08, 0x65, 0x76,
	0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x4f, 0x4e, 0x44, 0x5f, 0x45, 0x56, 0x45, 0x4e, 0x54,
	0x5f, 0x43, 0x48, 0x41, 0x4e, 0x47, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52, 0x07, 0x65, 0x76,
	0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x5f,
	0x6e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x62, 0x65, 0x66, 0x6f, 0x72,
	0x65, 0x4e, 0x75, 0x6d, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x66, 0x74, 0x65, 0x72, 0x5f, 0x6e, 0x75,
	0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x61, 0x66, 0x74, 0x65, 0x72, 0x4e, 0x75,
	0x6d, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x4e, 0x75, 0x6d,
	0x22, 0xf2, 0x02, 0x0a, 0x10, 0x46, 0x69, 0x73, 0x68, 0x69, 0x6e, 0x67, 0x45, 0x76, 0x65, 0x6e,
	0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x39, 0x0a, 0x0a, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x46, 0x49, 0x53, 0x48, 0x49, 0x4e, 0x47, 0x5f, 0x45, 0x56, 0x45, 0x4e, 0x54,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x52, 0x09, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x12, 0x40, 0x0a,
	0x08, 0x69, 0x6e, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x25, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x46, 0x69, 0x73, 0x68, 0x69, 0x6e, 0x67,
	0x45, 0x76, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x49, 0x6e, 0x74, 0x44, 0x61, 0x74,
	0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x69, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x49, 0x0a, 0x0b, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x46, 0x69,
	0x73, 0x68, 0x69, 0x6e, 0x67, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x53,
	0x74, 0x72, 0x69, 0x6e, 0x67, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a,
	0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x44, 0x61, 0x74, 0x61, 0x1a, 0x3a, 0x0a, 0x0c, 0x49, 0x6e,
	0x74, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3d, 0x0a, 0x0f, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x80, 0x01, 0x0a, 0x0f, 0x46, 0x69, 0x73, 0x68, 0x44, 0x61,
	0x6d, 0x61, 0x67, 0x65, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2c, 0x0a, 0x12, 0x66, 0x69, 0x73,
	0x68, 0x5f, 0x64, 0x61, 0x6d, 0x61, 0x67, 0x65, 0x64, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x66, 0x69, 0x73, 0x68, 0x44, 0x61, 0x6d, 0x61, 0x67,
	0x65, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x3f, 0x0a, 0x0f, 0x66, 0x69, 0x73, 0x68, 0x5f,
	0x64, 0x61, 0x6d, 0x61, 0x67, 0x65, 0x64, 0x5f, 0x6c, 0x76, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x17, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x46, 0x49, 0x53, 0x48, 0x5f, 0x44,
	0x41, 0x4d, 0x41, 0x47, 0x45, 0x44, 0x5f, 0x4c, 0x56, 0x52, 0x0d, 0x66, 0x69, 0x73, 0x68, 0x44,
	0x61, 0x6d, 0x61, 0x67, 0x65, 0x64, 0x4c, 0x76, 0x22, 0x3c, 0x0a, 0x08, 0x48, 0x6f, 0x6f, 0x6b,
	0x42, 0x61, 0x69, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x6f, 0x6f, 0x6b, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x68, 0x6f, 0x6f, 0x6b, 0x49, 0x64, 0x12, 0x17, 0x0a,
	0x07, 0x62, 0x61, 0x69, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06,
	0x62, 0x61, 0x69, 0x74, 0x49, 0x64, 0x22, 0xa2, 0x03, 0x0a, 0x04, 0x49, 0x74, 0x65, 0x6d, 0x12,
	0x17, 0x0a, 0x07, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x06, 0x69, 0x74, 0x65, 0x6d, 0x49, 0x64, 0x12, 0x3a, 0x0a, 0x0d, 0x69, 0x74, 0x65, 0x6d,
	0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x43, 0x41,
	0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x52, 0x0c, 0x69, 0x74, 0x65, 0x6d, 0x43, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x12, 0x2e, 0x0a, 0x09, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52, 0x08, 0x69, 0x74, 0x65, 0x6d,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x73, 0x75, 0x62,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x69, 0x74, 0x65,
	0x6d, 0x53, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x74, 0x65, 0x6d,
	0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x69, 0x74,
	0x65, 0x6d, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x28, 0x0a, 0x10, 0x69, 0x74, 0x65, 0x6d, 0x5f,
	0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0e, 0x69, 0x74, 0x65, 0x6d, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49,
	0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x2d, 0x0a, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x18, 0x0a, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x17, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x2e,
	0x45, 0x78, 0x74, 0x72, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x05, 0x65, 0x78, 0x74, 0x72,
	0x61, 0x1a, 0x38, 0x0a, 0x0a, 0x45, 0x78, 0x74, 0x72, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x75, 0x0a, 0x08, 0x49,
	0x74, 0x65, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x20, 0x0a, 0x04, 0x69, 0x74, 0x65, 0x6d, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49,
	0x74, 0x65, 0x6d, 0x52, 0x04, 0x69, 0x74, 0x65, 0x6d, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x74, 0x65,
	0x6d, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x69,
	0x74, 0x65, 0x6d, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x28, 0x0a, 0x10, 0x69, 0x74, 0x65, 0x6d,
	0x5f, 0x64, 0x65, 0x6c, 0x74, 0x61, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0e, 0x69, 0x74, 0x65, 0x6d, 0x44, 0x65, 0x6c, 0x74, 0x61, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x22, 0xc7, 0x01, 0x0a, 0x0e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x74, 0x65,
	0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x2d, 0x0a, 0x09, 0x70, 0x72, 0x6f, 0x70, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49,
	0x74, 0x65, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x70, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x2b, 0x0a, 0x08, 0x62, 0x61, 0x67, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x74, 0x65,
	0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x62, 0x61, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x3c,
	0x0a, 0x11, 0x65, 0x71, 0x75, 0x69, 0x70, 0x5f, 0x74, 0x61, 0x63, 0x6b, 0x6c, 0x65, 0x5f, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0f, 0x65, 0x71, 0x75,
	0x69, 0x70, 0x54, 0x61, 0x63, 0x6b, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x44, 0x0a, 0x0a,
	0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x4c, 0x6f, 0x6f, 0x74, 0x12, 0x20, 0x0a, 0x04, 0x69, 0x74,
	0x65, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x69, 0x74, 0x65, 0x6d, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x22, 0x69, 0x0a, 0x0b, 0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x4c, 0x6f, 0x6f, 0x74,
	0x73, 0x12, 0x28, 0x0a, 0x05, 0x6c, 0x6f, 0x6f, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e,
	0x4c, 0x6f, 0x6f, 0x74, 0x52, 0x05, 0x6c, 0x6f, 0x6f, 0x74, 0x73, 0x12, 0x30, 0x0a, 0x06, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x22, 0xe1, 0x01,
	0x0a, 0x06, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6c, 0x61, 0x69,
	0x6d, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6c, 0x61, 0x69, 0x6d,
	0x49, 0x44, 0x12, 0x39, 0x0a, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a,
	0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x35, 0x0a, 0x09, 0x73,
	0x68, 0x6f, 0x77, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x53,
	0x48, 0x4f, 0x57, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52, 0x08, 0x73, 0x68, 0x6f, 0x77, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x2d, 0x0a, 0x09, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49,
	0x74, 0x65, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x69, 0x74, 0x65, 0x6d, 0x4c, 0x69, 0x73,
	0x74, 0x22, 0x6a, 0x0a, 0x0c, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x42, 0x75, 0x79, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x07, 0x67, 0x6f, 0x6f, 0x64, 0x73, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09,
	0x62, 0x75, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x08, 0x62, 0x75, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x12, 0x22, 0x0a, 0x0d, 0x6c, 0x61, 0x73,
	0x74, 0x5f, 0x62, 0x75, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0b, 0x6c, 0x61, 0x73, 0x74, 0x42, 0x75, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x63, 0x0a,
	0x08, 0x49, 0x74, 0x65, 0x6d, 0x42, 0x61, 0x73, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x74, 0x65,
	0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x69, 0x74, 0x65, 0x6d,
	0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x69, 0x74, 0x65, 0x6d, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65,
	0x49, 0x64, 0x22, 0x3d, 0x0a, 0x0c, 0x49, 0x74, 0x65, 0x6d, 0x42, 0x61, 0x73, 0x65, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x2d, 0x0a, 0x09, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49,
	0x74, 0x65, 0x6d, 0x42, 0x61, 0x73, 0x65, 0x52, 0x08, 0x69, 0x74, 0x65, 0x6d, 0x4c, 0x69, 0x73,
	0x74, 0x22, 0x49, 0x0a, 0x0a, 0x49, 0x74, 0x65, 0x6d, 0x43, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x17, 0x0a, 0x07, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x06, 0x69, 0x74, 0x65, 0x6d, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0d, 0x6c, 0x61, 0x73, 0x74,
	0x5f, 0x75, 0x73, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0b, 0x6c, 0x61, 0x73, 0x74, 0x55, 0x73, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x6c, 0x0a, 0x09,
	0x50, 0x6f, 0x6e, 0x64, 0x47, 0x6f, 0x6f, 0x64, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x67, 0x6f, 0x6f,
	0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x67, 0x6f, 0x6f, 0x64,
	0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x6f, 0x75, 0x67, 0x68, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x06, 0x62, 0x6f, 0x75, 0x67, 0x68, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x75,
	0x6d, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x6e, 0x75, 0x6d, 0x73, 0x12, 0x1a,
	0x0a, 0x08, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x08, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x7f, 0x0a, 0x09, 0x50, 0x6f,
	0x6e, 0x64, 0x42, 0x61, 0x74, 0x63, 0x68, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x61, 0x74, 0x63, 0x68,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x62, 0x61, 0x74, 0x63, 0x68,
	0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x73, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x74, 0x73, 0x53, 0x74, 0x61, 0x72, 0x74, 0x12, 0x15, 0x0a,
	0x06, 0x74, 0x73, 0x5f, 0x65, 0x6e, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74,
	0x73, 0x45, 0x6e, 0x64, 0x12, 0x25, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x6f, 0x6e, 0x64,
	0x47, 0x6f, 0x6f, 0x64, 0x73, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x4b, 0x0a, 0x09, 0x50,
	0x6f, 0x6e, 0x64, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x6f, 0x6e, 0x64,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x70, 0x6f, 0x6e, 0x64, 0x49,
	0x64, 0x12, 0x25, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x6f, 0x6e, 0x64, 0x42, 0x61, 0x74,
	0x63, 0x68, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0xa1, 0x01, 0x0a, 0x11, 0x50, 0x75, 0x72,
	0x63, 0x68, 0x61, 0x73, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x30,
	0x0a, 0x09, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x52, 0x4f, 0x44, 0x55,
	0x43, 0x54, 0x5f, 0x49, 0x44, 0x52, 0x09, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x44,
	0x12, 0x1e, 0x0a, 0x0a, 0x50, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x49, 0x44, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x50, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x49, 0x44,
	0x12, 0x20, 0x0a, 0x0b, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x79, 0x49, 0x44, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x64, 0x69, 0x74, 0x79,
	0x49, 0x44, 0x12, 0x18, 0x0a, 0x07, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x44, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x07, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x44, 0x22, 0x95, 0x03, 0x0a,
	0x0d, 0x57, 0x65, 0x61, 0x74, 0x68, 0x65, 0x72, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x12, 0x16,
	0x0a, 0x06, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06,
	0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x61, 0x69, 0x72, 0x5f, 0x74, 0x65,
	0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0e, 0x61, 0x69, 0x72, 0x54, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12,
	0x2b, 0x0a, 0x11, 0x77, 0x61, 0x74, 0x65, 0x72, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x77, 0x61, 0x74, 0x65,
	0x72, 0x54, 0x65, 0x6d, 0x70, 0x65, 0x72, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12, 0x1d, 0x0a, 0x0a,
	0x77, 0x69, 0x6e, 0x64, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x77, 0x69, 0x6e, 0x64, 0x53, 0x70, 0x65, 0x65, 0x64, 0x12, 0x3d, 0x0a, 0x0e, 0x77,
	0x69, 0x6e, 0x64, 0x5f, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x44, 0x49, 0x52,
	0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52, 0x0d, 0x77, 0x69, 0x6e,
	0x64, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x26, 0x0a, 0x03, 0x73, 0x6b,
	0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x57, 0x45, 0x41, 0x54, 0x48, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52, 0x03, 0x73,
	0x6b, 0x79, 0x12, 0x26, 0x0a, 0x0f, 0x6c, 0x75, 0x78, 0x5f, 0x69, 0x6e, 0x5f, 0x65, 0x78, 0x70,
	0x6f, 0x73, 0x75, 0x72, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x6c, 0x75, 0x78,
	0x49, 0x6e, 0x45, 0x78, 0x70, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x6c, 0x75,
	0x78, 0x5f, 0x69, 0x6e, 0x5f, 0x73, 0x68, 0x61, 0x64, 0x6f, 0x77, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0b, 0x6c, 0x75, 0x78, 0x49, 0x6e, 0x53, 0x68, 0x61, 0x64, 0x6f, 0x77, 0x12, 0x2d,
	0x0a, 0x12, 0x70, 0x72, 0x65, 0x73, 0x73, 0x75, 0x72, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6c, 0x75,
	0x65, 0x6e, 0x63, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x11, 0x70, 0x72, 0x65, 0x73,
	0x73, 0x75, 0x72, 0x65, 0x49, 0x6e, 0x66, 0x6c, 0x75, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x15, 0x0a,
	0x06, 0x63, 0x66, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x63,
	0x66, 0x67, 0x49, 0x64, 0x22, 0x53, 0x0a, 0x0a, 0x57, 0x65, 0x61, 0x74, 0x68, 0x65, 0x72, 0x44,
	0x61, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x2f, 0x0a, 0x07, 0x70, 0x65, 0x72, 0x69,
	0x6f, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x57, 0x65, 0x61, 0x74, 0x68, 0x65, 0x72, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64,
	0x52, 0x07, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x73, 0x22, 0x5a, 0x0a, 0x08, 0x47, 0x61, 0x6d,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x44, 0x61, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x03, 0x44, 0x61, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x48, 0x6f, 0x75, 0x72, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x48, 0x6f, 0x75, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x4d,
	0x69, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x4d, 0x69, 0x6e, 0x12, 0x16, 0x0a,
	0x06, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x50,
	0x65, 0x72, 0x69, 0x6f, 0x64, 0x22, 0xea, 0x01, 0x0a, 0x0a, 0x52, 0x6f, 0x64, 0x52, 0x69, 0x67,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x69, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x72, 0x69, 0x67, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x15, 0x0a, 0x06, 0x72, 0x6f, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x05, 0x72, 0x6f, 0x64, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x72, 0x65, 0x65, 0x6c, 0x5f, 0x69,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x72, 0x65, 0x65, 0x6c, 0x49, 0x64, 0x12,
	0x17, 0x0a, 0x07, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x06, 0x6c, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x6c, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x6c, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x62, 0x61, 0x69, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x62, 0x61, 0x69, 0x74, 0x49, 0x64, 0x12, 0x19,
	0x0a, 0x08, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x07, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x6f, 0x6f,
	0x6b, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x68, 0x6f, 0x6f, 0x6b,
	0x49, 0x64, 0x22, 0x2c, 0x0a, 0x08, 0x53, 0x74, 0x61, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x10,
	0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x76, 0x61, 0x6c,
	0x22, 0xe4, 0x01, 0x0a, 0x0d, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x75, 0x6c, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x79, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x03, 0x74, 0x79, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x05, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x12, 0x2a, 0x0a, 0x08, 0x61, 0x64, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x0f, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x53, 0x55,
	0x4d, 0x5f, 0x41, 0x44, 0x44, 0x52, 0x07, 0x61, 0x64, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x10,
	0x0a, 0x03, 0x76, 0x61, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x76, 0x61, 0x6c,
	0x12, 0x19, 0x0a, 0x08, 0x63, 0x6f, 0x6e, 0x64, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x64, 0x4b, 0x65, 0x79, 0x12, 0x19, 0x0a, 0x08, 0x63,
	0x6f, 0x6e, 0x64, 0x5f, 0x76, 0x61, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x63,
	0x6f, 0x6e, 0x64, 0x56, 0x61, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xb1, 0x01, 0x0a, 0x0d, 0x53, 0x74, 0x61, 0x74,
	0x73, 0x52, 0x75, 0x6c, 0x65, 0x44, 0x65, 0x73, 0x63, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x79, 0x70,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x74, 0x79, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x66,
	0x69, 0x65, 0x6c, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x66, 0x69, 0x65, 0x6c,
	0x64, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x12, 0x2a, 0x0a, 0x08, 0x61, 0x64, 0x64,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0f, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x53, 0x55, 0x4d, 0x5f, 0x41, 0x44, 0x44, 0x52, 0x07, 0x61, 0x64,
	0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x6f, 0x6e, 0x64, 0x5f, 0x6b, 0x65,
	0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x64, 0x4b, 0x65, 0x79,
	0x12, 0x19, 0x0a, 0x08, 0x63, 0x6f, 0x6e, 0x64, 0x5f, 0x76, 0x61, 0x6c, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x64, 0x56, 0x61, 0x6c, 0x22, 0xc6, 0x01, 0x0a, 0x0a,
	0x52, 0x6f, 0x64, 0x42, 0x61, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x30, 0x0a, 0x04, 0x69, 0x6e,
	0x66, 0x6f, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x52, 0x6f, 0x64, 0x42, 0x61, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x49, 0x6e, 0x66,
	0x6f, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x1b, 0x0a, 0x09, 0x62, 0x61, 0x67, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x08, 0x62, 0x61, 0x67, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x1a, 0x45, 0x0a,
	0x09, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x22, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x22, 0xed, 0x02, 0x0a, 0x09, 0x4d, 0x61, 0x69, 0x6c, 0x42, 0x72, 0x69,
	0x65, 0x66, 0x12, 0x17, 0x0a, 0x07, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x06, 0x6d, 0x61, 0x69, 0x6c, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x69, 0x63, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x2e, 0x0a, 0x09,
	0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x52, 0x08, 0x6d, 0x61, 0x69, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x21, 0x0a,
	0x0c, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x73, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0b, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x73, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65,
	0x6e, 0x64, 0x65, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x65, 0x6e, 0x64,
	0x65, 0x72, 0x12, 0x35, 0x0a, 0x06, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x18, 0x09, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4d, 0x61, 0x69, 0x6c,
	0x42, 0x72, 0x69, 0x65, 0x66, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x06, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x1a, 0x39, 0x0a, 0x0b, 0x45, 0x78, 0x74,
	0x65, 0x6e, 0x64, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x22, 0xc6, 0x01, 0x0a, 0x0e, 0x4d, 0x61, 0x69, 0x6c, 0x41, 0x74, 0x74,
	0x61, 0x63, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3f, 0x0a, 0x0d, 0x61, 0x74, 0x74, 0x61, 0x63,
	0x68, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x41, 0x54, 0x54,
	0x41, 0x43, 0x48, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x52, 0x0c, 0x61, 0x74, 0x74, 0x61,
	0x63, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x43, 0x0a, 0x0c, 0x63, 0x6c, 0x61, 0x69,
	0x6d, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x41, 0x54, 0x54,
	0x41, 0x43, 0x48, 0x5f, 0x43, 0x4c, 0x41, 0x49, 0x4d, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x52, 0x0b, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2e, 0x0a,
	0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x42, 0x61, 0x73, 0x65,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x22, 0xa2, 0x01,
	0x0a, 0x0e, 0x4d, 0x61, 0x69, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x27, 0x0a, 0x05, 0x62, 0x72, 0x69, 0x65, 0x66, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4d, 0x61, 0x69, 0x6c, 0x42, 0x72, 0x69,
	0x65, 0x66, 0x52, 0x05, 0x62, 0x72, 0x69, 0x65, 0x66, 0x12, 0x2e, 0x0a, 0x08, 0x72, 0x65, 0x61,
	0x64, 0x5f, 0x79, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x52, 0x07, 0x72, 0x65, 0x61, 0x64, 0x59, 0x65, 0x74, 0x12, 0x37, 0x0a, 0x0b, 0x61, 0x74, 0x74,
	0x61, 0x63, 0x68, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4d, 0x61, 0x69, 0x6c, 0x41, 0x74, 0x74, 0x61,
	0x63, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x49, 0x6e,
	0x66, 0x6f, 0x22, 0xe3, 0x02, 0x0a, 0x0c, 0x4d, 0x61, 0x69, 0x6c, 0x41, 0x73, 0x73, 0x65, 0x6d,
	0x62, 0x6c, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x09, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52, 0x08, 0x6d, 0x61, 0x69, 0x6c,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x2e, 0x0a, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x49,
	0x74, 0x65, 0x6d, 0x42, 0x61, 0x73, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x07, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x73, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x65, 0x78, 0x70, 0x69,
	0x72, 0x65, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x38, 0x0a, 0x06, 0x65, 0x78, 0x74, 0x65,
	0x6e, 0x64, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x4d, 0x61, 0x69, 0x6c, 0x41, 0x73, 0x73, 0x65, 0x6d, 0x62, 0x6c, 0x79, 0x2e, 0x45,
	0x78, 0x74, 0x65, 0x6e, 0x64, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x65, 0x78, 0x74, 0x65,
	0x6e, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x69, 0x63, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x1a, 0x39, 0x0a,
	0x0b, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xa7, 0x01, 0x0a, 0x16, 0x4d, 0x73, 0x67,
	0x42, 0x72, 0x6f, 0x61, 0x64, 0x63, 0x61, 0x73, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x33, 0x0a, 0x07, 0x62, 0x63, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4d, 0x53,
	0x47, 0x5f, 0x42, 0x52, 0x4f, 0x41, 0x44, 0x43, 0x41, 0x53, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x52, 0x06, 0x62, 0x63, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x74,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x22, 0x3f, 0x0a, 0x08, 0x54, 0x61, 0x73, 0x6b, 0x43, 0x6f, 0x6e, 0x64, 0x12, 0x17,
	0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x06, 0x63, 0x6f, 0x6e, 0x64, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x67, 0x72,
	0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x67, 0x72,
	0x65, 0x73, 0x73, 0x22, 0x7e, 0x0a, 0x08, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x67,
	0x72, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x43, 0x6f, 0x6e, 0x64, 0x52, 0x08, 0x70, 0x72,
	0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x12, 0x2b, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x54, 0x41, 0x53, 0x4b, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x22, 0x8a, 0x01, 0x0a, 0x0c, 0x54, 0x61, 0x73, 0x6b, 0x50, 0x72, 0x6f, 0x67,
	0x72, 0x65, 0x73, 0x73, 0x12, 0x31, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x54, 0x41, 0x53, 0x4b, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x52, 0x08, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x15, 0x0a, 0x06, 0x73, 0x75, 0x62, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x73, 0x75, 0x62, 0x49, 0x64, 0x12, 0x1a,
	0x0a, 0x08, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x65, 0x64, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03,
	0x52, 0x08, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x65, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63,
	0x6f, 0x72, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65,
	0x22, 0x8a, 0x01, 0x0a, 0x0c, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x31, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x52, 0x4f,
	0x47, 0x52, 0x45, 0x53, 0x53, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x12, 0x15, 0x0a, 0x06, 0x73, 0x75, 0x62, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x73, 0x75, 0x62, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x65, 0x64, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x52, 0x08, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x65, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x22, 0xb6, 0x01,
	0x0a, 0x0a, 0x52, 0x61, 0x6e, 0x6b, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x12, 0x29, 0x0a, 0x04,
	0x75, 0x73, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x72, 0x69, 0x65, 0x66, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x12, 0x30, 0x0a, 0x04, 0x69,
	0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x52, 0x61, 0x6e, 0x6b, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x2e, 0x49, 0x6e,
	0x66, 0x6f, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x1a, 0x37, 0x0a,
	0x09, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x29, 0x0a, 0x0a, 0x41, 0x6e, 0x6e, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x75, 0x72,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x55, 0x72,
	0x6c, 0x22, 0x5c, 0x0a, 0x09, 0x41, 0x6e, 0x6e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x32,
	0x0a, 0x09, 0x6a, 0x75, 0x6d, 0x70, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x41, 0x4e, 0x4e, 0x5f, 0x4a,
	0x55, 0x4d, 0x50, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52, 0x08, 0x6a, 0x75, 0x6d, 0x70, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6a, 0x75, 0x6d, 0x70, 0x5f, 0x61, 0x72, 0x67, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6a, 0x75, 0x6d, 0x70, 0x41, 0x72, 0x67, 0x73, 0x22,
	0x75, 0x0a, 0x0d, 0x41, 0x6e, 0x6e, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x12, 0x43, 0x0a, 0x10, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x73, 0x74, 0x72, 0x61,
	0x74, 0x65, 0x67, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x41, 0x4e, 0x4e, 0x5f, 0x44, 0x49, 0x53, 0x50, 0x4c, 0x41, 0x59, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x52, 0x0f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x53, 0x74, 0x72,
	0x61, 0x74, 0x65, 0x67, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x69, 0x6e, 0x5f, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x69, 0x6e, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xd5, 0x02, 0x0a, 0x0c, 0x41, 0x6e, 0x6e, 0x50, 0x6f,
	0x70, 0x75, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72,
	0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72,
	0x69, 0x74, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x49, 0x64, 0x12, 0x33, 0x0a, 0x0b, 0x61, 0x6e, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x41, 0x6e, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x0a, 0x61, 0x6e, 0x6e,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6f, 0x70, 0x5f, 0x73,
	0x74, 0x79, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x6f, 0x70, 0x53,
	0x74, 0x79, 0x6c, 0x65, 0x12, 0x30, 0x0a, 0x0a, 0x61, 0x6e, 0x6e, 0x5f, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x41, 0x6e, 0x6e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x09, 0x61, 0x6e, 0x6e,
	0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3c, 0x0a, 0x0e, 0x61, 0x6e, 0x6e, 0x5f, 0x63, 0x6f,
	0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x41, 0x6e, 0x6e, 0x43, 0x6f, 0x6e, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x0d, 0x61, 0x6e, 0x6e, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x42, 0x41,
	0x5a, 0x3f, 0x67, 0x69, 0x74, 0x2e, 0x6b, 0x65, 0x65, 0x70, 0x66, 0x61, 0x6e, 0x63, 0x79, 0x2e,
	0x78, 0x79, 0x7a, 0x2f, 0x62, 0x61, 0x63, 0x6b, 0x2d, 0x65, 0x6e, 0x64, 0x2f, 0x66, 0x61, 0x6e,
	0x63, 0x79, 0x2d, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x63, 0x70,
	0x62, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x3b, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x50,
	0x42, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_common_proto_rawDescOnce sync.Once
	file_common_proto_rawDescData = file_common_proto_rawDesc
)

func file_common_proto_rawDescGZIP() []byte {
	file_common_proto_rawDescOnce.Do(func() {
		file_common_proto_rawDescData = protoimpl.X.CompressGZIP(file_common_proto_rawDescData)
	})
	return file_common_proto_rawDescData
}

var file_common_proto_msgTypes = make([]protoimpl.MessageInfo, 73)
var file_common_proto_goTypes = []interface{}{
	(*AppUpdateInfo)(nil),          // 0: common.AppUpdateInfo
	(*AppResourceInfo)(nil),        // 1: common.AppResourceInfo
	(*AppAddressInfo)(nil),         // 2: common.AppAddressInfo
	(*ThirdLoginInfo)(nil),         // 3: common.ThirdLoginInfo
	(*AccountInfo)(nil),            // 4: common.AccountInfo
	(*DeviceInfo)(nil),             // 5: common.DeviceInfo
	(*RichUserInfo)(nil),           // 6: common.RichUserInfo
	(*BriefUserInfo)(nil),          // 7: common.BriefUserInfo
	(*RegionInfo)(nil),             // 8: common.RegionInfo
	(*ExtendUserInfo)(nil),         // 9: common.ExtendUserInfo
	(*BanAccountInfo)(nil),         // 10: common.BanAccountInfo
	(*Fisher)(nil),                 // 11: common.Fisher
	(*RoomInfo)(nil),               // 12: common.RoomInfo
	(*Position)(nil),               // 13: common.Position
	(*Direction)(nil),              // 14: common.Direction
	(*FisherSpotSync)(nil),         // 15: common.FisherSpotSync
	(*FishInfo)(nil),               // 16: common.FishInfo
	(*FishDetailInfo)(nil),         // 17: common.FishDetailInfo
	(*FishSyncControl)(nil),        // 18: common.FishSyncControl
	(*HookBaitTypePose)(nil),       // 19: common.HookBaitTypePose
	(*HookHabitParam)(nil),         // 20: common.HookHabitParam
	(*TimeOfDay)(nil),              // 21: common.TimeOfDay
	(*ThrowGridInfo)(nil),          // 22: common.ThrowGridInfo
	(*FisheryPlayerInfo)(nil),      // 23: common.FisheryPlayerInfo
	(*PlayerBaseInfo)(nil),         // 24: common.PlayerBaseInfo
	(*ExpLevelChangeInfo)(nil),     // 25: common.ExpLevelChangeInfo
	(*PondEventChangeInfo)(nil),    // 26: common.PondEventChangeInfo
	(*FishingEventInfo)(nil),       // 27: common.FishingEventInfo
	(*FishDamagedInfo)(nil),        // 28: common.FishDamagedInfo
	(*HookBait)(nil),               // 29: common.HookBait
	(*Item)(nil),                   // 30: common.Item
	(*ItemInfo)(nil),               // 31: common.ItemInfo
	(*PlayerItemInfo)(nil),         // 32: common.PlayerItemInfo
	(*OriginLoot)(nil),             // 33: common.OriginLoot
	(*OriginLoots)(nil),            // 34: common.OriginLoots
	(*Reward)(nil),                 // 35: common.Reward
	(*GoodsBuyInfo)(nil),           // 36: common.GoodsBuyInfo
	(*ItemBase)(nil),               // 37: common.ItemBase
	(*ItemBaseList)(nil),           // 38: common.ItemBaseList
	(*ItemCdInfo)(nil),             // 39: common.ItemCdInfo
	(*PondGoods)(nil),              // 40: common.PondGoods
	(*PondBatch)(nil),              // 41: common.PondBatch
	(*PondStore)(nil),              // 42: common.PondStore
	(*PurchaseOrderInfo)(nil),      // 43: common.PurchaseOrderInfo
	(*WeatherPeriod)(nil),          // 44: common.WeatherPeriod
	(*WeatherDay)(nil),             // 45: common.WeatherDay
	(*GameTime)(nil),               // 46: common.GameTime
	(*RodRigInfo)(nil),             // 47: common.RodRigInfo
	(*StatInfo)(nil),               // 48: common.StatInfo
	(*StatsRuleInfo)(nil),          // 49: common.StatsRuleInfo
	(*StatsRuleDesc)(nil),          // 50: common.StatsRuleDesc
	(*RodBagInfo)(nil),             // 51: common.RodBagInfo
	(*MailBrief)(nil),              // 52: common.MailBrief
	(*MailAttachInfo)(nil),         // 53: common.MailAttachInfo
	(*MailDetailInfo)(nil),         // 54: common.MailDetailInfo
	(*MailAssembly)(nil),           // 55: common.MailAssembly
	(*MsgBroadcastDetailInfo)(nil), // 56: common.MsgBroadcastDetailInfo
	(*TaskCond)(nil),               // 57: common.TaskCond
	(*TaskInfo)(nil),               // 58: common.TaskInfo
	(*TaskProgress)(nil),           // 59: common.TaskProgress
	(*ProgressInfo)(nil),           // 60: common.ProgressInfo
	(*RankPlayer)(nil),             // 61: common.RankPlayer
	(*AnnContent)(nil),             // 62: common.AnnContent
	(*AnnAction)(nil),              // 63: common.AnnAction
	(*AnnConditions)(nil),          // 64: common.AnnConditions
	(*AnnPopupInfo)(nil),           // 65: common.AnnPopupInfo
	nil,                            // 66: common.FishingEventInfo.IntDataEntry
	nil,                            // 67: common.FishingEventInfo.StringDataEntry
	nil,                            // 68: common.Item.ExtraEntry
	nil,                            // 69: common.RodBagInfo.InfoEntry
	nil,                            // 70: common.MailBrief.ExtendEntry
	nil,                            // 71: common.MailAssembly.ExtendEntry
	nil,                            // 72: common.RankPlayer.InfoEntry
	(LANGUAGE_TYPE)(0),             // 73: common.LANGUAGE_TYPE
	(ACC_TYPE)(0),                  // 74: common.ACC_TYPE
	(PLATFORM_TYPE)(0),             // 75: common.PLATFORM_TYPE
	(BAN_ACC_REASON_TYPE)(0),       // 76: common.BAN_ACC_REASON_TYPE
	(ROOM_TYPE)(0),                 // 77: common.ROOM_TYPE
	(FISH_STATUS)(0),               // 78: common.FISH_STATUS
	(FISHING_BAIT_TRICK_TYPE)(0),   // 79: common.FISHING_BAIT_TRICK_TYPE
	(MAP_WATER_LAYER_TYPE)(0),      // 80: common.MAP_WATER_LAYER_TYPE
	(UNDER_WATER_STRUCTURE)(0),     // 81: common.UNDER_WATER_STRUCTURE
	(WATER_AREA_TYPE)(0),           // 82: common.WATER_AREA_TYPE
	(OBSTACLE_TYPE)(0),             // 83: common.OBSTACLE_TYPE
	(POND_EVENT_CHANGE_TYPE)(0),    // 84: common.POND_EVENT_CHANGE_TYPE
	(FISHING_EVENT_TYPE)(0),        // 85: common.FISHING_EVENT_TYPE
	(FISH_DAMAGED_LV)(0),           // 86: common.FISH_DAMAGED_LV
	(ITEM_CATEGORY)(0),             // 87: common.ITEM_CATEGORY
	(ITEM_TYPE)(0),                 // 88: common.ITEM_TYPE
	(ITEM_SOURCE_TYPE)(0),          // 89: common.ITEM_SOURCE_TYPE
	(REWARD_SHOW_TYPE)(0),          // 90: common.REWARD_SHOW_TYPE
	(PRODUCT_ID)(0),                // 91: common.PRODUCT_ID
	(DIRECTION_TYPE)(0),            // 92: common.DIRECTION_TYPE
	(WEATHER_TYPE)(0),              // 93: common.WEATHER_TYPE
	(SUM_ADD)(0),                   // 94: common.SUM_ADD
	(MAIL_TYPE)(0),                 // 95: common.MAIL_TYPE
	(MAIL_ATTACH_STATUS)(0),        // 96: common.MAIL_ATTACH_STATUS
	(MAIL_ATTACH_CLAIM_STATUS)(0),  // 97: common.MAIL_ATTACH_CLAIM_STATUS
	(MAIL_STATUS)(0),               // 98: common.MAIL_STATUS
	(MSG_BROADCAST_TYPE)(0),        // 99: common.MSG_BROADCAST_TYPE
	(TASK_STATUS)(0),               // 100: common.TASK_STATUS
	(TASK_CATEGORY)(0),             // 101: common.TASK_CATEGORY
	(PROGRESS_TYPE)(0),             // 102: common.PROGRESS_TYPE
	(ANN_JUMP_TYPE)(0),             // 103: common.ANN_JUMP_TYPE
	(ANN_DISPLAY_TYPE)(0),          // 104: common.ANN_DISPLAY_TYPE
}
var file_common_proto_depIdxs = []int32{
	73,  // 0: common.DeviceInfo.app_language:type_name -> common.LANGUAGE_TYPE
	7,   // 1: common.RichUserInfo.brief_user_info:type_name -> common.BriefUserInfo
	10,  // 2: common.RichUserInfo.ban_account_info:type_name -> common.BanAccountInfo
	74,  // 3: common.RichUserInfo.acc_type:type_name -> common.ACC_TYPE
	75,  // 4: common.RichUserInfo.platform:type_name -> common.PLATFORM_TYPE
	73,  // 5: common.RichUserInfo.app_language:type_name -> common.LANGUAGE_TYPE
	9,   // 6: common.RichUserInfo.extend_user_info:type_name -> common.ExtendUserInfo
	76,  // 7: common.BanAccountInfo.BanReason:type_name -> common.BAN_ACC_REASON_TYPE
	7,   // 8: common.Fisher.brief_user_info:type_name -> common.BriefUserInfo
	77,  // 9: common.RoomInfo.room_type:type_name -> common.ROOM_TYPE
	78,  // 10: common.FisherSpotSync.fish_status:type_name -> common.FISH_STATUS
	16,  // 11: common.FishDetailInfo.fish_info:type_name -> common.FishInfo
	28,  // 12: common.FishDetailInfo.fish_damaged_info:type_name -> common.FishDamagedInfo
	79,  // 13: common.HookBaitTypePose.pose_type:type_name -> common.FISHING_BAIT_TRICK_TYPE
	80,  // 14: common.HookHabitParam.layer_list:type_name -> common.MAP_WATER_LAYER_TYPE
	81,  // 15: common.HookHabitParam.structure_list:type_name -> common.UNDER_WATER_STRUCTURE
	19,  // 16: common.HookHabitParam.bait_pose_info:type_name -> common.HookBaitTypePose
	13,  // 17: common.ThrowGridInfo.pos:type_name -> common.Position
	82,  // 18: common.ThrowGridInfo.area_type:type_name -> common.WATER_AREA_TYPE
	83,  // 19: common.ThrowGridInfo.obstacle_type:type_name -> common.OBSTACLE_TYPE
	7,   // 20: common.FisheryPlayerInfo.user_info:type_name -> common.BriefUserInfo
	37,  // 21: common.ExpLevelChangeInfo.item_list:type_name -> common.ItemBase
	84,  // 22: common.PondEventChangeInfo.event_id:type_name -> common.POND_EVENT_CHANGE_TYPE
	85,  // 23: common.FishingEventInfo.event_type:type_name -> common.FISHING_EVENT_TYPE
	66,  // 24: common.FishingEventInfo.int_data:type_name -> common.FishingEventInfo.IntDataEntry
	67,  // 25: common.FishingEventInfo.string_data:type_name -> common.FishingEventInfo.StringDataEntry
	86,  // 26: common.FishDamagedInfo.fish_damaged_lv:type_name -> common.FISH_DAMAGED_LV
	87,  // 27: common.Item.item_category:type_name -> common.ITEM_CATEGORY
	88,  // 28: common.Item.item_type:type_name -> common.ITEM_TYPE
	68,  // 29: common.Item.extra:type_name -> common.Item.ExtraEntry
	30,  // 30: common.ItemInfo.item:type_name -> common.Item
	31,  // 31: common.PlayerItemInfo.prop_list:type_name -> common.ItemInfo
	31,  // 32: common.PlayerItemInfo.bag_list:type_name -> common.ItemInfo
	31,  // 33: common.PlayerItemInfo.equip_tackle_list:type_name -> common.ItemInfo
	30,  // 34: common.OriginLoot.item:type_name -> common.Item
	33,  // 35: common.OriginLoots.loots:type_name -> common.OriginLoot
	89,  // 36: common.OriginLoots.source:type_name -> common.ITEM_SOURCE_TYPE
	89,  // 37: common.Reward.source_type:type_name -> common.ITEM_SOURCE_TYPE
	90,  // 38: common.Reward.show_type:type_name -> common.REWARD_SHOW_TYPE
	31,  // 39: common.Reward.item_list:type_name -> common.ItemInfo
	37,  // 40: common.ItemBaseList.item_list:type_name -> common.ItemBase
	40,  // 41: common.PondBatch.list:type_name -> common.PondGoods
	41,  // 42: common.PondStore.list:type_name -> common.PondBatch
	91,  // 43: common.PurchaseOrderInfo.ProductID:type_name -> common.PRODUCT_ID
	92,  // 44: common.WeatherPeriod.wind_direction:type_name -> common.DIRECTION_TYPE
	93,  // 45: common.WeatherPeriod.sky:type_name -> common.WEATHER_TYPE
	44,  // 46: common.WeatherDay.periods:type_name -> common.WeatherPeriod
	94,  // 47: common.StatsRuleInfo.add_type:type_name -> common.SUM_ADD
	94,  // 48: common.StatsRuleDesc.add_type:type_name -> common.SUM_ADD
	69,  // 49: common.RodBagInfo.info:type_name -> common.RodBagInfo.InfoEntry
	95,  // 50: common.MailBrief.mail_type:type_name -> common.MAIL_TYPE
	70,  // 51: common.MailBrief.extend:type_name -> common.MailBrief.ExtendEntry
	96,  // 52: common.MailAttachInfo.attach_status:type_name -> common.MAIL_ATTACH_STATUS
	97,  // 53: common.MailAttachInfo.claim_status:type_name -> common.MAIL_ATTACH_CLAIM_STATUS
	38,  // 54: common.MailAttachInfo.rewards:type_name -> common.ItemBaseList
	52,  // 55: common.MailDetailInfo.brief:type_name -> common.MailBrief
	98,  // 56: common.MailDetailInfo.read_yet:type_name -> common.MAIL_STATUS
	53,  // 57: common.MailDetailInfo.attach_info:type_name -> common.MailAttachInfo
	95,  // 58: common.MailAssembly.mail_type:type_name -> common.MAIL_TYPE
	38,  // 59: common.MailAssembly.rewards:type_name -> common.ItemBaseList
	71,  // 60: common.MailAssembly.extend:type_name -> common.MailAssembly.ExtendEntry
	99,  // 61: common.MsgBroadcastDetailInfo.bc_type:type_name -> common.MSG_BROADCAST_TYPE
	57,  // 62: common.TaskInfo.progress:type_name -> common.TaskCond
	100, // 63: common.TaskInfo.status:type_name -> common.TASK_STATUS
	101, // 64: common.TaskProgress.category:type_name -> common.TASK_CATEGORY
	102, // 65: common.ProgressInfo.category:type_name -> common.PROGRESS_TYPE
	7,   // 66: common.RankPlayer.user:type_name -> common.BriefUserInfo
	72,  // 67: common.RankPlayer.info:type_name -> common.RankPlayer.InfoEntry
	103, // 68: common.AnnAction.jump_type:type_name -> common.ANN_JUMP_TYPE
	104, // 69: common.AnnConditions.display_strategy:type_name -> common.ANN_DISPLAY_TYPE
	62,  // 70: common.AnnPopupInfo.ann_content:type_name -> common.AnnContent
	63,  // 71: common.AnnPopupInfo.ann_action:type_name -> common.AnnAction
	64,  // 72: common.AnnPopupInfo.ann_conditions:type_name -> common.AnnConditions
	30,  // 73: common.RodBagInfo.InfoEntry.value:type_name -> common.Item
	74,  // [74:74] is the sub-list for method output_type
	74,  // [74:74] is the sub-list for method input_type
	74,  // [74:74] is the sub-list for extension type_name
	74,  // [74:74] is the sub-list for extension extendee
	0,   // [0:74] is the sub-list for field type_name
}

func init() { file_common_proto_init() }
func file_common_proto_init() {
	if File_common_proto != nil {
		return
	}
	file_enum_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_common_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppUpdateInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppResourceInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AppAddressInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ThirdLoginInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccountInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeviceInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RichUserInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BriefUserInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegionInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExtendUserInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BanAccountInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Fisher); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RoomInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Position); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Direction); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FisherSpotSync); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FishInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FishDetailInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FishSyncControl); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HookBaitTypePose); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HookHabitParam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TimeOfDay); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ThrowGridInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FisheryPlayerInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlayerBaseInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpLevelChangeInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PondEventChangeInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FishingEventInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FishDamagedInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HookBait); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Item); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ItemInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlayerItemInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OriginLoot); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OriginLoots); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Reward); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GoodsBuyInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ItemBase); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ItemBaseList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ItemCdInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PondGoods); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PondBatch); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PondStore); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PurchaseOrderInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WeatherPeriod); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WeatherDay); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GameTime); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RodRigInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StatInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StatsRuleInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StatsRuleDesc); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RodBagInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MailBrief); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MailAttachInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MailDetailInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MailAssembly); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MsgBroadcastDetailInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskCond); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskProgress); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProgressInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RankPlayer); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AnnContent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AnnAction); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[64].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AnnConditions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_common_proto_msgTypes[65].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AnnPopupInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_common_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   73,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_common_proto_goTypes,
		DependencyIndexes: file_common_proto_depIdxs,
		MessageInfos:      file_common_proto_msgTypes,
	}.Build()
	File_common_proto = out.File
	file_common_proto_rawDesc = nil
	file_common_proto_goTypes = nil
	file_common_proto_depIdxs = nil
}
