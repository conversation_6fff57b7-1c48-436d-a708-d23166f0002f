package dict_redis

const (
	RDBPlayer   = "player"   // 玩家库
	RDBGateway  = "gateway"  // 网关库
	RDBRoom     = "room"     // 房间库
	RDBGame     = "game"     // 游戏库
	RDBLock     = "lock"     // 分布式锁库
	RDBAsset    = "asset"    // 资产库
	RDBTask     = "task"     // 任务
	RDBGeneral  = "general"  // 通用业务
	RDBBiz      = "biz"      // 其它业务
	RDBActivity = "activity" // 活动
)

const (
	RDBPlayerIndex   = 0 // 玩家库
	RDBGatewayIndex  = 1 // 网关库
	RDBRoomIndex     = 2 // Room库
	RDBGameIndex     = 3 // 游戏库
	RDBLockIndex     = 4 // 分布式锁库
	RDBAssetIndex    = 5 // 资产库
	RDBTaskIndex     = 6 // 任务库
	RDBGeneralIndex  = 7 // 通用业务
	RDBBizIndex      = 8 // 其它业务
	RDBActivityIndex = 9 // 活动
)
