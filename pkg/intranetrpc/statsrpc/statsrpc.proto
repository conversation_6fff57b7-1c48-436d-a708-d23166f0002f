syntax = "proto3";

package statsRpc;
option go_package = ".;statsRpc";

import "common.proto";
import "errors.proto";
import "enum.proto";

message GetStatsListReq {
    int32                   product_id               = 1;  // 产品 ID
    uint64                  player_id                = 2;  // 玩家id
}

message GetStatsListRsp {
    common.Result ret                        = 1;
    int32                   product_id               = 2;  // 产品 ID
    uint64                  player_id                = 3;  // 玩家id
    repeated common.StatInfo list = 4; // 统计信息
}

// 获取统计规则
message GetStatsRulesReq {
    uint64         player_id = 1;  // 玩家id
    repeated common.StatsRuleDesc list = 2; // 统计规则描述
}

// 获取统计规则应答
message GetStatsRulesRsp {
    common.Result ret                      = 1;
    repeated      common.StatsRuleInfo list = 2;  // 统计规则
}


// RPC服务
service statsService {
    // 获取玩家统计信息
    rpc GetStatsList(GetStatsListReq) returns (GetStatsListRsp);
    // 获取统计规则
    rpc GetStatsRules(GetStatsRulesReq) returns (GetStatsRulesRsp);
}