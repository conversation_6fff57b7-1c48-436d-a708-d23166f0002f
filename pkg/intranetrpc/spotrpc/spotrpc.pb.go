// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v5.27.0
// source: spotrpc/spotrpc.proto

package spotRpc

import (
	common "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// PondChangeEventNtf 钓场事件变化通知
type PondChangeEventNtf struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerId  uint64                        `protobuf:"varint,1,opt,name=player_id,json=playerId,proto3" json:"player_id,omitempty"`   // 玩家id
	RoomInfo  *common.RoomInfo              `protobuf:"bytes,2,opt,name=room_info,json=roomInfo,proto3" json:"room_info,omitempty"`    // 房间信息
	EventList []*common.PondEventChangeInfo `protobuf:"bytes,3,rep,name=event_list,json=eventList,proto3" json:"event_list,omitempty"` // 事件列表
}

func (x *PondChangeEventNtf) Reset() {
	*x = PondChangeEventNtf{}
	if protoimpl.UnsafeEnabled {
		mi := &file_spotrpc_spotrpc_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PondChangeEventNtf) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PondChangeEventNtf) ProtoMessage() {}

func (x *PondChangeEventNtf) ProtoReflect() protoreflect.Message {
	mi := &file_spotrpc_spotrpc_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PondChangeEventNtf.ProtoReflect.Descriptor instead.
func (*PondChangeEventNtf) Descriptor() ([]byte, []int) {
	return file_spotrpc_spotrpc_proto_rawDescGZIP(), []int{0}
}

func (x *PondChangeEventNtf) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

func (x *PondChangeEventNtf) GetRoomInfo() *common.RoomInfo {
	if x != nil {
		return x.RoomInfo
	}
	return nil
}

func (x *PondChangeEventNtf) GetEventList() []*common.PondEventChangeInfo {
	if x != nil {
		return x.EventList
	}
	return nil
}

// 鱼护中鱼详细信息查询
type KeepnetFishInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *KeepnetFishInfoReq) Reset() {
	*x = KeepnetFishInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_spotrpc_spotrpc_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KeepnetFishInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KeepnetFishInfoReq) ProtoMessage() {}

func (x *KeepnetFishInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_spotrpc_spotrpc_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KeepnetFishInfoReq.ProtoReflect.Descriptor instead.
func (*KeepnetFishInfoReq) Descriptor() ([]byte, []int) {
	return file_spotrpc_spotrpc_proto_rawDescGZIP(), []int{1}
}

// 鱼护中鱼详细信息响应
type KeepnetFishInfoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FishInfo []*common.FishDetailInfo `protobuf:"bytes,1,rep,name=fish_info,json=fishInfo,proto3" json:"fish_info,omitempty"` // 鱼信息
}

func (x *KeepnetFishInfoRsp) Reset() {
	*x = KeepnetFishInfoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_spotrpc_spotrpc_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KeepnetFishInfoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KeepnetFishInfoRsp) ProtoMessage() {}

func (x *KeepnetFishInfoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_spotrpc_spotrpc_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KeepnetFishInfoRsp.ProtoReflect.Descriptor instead.
func (*KeepnetFishInfoRsp) Descriptor() ([]byte, []int) {
	return file_spotrpc_spotrpc_proto_rawDescGZIP(), []int{2}
}

func (x *KeepnetFishInfoRsp) GetFishInfo() []*common.FishDetailInfo {
	if x != nil {
		return x.FishInfo
	}
	return nil
}

// 批量鱼护操作请求
type FishKeepnetBatchOptReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PondId        int64                        `protobuf:"varint,1,opt,name=pond_id,json=pondId,proto3" json:"pond_id,omitempty"`                     // 钓场id
	FishInstances []string                     `protobuf:"bytes,2,rep,name=fish_instances,json=fishInstances,proto3" json:"fish_instances,omitempty"` // 鱼实例id列表
	Action        common.FISH_KEEPNET_OPT_TYPE `protobuf:"varint,3,opt,name=action,proto3,enum=common.FISH_KEEPNET_OPT_TYPE" json:"action,omitempty"` // 操作类型
}

func (x *FishKeepnetBatchOptReq) Reset() {
	*x = FishKeepnetBatchOptReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_spotrpc_spotrpc_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FishKeepnetBatchOptReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FishKeepnetBatchOptReq) ProtoMessage() {}

func (x *FishKeepnetBatchOptReq) ProtoReflect() protoreflect.Message {
	mi := &file_spotrpc_spotrpc_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FishKeepnetBatchOptReq.ProtoReflect.Descriptor instead.
func (*FishKeepnetBatchOptReq) Descriptor() ([]byte, []int) {
	return file_spotrpc_spotrpc_proto_rawDescGZIP(), []int{3}
}

func (x *FishKeepnetBatchOptReq) GetPondId() int64 {
	if x != nil {
		return x.PondId
	}
	return 0
}

func (x *FishKeepnetBatchOptReq) GetFishInstances() []string {
	if x != nil {
		return x.FishInstances
	}
	return nil
}

func (x *FishKeepnetBatchOptReq) GetAction() common.FISH_KEEPNET_OPT_TYPE {
	if x != nil {
		return x.Action
	}
	return common.FISH_KEEPNET_OPT_TYPE(0)
}

// 批量鱼护操作响应
type FishKeepnetBatchOptRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret *common.Result `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"` // 结果
}

func (x *FishKeepnetBatchOptRsp) Reset() {
	*x = FishKeepnetBatchOptRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_spotrpc_spotrpc_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FishKeepnetBatchOptRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FishKeepnetBatchOptRsp) ProtoMessage() {}

func (x *FishKeepnetBatchOptRsp) ProtoReflect() protoreflect.Message {
	mi := &file_spotrpc_spotrpc_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FishKeepnetBatchOptRsp.ProtoReflect.Descriptor instead.
func (*FishKeepnetBatchOptRsp) Descriptor() ([]byte, []int) {
	return file_spotrpc_spotrpc_proto_rawDescGZIP(), []int{4}
}

func (x *FishKeepnetBatchOptRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

var File_spotrpc_spotrpc_proto protoreflect.FileDescriptor

var file_spotrpc_spotrpc_proto_rawDesc = []byte{
	0x0a, 0x15, 0x73, 0x70, 0x6f, 0x74, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x70, 0x6f, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x73, 0x70, 0x6f, 0x74, 0x52, 0x70, 0x63,
	0x1a, 0x0c, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0a,
	0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0c, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x9c, 0x01, 0x0a, 0x12, 0x50, 0x6f, 0x6e,
	0x64, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x4e, 0x74, 0x66, 0x12,
	0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x09,
	0x72, 0x6f, 0x6f, 0x6d, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x6f, 0x6f, 0x6d, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x08, 0x72, 0x6f, 0x6f, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3a, 0x0a, 0x0a, 0x65,
	0x76, 0x65, 0x6e, 0x74, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1b, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x6f, 0x6e, 0x64, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x65, 0x76,
	0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x14, 0x0a, 0x12, 0x4b, 0x65, 0x65, 0x70, 0x6e,
	0x65, 0x74, 0x46, 0x69, 0x73, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x22, 0x49, 0x0a,
	0x12, 0x4b, 0x65, 0x65, 0x70, 0x6e, 0x65, 0x74, 0x46, 0x69, 0x73, 0x68, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x73, 0x70, 0x12, 0x33, 0x0a, 0x09, 0x66, 0x69, 0x73, 0x68, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x46, 0x69, 0x73, 0x68, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08,
	0x66, 0x69, 0x73, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x8f, 0x01, 0x0a, 0x16, 0x46, 0x69, 0x73,
	0x68, 0x4b, 0x65, 0x65, 0x70, 0x6e, 0x65, 0x74, 0x42, 0x61, 0x74, 0x63, 0x68, 0x4f, 0x70, 0x74,
	0x52, 0x65, 0x71, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x6f, 0x6e, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x70, 0x6f, 0x6e, 0x64, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e,
	0x66, 0x69, 0x73, 0x68, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x66, 0x69, 0x73, 0x68, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e,
	0x63, 0x65, 0x73, 0x12, 0x35, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x46, 0x49, 0x53,
	0x48, 0x5f, 0x4b, 0x45, 0x45, 0x50, 0x4e, 0x45, 0x54, 0x5f, 0x4f, 0x50, 0x54, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x3a, 0x0a, 0x16, 0x46, 0x69,
	0x73, 0x68, 0x4b, 0x65, 0x65, 0x70, 0x6e, 0x65, 0x74, 0x42, 0x61, 0x74, 0x63, 0x68, 0x4f, 0x70,
	0x74, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x32, 0xfb, 0x01, 0x0a, 0x0b, 0x53, 0x70, 0x6f, 0x74, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x44, 0x0a, 0x15, 0x50, 0x6f, 0x6e, 0x64, 0x43, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x12,
	0x1b, 0x2e, 0x73, 0x70, 0x6f, 0x74, 0x52, 0x70, 0x63, 0x2e, 0x50, 0x6f, 0x6e, 0x64, 0x43, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x4e, 0x74, 0x66, 0x1a, 0x0e, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x4e, 0x0a, 0x12,
	0x47, 0x65, 0x74, 0x4b, 0x65, 0x65, 0x70, 0x6e, 0x65, 0x74, 0x46, 0x69, 0x73, 0x68, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x1b, 0x2e, 0x73, 0x70, 0x6f, 0x74, 0x52, 0x70, 0x63, 0x2e, 0x4b, 0x65, 0x65,
	0x70, 0x6e, 0x65, 0x74, 0x46, 0x69, 0x73, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x1a,
	0x1b, 0x2e, 0x73, 0x70, 0x6f, 0x74, 0x52, 0x70, 0x63, 0x2e, 0x4b, 0x65, 0x65, 0x70, 0x6e, 0x65,
	0x74, 0x46, 0x69, 0x73, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x12, 0x56, 0x0a, 0x12,
	0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x4b, 0x65, 0x65, 0x70, 0x6e, 0x65, 0x74, 0x46, 0x69,
	0x73, 0x68, 0x12, 0x1f, 0x2e, 0x73, 0x70, 0x6f, 0x74, 0x52, 0x70, 0x63, 0x2e, 0x46, 0x69, 0x73,
	0x68, 0x4b, 0x65, 0x65, 0x70, 0x6e, 0x65, 0x74, 0x42, 0x61, 0x74, 0x63, 0x68, 0x4f, 0x70, 0x74,
	0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x73, 0x70, 0x6f, 0x74, 0x52, 0x70, 0x63, 0x2e, 0x46, 0x69,
	0x73, 0x68, 0x4b, 0x65, 0x65, 0x70, 0x6e, 0x65, 0x74, 0x42, 0x61, 0x74, 0x63, 0x68, 0x4f, 0x70,
	0x74, 0x52, 0x73, 0x70, 0x42, 0x0b, 0x5a, 0x09, 0x2e, 0x3b, 0x73, 0x70, 0x6f, 0x74, 0x52, 0x70,
	0x63, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_spotrpc_spotrpc_proto_rawDescOnce sync.Once
	file_spotrpc_spotrpc_proto_rawDescData = file_spotrpc_spotrpc_proto_rawDesc
)

func file_spotrpc_spotrpc_proto_rawDescGZIP() []byte {
	file_spotrpc_spotrpc_proto_rawDescOnce.Do(func() {
		file_spotrpc_spotrpc_proto_rawDescData = protoimpl.X.CompressGZIP(file_spotrpc_spotrpc_proto_rawDescData)
	})
	return file_spotrpc_spotrpc_proto_rawDescData
}

var file_spotrpc_spotrpc_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_spotrpc_spotrpc_proto_goTypes = []interface{}{
	(*PondChangeEventNtf)(nil),         // 0: spotRpc.PondChangeEventNtf
	(*KeepnetFishInfoReq)(nil),         // 1: spotRpc.KeepnetFishInfoReq
	(*KeepnetFishInfoRsp)(nil),         // 2: spotRpc.KeepnetFishInfoRsp
	(*FishKeepnetBatchOptReq)(nil),     // 3: spotRpc.FishKeepnetBatchOptReq
	(*FishKeepnetBatchOptRsp)(nil),     // 4: spotRpc.FishKeepnetBatchOptRsp
	(*common.RoomInfo)(nil),            // 5: common.RoomInfo
	(*common.PondEventChangeInfo)(nil), // 6: common.PondEventChangeInfo
	(*common.FishDetailInfo)(nil),      // 7: common.FishDetailInfo
	(common.FISH_KEEPNET_OPT_TYPE)(0),  // 8: common.FISH_KEEPNET_OPT_TYPE
	(*common.Result)(nil),              // 9: common.Result
}
var file_spotrpc_spotrpc_proto_depIdxs = []int32{
	5, // 0: spotRpc.PondChangeEventNtf.room_info:type_name -> common.RoomInfo
	6, // 1: spotRpc.PondChangeEventNtf.event_list:type_name -> common.PondEventChangeInfo
	7, // 2: spotRpc.KeepnetFishInfoRsp.fish_info:type_name -> common.FishDetailInfo
	8, // 3: spotRpc.FishKeepnetBatchOptReq.action:type_name -> common.FISH_KEEPNET_OPT_TYPE
	9, // 4: spotRpc.FishKeepnetBatchOptRsp.ret:type_name -> common.Result
	0, // 5: spotRpc.SpotService.PondChangeEventNotify:input_type -> spotRpc.PondChangeEventNtf
	1, // 6: spotRpc.SpotService.GetKeepnetFishInfo:input_type -> spotRpc.KeepnetFishInfoReq
	3, // 7: spotRpc.SpotService.OperateKeepnetFish:input_type -> spotRpc.FishKeepnetBatchOptReq
	9, // 8: spotRpc.SpotService.PondChangeEventNotify:output_type -> common.Result
	2, // 9: spotRpc.SpotService.GetKeepnetFishInfo:output_type -> spotRpc.KeepnetFishInfoRsp
	4, // 10: spotRpc.SpotService.OperateKeepnetFish:output_type -> spotRpc.FishKeepnetBatchOptRsp
	8, // [8:11] is the sub-list for method output_type
	5, // [5:8] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_spotrpc_spotrpc_proto_init() }
func file_spotrpc_spotrpc_proto_init() {
	if File_spotrpc_spotrpc_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_spotrpc_spotrpc_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PondChangeEventNtf); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_spotrpc_spotrpc_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KeepnetFishInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_spotrpc_spotrpc_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KeepnetFishInfoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_spotrpc_spotrpc_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FishKeepnetBatchOptReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_spotrpc_spotrpc_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FishKeepnetBatchOptRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_spotrpc_spotrpc_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_spotrpc_spotrpc_proto_goTypes,
		DependencyIndexes: file_spotrpc_spotrpc_proto_depIdxs,
		MessageInfos:      file_spotrpc_spotrpc_proto_msgTypes,
	}.Build()
	File_spotrpc_spotrpc_proto = out.File
	file_spotrpc_spotrpc_proto_rawDesc = nil
	file_spotrpc_spotrpc_proto_goTypes = nil
	file_spotrpc_spotrpc_proto_depIdxs = nil
}
