syntax = "proto3";

package spotRpc;
option go_package = ".;spotRpc";

import "common.proto";
import "enum.proto";
import "errors.proto";

// PondChangeEventNtf 钓场事件变化通知
message PondChangeEventNtf {
  uint64          player_id                             = 1;  // 玩家id
  common.RoomInfo room_info                             = 2;  // 房间信息
  repeated        common.PondEventChangeInfo event_list = 3;  // 事件列表
}


// 鱼护中鱼详细信息查询
message KeepnetFishInfoReq {
}

// 鱼护中鱼详细信息响应
message KeepnetFishInfoRsp {
  repeated common.FishDetailInfo fish_info = 1;  // 鱼信息
}

// 批量鱼护操作请求
message FishKeepnetBatchOptReq {
  int64                        pond_id               = 1;  // 钓场id
  repeated                     string fish_instances = 2;  // 鱼实例id列表
  common.FISH_KEEPNET_OPT_TYPE action                = 3;  // 操作类型
}

// 批量鱼护操作响应
message FishKeepnetBatchOptRsp {
  common.Result ret = 1;  // 结果
}



// RPC服务
service SpotService {
  // 操作道具请求
  rpc PondChangeEventNotify(PondChangeEventNtf) returns(common.Result);
  // 获取鱼护详细信息
  rpc GetKeepnetFishInfo(KeepnetFishInfoReq) returns (KeepnetFishInfoRsp);
  // 操作鱼护
  rpc OperateKeepnetFish(FishKeepnetBatchOptReq) returns (FishKeepnetBatchOptRsp);
}


