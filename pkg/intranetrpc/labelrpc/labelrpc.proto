syntax = "proto3";

package labelRpc;
option go_package = ".;labelRpc";

import "enum.proto";
import "errors.proto";

// LabelEventInfo 标签事件信息
message LabelEventInfo {
    int32                   product_id = 1;  // 产品id
    uint64                  player_id  = 2;  // 玩家id
    common.LABEL_EVENT_TYPE event_type = 3;  // 事件类型
    int64                   value      = 4;  // 值
}

  // GetPlayerLabelListReq 获取玩家匹配标签列表
message GetPlayerLabelListReq {
    uint64 player_id = 1;  // 玩家id
}

  // GetPlayerLabelListRsp 获取玩家匹配标签列表
message GetPlayerLabelListRsp {
    common.Result ret              = 1;  // 错误码
    repeated      int64 label_list = 2;  // 标签列表(配置的标签id)
}

// RPC服务
service LabelService {
    // 标签事件推送
    rpc PushLabelEvent(LabelEventInfo) returns (common.Result);

    // 查询标签列表
    rpc QueryPlayerLabelList(GetPlayerLabelListReq) returns (GetPlayerLabelListRsp);
}
