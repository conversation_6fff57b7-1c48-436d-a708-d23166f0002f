// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.1
// 	protoc        v5.27.0
// source: labelrpc/labelrpc.proto

package labelRpc

import (
	common "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// LabelEventInfo 标签事件信息
type LabelEventInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProductId int32                   `protobuf:"varint,1,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"`                              // 产品id
	PlayerId  uint64                  `protobuf:"varint,2,opt,name=player_id,json=playerId,proto3" json:"player_id,omitempty"`                                 // 玩家id
	EventType common.LABEL_EVENT_TYPE `protobuf:"varint,3,opt,name=event_type,json=eventType,proto3,enum=common.LABEL_EVENT_TYPE" json:"event_type,omitempty"` // 事件类型
	Value     int64                   `protobuf:"varint,4,opt,name=value,proto3" json:"value,omitempty"`                                                       // 值
}

func (x *LabelEventInfo) Reset() {
	*x = LabelEventInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_labelrpc_labelrpc_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LabelEventInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LabelEventInfo) ProtoMessage() {}

func (x *LabelEventInfo) ProtoReflect() protoreflect.Message {
	mi := &file_labelrpc_labelrpc_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LabelEventInfo.ProtoReflect.Descriptor instead.
func (*LabelEventInfo) Descriptor() ([]byte, []int) {
	return file_labelrpc_labelrpc_proto_rawDescGZIP(), []int{0}
}

func (x *LabelEventInfo) GetProductId() int32 {
	if x != nil {
		return x.ProductId
	}
	return 0
}

func (x *LabelEventInfo) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

func (x *LabelEventInfo) GetEventType() common.LABEL_EVENT_TYPE {
	if x != nil {
		return x.EventType
	}
	return common.LABEL_EVENT_TYPE(0)
}

func (x *LabelEventInfo) GetValue() int64 {
	if x != nil {
		return x.Value
	}
	return 0
}

// GetPlayerLabelListReq 获取玩家匹配标签列表
type GetPlayerLabelListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerId uint64 `protobuf:"varint,1,opt,name=player_id,json=playerId,proto3" json:"player_id,omitempty"` // 玩家id
}

func (x *GetPlayerLabelListReq) Reset() {
	*x = GetPlayerLabelListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_labelrpc_labelrpc_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPlayerLabelListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPlayerLabelListReq) ProtoMessage() {}

func (x *GetPlayerLabelListReq) ProtoReflect() protoreflect.Message {
	mi := &file_labelrpc_labelrpc_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPlayerLabelListReq.ProtoReflect.Descriptor instead.
func (*GetPlayerLabelListReq) Descriptor() ([]byte, []int) {
	return file_labelrpc_labelrpc_proto_rawDescGZIP(), []int{1}
}

func (x *GetPlayerLabelListReq) GetPlayerId() uint64 {
	if x != nil {
		return x.PlayerId
	}
	return 0
}

// GetPlayerLabelListRsp 获取玩家匹配标签列表
type GetPlayerLabelListRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret       *common.Result `protobuf:"bytes,1,opt,name=ret,proto3" json:"ret,omitempty"`                                      // 错误码
	LabelList []int64        `protobuf:"varint,2,rep,packed,name=label_list,json=labelList,proto3" json:"label_list,omitempty"` // 标签列表(配置的标签id)
}

func (x *GetPlayerLabelListRsp) Reset() {
	*x = GetPlayerLabelListRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_labelrpc_labelrpc_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPlayerLabelListRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPlayerLabelListRsp) ProtoMessage() {}

func (x *GetPlayerLabelListRsp) ProtoReflect() protoreflect.Message {
	mi := &file_labelrpc_labelrpc_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPlayerLabelListRsp.ProtoReflect.Descriptor instead.
func (*GetPlayerLabelListRsp) Descriptor() ([]byte, []int) {
	return file_labelrpc_labelrpc_proto_rawDescGZIP(), []int{2}
}

func (x *GetPlayerLabelListRsp) GetRet() *common.Result {
	if x != nil {
		return x.Ret
	}
	return nil
}

func (x *GetPlayerLabelListRsp) GetLabelList() []int64 {
	if x != nil {
		return x.LabelList
	}
	return nil
}

var File_labelrpc_labelrpc_proto protoreflect.FileDescriptor

var file_labelrpc_labelrpc_proto_rawDesc = []byte{
	0x0a, 0x17, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x72, 0x70, 0x63, 0x2f, 0x6c, 0x61, 0x62, 0x65, 0x6c,
	0x72, 0x70, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x6c, 0x61, 0x62, 0x65, 0x6c,
	0x52, 0x70, 0x63, 0x1a, 0x0a, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x0c, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x9b, 0x01,
	0x0a, 0x0e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x12,
	0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x64, 0x12, 0x37, 0x0a, 0x0a,
	0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x18, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4c, 0x41, 0x42, 0x45, 0x4c, 0x5f,
	0x45, 0x56, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x52, 0x09, 0x65, 0x76, 0x65, 0x6e,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x34, 0x0a, 0x15, 0x47,
	0x65, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49,
	0x64, 0x22, 0x58, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x4c, 0x61,
	0x62, 0x65, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x12, 0x20, 0x0a, 0x03, 0x72, 0x65,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x03, 0x72, 0x65, 0x74, 0x12, 0x1d, 0x0a, 0x0a,
	0x6c, 0x61, 0x62, 0x65, 0x6c, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03,
	0x52, 0x09, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x32, 0xa4, 0x01, 0x0a, 0x0c,
	0x4c, 0x61, 0x62, 0x65, 0x6c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x3a, 0x0a, 0x0e,
	0x50, 0x75, 0x73, 0x68, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x18,
	0x2e, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x70, 0x63, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x45,
	0x76, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x0e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x58, 0x0a, 0x14, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x1f, 0x2e, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x70, 0x63, 0x2e, 0x47, 0x65, 0x74, 0x50,
	0x6c, 0x61, 0x79, 0x65, 0x72, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x1a, 0x1f, 0x2e, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x70, 0x63, 0x2e, 0x47, 0x65, 0x74,
	0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x73, 0x70, 0x42, 0x0c, 0x5a, 0x0a, 0x2e, 0x3b, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x52, 0x70, 0x63,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_labelrpc_labelrpc_proto_rawDescOnce sync.Once
	file_labelrpc_labelrpc_proto_rawDescData = file_labelrpc_labelrpc_proto_rawDesc
)

func file_labelrpc_labelrpc_proto_rawDescGZIP() []byte {
	file_labelrpc_labelrpc_proto_rawDescOnce.Do(func() {
		file_labelrpc_labelrpc_proto_rawDescData = protoimpl.X.CompressGZIP(file_labelrpc_labelrpc_proto_rawDescData)
	})
	return file_labelrpc_labelrpc_proto_rawDescData
}

var file_labelrpc_labelrpc_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_labelrpc_labelrpc_proto_goTypes = []interface{}{
	(*LabelEventInfo)(nil),        // 0: labelRpc.LabelEventInfo
	(*GetPlayerLabelListReq)(nil), // 1: labelRpc.GetPlayerLabelListReq
	(*GetPlayerLabelListRsp)(nil), // 2: labelRpc.GetPlayerLabelListRsp
	(common.LABEL_EVENT_TYPE)(0),  // 3: common.LABEL_EVENT_TYPE
	(*common.Result)(nil),         // 4: common.Result
}
var file_labelrpc_labelrpc_proto_depIdxs = []int32{
	3, // 0: labelRpc.LabelEventInfo.event_type:type_name -> common.LABEL_EVENT_TYPE
	4, // 1: labelRpc.GetPlayerLabelListRsp.ret:type_name -> common.Result
	0, // 2: labelRpc.LabelService.PushLabelEvent:input_type -> labelRpc.LabelEventInfo
	1, // 3: labelRpc.LabelService.QueryPlayerLabelList:input_type -> labelRpc.GetPlayerLabelListReq
	4, // 4: labelRpc.LabelService.PushLabelEvent:output_type -> common.Result
	2, // 5: labelRpc.LabelService.QueryPlayerLabelList:output_type -> labelRpc.GetPlayerLabelListRsp
	4, // [4:6] is the sub-list for method output_type
	2, // [2:4] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_labelrpc_labelrpc_proto_init() }
func file_labelrpc_labelrpc_proto_init() {
	if File_labelrpc_labelrpc_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_labelrpc_labelrpc_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LabelEventInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_labelrpc_labelrpc_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPlayerLabelListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_labelrpc_labelrpc_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPlayerLabelListRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_labelrpc_labelrpc_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_labelrpc_labelrpc_proto_goTypes,
		DependencyIndexes: file_labelrpc_labelrpc_proto_depIdxs,
		MessageInfos:      file_labelrpc_labelrpc_proto_msgTypes,
	}.Build()
	File_labelrpc_labelrpc_proto = out.File
	file_labelrpc_labelrpc_proto_rawDesc = nil
	file_labelrpc_labelrpc_proto_goTypes = nil
	file_labelrpc_labelrpc_proto_depIdxs = nil
}
