// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v5.27.0
// source: labelrpc/labelrpc.proto

package labelRpc

import (
	context "context"
	common "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	LabelService_PushLabelEvent_FullMethodName       = "/labelRpc.LabelService/PushLabelEvent"
	LabelService_QueryPlayerLabelList_FullMethodName = "/labelRpc.LabelService/QueryPlayerLabelList"
)

// LabelServiceClient is the client API for LabelService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type LabelServiceClient interface {
	// 标签事件推送
	PushLabelEvent(ctx context.Context, in *LabelEventInfo, opts ...grpc.CallOption) (*common.Result, error)
	// 查询标签列表
	QueryPlayerLabelList(ctx context.Context, in *GetPlayerLabelListReq, opts ...grpc.CallOption) (*GetPlayerLabelListRsp, error)
}

type labelServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewLabelServiceClient(cc grpc.ClientConnInterface) LabelServiceClient {
	return &labelServiceClient{cc}
}

func (c *labelServiceClient) PushLabelEvent(ctx context.Context, in *LabelEventInfo, opts ...grpc.CallOption) (*common.Result, error) {
	out := new(common.Result)
	err := c.cc.Invoke(ctx, LabelService_PushLabelEvent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *labelServiceClient) QueryPlayerLabelList(ctx context.Context, in *GetPlayerLabelListReq, opts ...grpc.CallOption) (*GetPlayerLabelListRsp, error) {
	out := new(GetPlayerLabelListRsp)
	err := c.cc.Invoke(ctx, LabelService_QueryPlayerLabelList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// LabelServiceServer is the server API for LabelService service.
// All implementations should embed UnimplementedLabelServiceServer
// for forward compatibility
type LabelServiceServer interface {
	// 标签事件推送
	PushLabelEvent(context.Context, *LabelEventInfo) (*common.Result, error)
	// 查询标签列表
	QueryPlayerLabelList(context.Context, *GetPlayerLabelListReq) (*GetPlayerLabelListRsp, error)
}

// UnimplementedLabelServiceServer should be embedded to have forward compatible implementations.
type UnimplementedLabelServiceServer struct {
}

func (UnimplementedLabelServiceServer) PushLabelEvent(context.Context, *LabelEventInfo) (*common.Result, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PushLabelEvent not implemented")
}
func (UnimplementedLabelServiceServer) QueryPlayerLabelList(context.Context, *GetPlayerLabelListReq) (*GetPlayerLabelListRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryPlayerLabelList not implemented")
}

// UnsafeLabelServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to LabelServiceServer will
// result in compilation errors.
type UnsafeLabelServiceServer interface {
	mustEmbedUnimplementedLabelServiceServer()
}

func RegisterLabelServiceServer(s grpc.ServiceRegistrar, srv LabelServiceServer) {
	s.RegisterService(&LabelService_ServiceDesc, srv)
}

func _LabelService_PushLabelEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LabelEventInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LabelServiceServer).PushLabelEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LabelService_PushLabelEvent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LabelServiceServer).PushLabelEvent(ctx, req.(*LabelEventInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _LabelService_QueryPlayerLabelList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPlayerLabelListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LabelServiceServer).QueryPlayerLabelList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LabelService_QueryPlayerLabelList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LabelServiceServer).QueryPlayerLabelList(ctx, req.(*GetPlayerLabelListReq))
	}
	return interceptor(ctx, in, info, handler)
}

// LabelService_ServiceDesc is the grpc.ServiceDesc for LabelService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var LabelService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "labelRpc.LabelService",
	HandlerType: (*LabelServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "PushLabelEvent",
			Handler:    _LabelService_PushLabelEvent_Handler,
		},
		{
			MethodName: "QueryPlayerLabelList",
			Handler:    _LabelService_QueryPlayerLabelList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "labelrpc/labelrpc.proto",
}
