// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type StockRelease struct {
	Id        int64 `json:"id"`
	StockId   int64 `json:"stockId"`
	FishId    int64 `json:"fishId"`
	FishEnvId int64 `json:"fishEnvId"`
	ReleaseId int64 `json:"releaseId"`
}

var lockStockRelease sync.RWMutex
var storeStockRelease sync.Map
var strStockRelease string = "stock_release"

func InitStockReleaseCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strStockRelease, watchStockReleaseFunc)
	return LoadAllStockReleaseCfg()
}

func fixKeyStockRelease(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strStockRelease)
}
func watchStockReleaseFunc(key string, js string) {
	mapStockRelease := make(map[int64]*StockRelease)
	errUnmarshal := json.Unmarshal([]byte(js), &mapStockRelease)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeStockRelease.Store(key, mapStockRelease)
}

func GetAllStockRelease(option ...consulconfig.Option) map[int64]*StockRelease {
	fitKey := fixKeyStockRelease(option...)
	store, ok := storeStockRelease.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*StockRelease)
		if ok {
			return storeMap
		}
	}
	lockStockRelease.Lock()
	defer lockStockRelease.Unlock()
	store, ok = storeStockRelease.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*StockRelease)
		if ok {
			return storeMap
		}
	}
	tblStockRelease := make(map[int64]*StockRelease)
	stock_release_str, err := consulconfig.GetInstance().GetConfig(strStockRelease, option...)
	if stock_release_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(stock_release_str), &tblStockRelease)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "stock_release", errUnmarshal)
		return nil
	}
	storeStockRelease.Store(fitKey, tblStockRelease)
	return tblStockRelease
}

func GetStockRelease(id int64, option ...consulconfig.Option) *StockRelease {
	fitKey := fixKeyStockRelease(option...)
	store, ok := storeStockRelease.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*StockRelease)
		if ok {
			return storeMap[id]
		}
	}
	lockStockRelease.Lock()
	defer lockStockRelease.Unlock()
	store, ok = storeStockRelease.Load(fitKey)
	if ok {
		tblStockRelease, ok := store.(*StockRelease)
		if ok {
			return tblStockRelease
		}
	}
	tblStockRelease := make(map[int64]*StockRelease)
	stock_release_str, err := consulconfig.GetInstance().GetConfig(strStockRelease, option...)
	if stock_release_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(stock_release_str), &tblStockRelease)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "stock_release", errUnmarshal)
		return nil
	}
	storeStockRelease.Store(fitKey, tblStockRelease)
	return tblStockRelease[id]
}

func LoadAllStockReleaseCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strStockRelease, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "StockRelease", successChannels)
	return nil
}
