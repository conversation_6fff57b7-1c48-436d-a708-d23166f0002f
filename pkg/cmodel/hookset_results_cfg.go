// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type HooksetResults struct {
	Id                  int32   `json:"id"`
	Name                string  `json:"name"`
	YValueRange         int32   `json:"yValueRange"`
	HooksetSucRate      int32   `json:"hooksetSucRate"`
	UnhookingProportion int32   `json:"unhookingProportion"`
	HooksetResult       int32   `json:"hooksetResult"`
	ResultFactor        float32 `json:"resultFactor"`
}

var lockHooksetResults sync.RWMutex
var storeHooksetResults sync.Map
var strHooksetResults string = "hookset_results"

func InitHooksetResultsCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strHooksetResults, watchHooksetResultsFunc)
	return LoadAllHooksetResultsCfg()
}

func fixKeyHooksetResults(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strHooksetResults)
}
func watchHooksetResultsFunc(key string, js string) {
	mapHooksetResults := make(map[int64]*HooksetResults)
	errUnmarshal := json.Unmarshal([]byte(js), &mapHooksetResults)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeHooksetResults.Store(key, mapHooksetResults)
}

func GetAllHooksetResults(option ...consulconfig.Option) map[int64]*HooksetResults {
	fitKey := fixKeyHooksetResults(option...)
	store, ok := storeHooksetResults.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*HooksetResults)
		if ok {
			return storeMap
		}
	}
	lockHooksetResults.Lock()
	defer lockHooksetResults.Unlock()
	store, ok = storeHooksetResults.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*HooksetResults)
		if ok {
			return storeMap
		}
	}
	tblHooksetResults := make(map[int64]*HooksetResults)
	hookset_results_str, err := consulconfig.GetInstance().GetConfig(strHooksetResults, option...)
	if hookset_results_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(hookset_results_str), &tblHooksetResults)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "hookset_results", errUnmarshal)
		return nil
	}
	storeHooksetResults.Store(fitKey, tblHooksetResults)
	return tblHooksetResults
}

func GetHooksetResults(id int64, option ...consulconfig.Option) *HooksetResults {
	fitKey := fixKeyHooksetResults(option...)
	store, ok := storeHooksetResults.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*HooksetResults)
		if ok {
			return storeMap[id]
		}
	}
	lockHooksetResults.Lock()
	defer lockHooksetResults.Unlock()
	store, ok = storeHooksetResults.Load(fitKey)
	if ok {
		tblHooksetResults, ok := store.(*HooksetResults)
		if ok {
			return tblHooksetResults
		}
	}
	tblHooksetResults := make(map[int64]*HooksetResults)
	hookset_results_str, err := consulconfig.GetInstance().GetConfig(strHooksetResults, option...)
	if hookset_results_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(hookset_results_str), &tblHooksetResults)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "hookset_results", errUnmarshal)
		return nil
	}
	storeHooksetResults.Store(fitKey, tblHooksetResults)
	return tblHooksetResults[id]
}

func LoadAllHooksetResultsCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strHooksetResults, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "HooksetResults", successChannels)
	return nil
}
