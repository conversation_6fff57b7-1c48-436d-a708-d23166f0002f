// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type AuditStrategy struct {
	Id           int64    `json:"id"`
	AuditOpen    bool     `json:"auditOpen"`
	AuditVersion string   `json:"auditVersion"`
	AuditSrvUrl  []string `json:"auditSrvUrl"`
	AuditConfig  string   `json:"auditConfig"`
}

var lockAuditStrategy sync.RWMutex
var storeAuditStrategy sync.Map
var strAuditStrategy string = "audit_strategy"

func InitAuditStrategyCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strAuditStrategy, watchAuditStrategyFunc)
	return LoadAllAuditStrategyCfg()
}

func fixKeyAuditStrategy(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strAuditStrategy)
}
func watchAuditStrategyFunc(key string, js string) {
	store, ok := storeAuditStrategy.Load(key)
	if !ok {
		store = &AuditStrategy{}
	}
	errUnmarshal := json.Unmarshal([]byte(js), &store)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeAuditStrategy.Store(key, store)
}

func GetAuditStrategy(option ...consulconfig.Option) *AuditStrategy {
	fitKey := fixKeyAuditStrategy(option...)
	store, ok := storeAuditStrategy.Load(fitKey)
	if ok {
		tblAuditStrategy, ok := store.(*AuditStrategy)
		if ok {
			return tblAuditStrategy
		}
	}
	lockAuditStrategy.Lock()
	defer lockAuditStrategy.Unlock()
	store, ok = storeAuditStrategy.Load(fitKey)
	if ok {
		tblAuditStrategy, ok := store.(*AuditStrategy)
		if ok {
			return tblAuditStrategy
		}
	}
	tblAuditStrategy := &AuditStrategy{}
	audit_strategy_str, err := consulconfig.GetInstance().GetConfig(strAuditStrategy, option...)
	if audit_strategy_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(audit_strategy_str), &tblAuditStrategy)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v, json:%s", strAuditStrategy, errUnmarshal, audit_strategy_str)
		return nil
	}
	storeAuditStrategy.Store(fitKey, tblAuditStrategy)
	return tblAuditStrategy
}

func LoadAllAuditStrategyCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strAuditStrategy, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "AuditStrategy", successChannels)
	return nil
}
