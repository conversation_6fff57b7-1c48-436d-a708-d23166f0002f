// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type AppUpdateInfo struct {
	Id            int64  `json:"id"`
	Version       string `json:"version"`
	IsPrompt      bool   `json:"isPrompt"`
	IsForce       bool   `json:"isForce"`
	DownloadUrl   string `json:"downloadUrl"`
	TipMsgKey     string `json:"tipMsgKey"`
	MinAppVersion string `json:"minAppVersion"`
	DowntimeStart int64  `json:"downtimeStart"`
	DowntimeEnd   int64  `json:"downtimeEnd"`
}

var lockAppUpdateInfo sync.RWMutex
var storeAppUpdateInfo sync.Map
var strAppUpdateInfo string = "app_update_info"

func InitAppUpdateInfoCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strAppUpdateInfo, watchAppUpdateInfoFunc)
	return LoadAllAppUpdateInfoCfg()
}

func fixKeyAppUpdateInfo(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strAppUpdateInfo)
}
func watchAppUpdateInfoFunc(key string, js string) {
	store, ok := storeAppUpdateInfo.Load(key)
	if !ok {
		store = &AppUpdateInfo{}
	}
	errUnmarshal := json.Unmarshal([]byte(js), &store)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeAppUpdateInfo.Store(key, store)
}

func GetAppUpdateInfo(option ...consulconfig.Option) *AppUpdateInfo {
	fitKey := fixKeyAppUpdateInfo(option...)
	store, ok := storeAppUpdateInfo.Load(fitKey)
	if ok {
		tblAppUpdateInfo, ok := store.(*AppUpdateInfo)
		if ok {
			return tblAppUpdateInfo
		}
	}
	lockAppUpdateInfo.Lock()
	defer lockAppUpdateInfo.Unlock()
	store, ok = storeAppUpdateInfo.Load(fitKey)
	if ok {
		tblAppUpdateInfo, ok := store.(*AppUpdateInfo)
		if ok {
			return tblAppUpdateInfo
		}
	}
	tblAppUpdateInfo := &AppUpdateInfo{}
	app_update_info_str, err := consulconfig.GetInstance().GetConfig(strAppUpdateInfo, option...)
	if app_update_info_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(app_update_info_str), &tblAppUpdateInfo)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v, json:%s", strAppUpdateInfo, errUnmarshal, app_update_info_str)
		return nil
	}
	storeAppUpdateInfo.Store(fitKey, tblAppUpdateInfo)
	return tblAppUpdateInfo
}

func LoadAllAppUpdateInfoCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strAppUpdateInfo, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "AppUpdateInfo", successChannels)
	return nil
}
