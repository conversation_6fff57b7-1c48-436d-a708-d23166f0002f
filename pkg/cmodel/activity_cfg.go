// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type Activity struct {
	Id        int64  `json:"id"`
	Name      string `json:"name"`
	Is<PERSON>oop    bool   `json:"isLoop"`
	CycleDays int32  `json:"cycleDays"`
	OpenAt    int64  `json:"openAt"`
	CloseAt   int64  `json:"closeAt"`
	Lev       int32  `json:"lev"`
	Target    int32  `json:"target"`
	Update    int32  `json:"update"`
}

var lockActivity sync.RWMutex
var storeActivity sync.Map
var strActivity string = "activity"

func InitActivityCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strActivity, watchActivityFunc)
	return LoadAllActivityCfg()
}

func fixKeyActivity(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strActivity)
}
func watchActivityFunc(key string, js string) {
	mapActivity := make(map[int64]*Activity)
	errUnmarshal := json.Unmarshal([]byte(js), &mapActivity)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeActivity.Store(key, mapActivity)
}

func GetAllActivity(option ...consulconfig.Option) map[int64]*Activity {
	fitKey := fixKeyActivity(option...)
	store, ok := storeActivity.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*Activity)
		if ok {
			return storeMap
		}
	}
	lockActivity.Lock()
	defer lockActivity.Unlock()
	store, ok = storeActivity.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*Activity)
		if ok {
			return storeMap
		}
	}
	tblActivity := make(map[int64]*Activity)
	activity_str, err := consulconfig.GetInstance().GetConfig(strActivity, option...)
	if activity_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(activity_str), &tblActivity)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "activity", errUnmarshal)
		return nil
	}
	storeActivity.Store(fitKey, tblActivity)
	return tblActivity
}

func GetActivity(id int64, option ...consulconfig.Option) *Activity {
	fitKey := fixKeyActivity(option...)
	store, ok := storeActivity.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*Activity)
		if ok {
			return storeMap[id]
		}
	}
	lockActivity.Lock()
	defer lockActivity.Unlock()
	store, ok = storeActivity.Load(fitKey)
	if ok {
		tblActivity, ok := store.(*Activity)
		if ok {
			return tblActivity
		}
	}
	tblActivity := make(map[int64]*Activity)
	activity_str, err := consulconfig.GetInstance().GetConfig(strActivity, option...)
	if activity_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(activity_str), &tblActivity)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "activity", errUnmarshal)
		return nil
	}
	storeActivity.Store(fitKey, tblActivity)
	return tblActivity[id]
}

func LoadAllActivityCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strActivity, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "Activity", successChannels)
	return nil
}
