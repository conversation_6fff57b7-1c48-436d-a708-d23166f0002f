// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type StatsRule struct {
	Id      int64  `json:"id"`
	Name    string `json:"name"`
	Mark    string `json:"mark"`
	Typ     int32  `json:"typ"`
	Target  int64  `json:"target"`
	Field   int64  `json:"field"`
	AddRule int32  `json:"addRule"`
	CondKey int32  `json:"condKey"`
	CondVal int64  `json:"condVal"`
}

var lockStatsRule sync.RWMutex
var storeStatsRule sync.Map
var strStatsRule string = "stats_rule"

func InitStatsRuleCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strStatsRule, watchStatsRuleFunc)
	return LoadAllStatsRuleCfg()
}

func fixKeyStatsRule(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strStatsRule)
}
func watchStatsRuleFunc(key string, js string) {
	mapStatsRule := make(map[int64]*StatsRule)
	errUnmarshal := json.Unmarshal([]byte(js), &mapStatsRule)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeStatsRule.Store(key, mapStatsRule)
}

func GetAllStatsRule(option ...consulconfig.Option) map[int64]*StatsRule {
	fitKey := fixKeyStatsRule(option...)
	store, ok := storeStatsRule.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*StatsRule)
		if ok {
			return storeMap
		}
	}
	lockStatsRule.Lock()
	defer lockStatsRule.Unlock()
	store, ok = storeStatsRule.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*StatsRule)
		if ok {
			return storeMap
		}
	}
	tblStatsRule := make(map[int64]*StatsRule)
	stats_rule_str, err := consulconfig.GetInstance().GetConfig(strStatsRule, option...)
	if stats_rule_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(stats_rule_str), &tblStatsRule)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "stats_rule", errUnmarshal)
		return nil
	}
	storeStatsRule.Store(fitKey, tblStatsRule)
	return tblStatsRule
}

func GetStatsRule(id int64, option ...consulconfig.Option) *StatsRule {
	fitKey := fixKeyStatsRule(option...)
	store, ok := storeStatsRule.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*StatsRule)
		if ok {
			return storeMap[id]
		}
	}
	lockStatsRule.Lock()
	defer lockStatsRule.Unlock()
	store, ok = storeStatsRule.Load(fitKey)
	if ok {
		tblStatsRule, ok := store.(*StatsRule)
		if ok {
			return tblStatsRule
		}
	}
	tblStatsRule := make(map[int64]*StatsRule)
	stats_rule_str, err := consulconfig.GetInstance().GetConfig(strStatsRule, option...)
	if stats_rule_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(stats_rule_str), &tblStatsRule)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "stats_rule", errUnmarshal)
		return nil
	}
	storeStatsRule.Store(fitKey, tblStatsRule)
	return tblStatsRule[id]
}

func LoadAllStatsRuleCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strStatsRule, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "StatsRule", successChannels)
	return nil
}
