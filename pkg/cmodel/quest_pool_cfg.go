// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type QuestPool struct {
	Id           int64  `json:"id"`
	Name         string `json:"name"`
	NameLanguage int64  `json:"nameLanguage"`
	DescLanguage int64  `json:"descLanguage"`
	QuestMode    int32  `json:"questMode"`
	PondId       int64  `json:"pondId"`
	Difficulty   int32  `json:"difficulty"`
	Cond         int64  `json:"cond"`
	Reward       int64  `json:"reward"`
	CanTrace     int32  `json:"canTrace"`
	LocationId   int64  `json:"locationId"`
}

var lockQuestPool sync.RWMutex
var storeQuestPool sync.Map
var strQuestPool string = "quest_pool"

func InitQuestPoolCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strQuestPool, watchQuestPoolFunc)
	return LoadAllQuestPoolCfg()
}

func fixKeyQuestPool(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strQuestPool)
}
func watchQuestPoolFunc(key string, js string) {
	mapQuestPool := make(map[int64]*QuestPool)
	errUnmarshal := json.Unmarshal([]byte(js), &mapQuestPool)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeQuestPool.Store(key, mapQuestPool)
}

func GetAllQuestPool(option ...consulconfig.Option) map[int64]*QuestPool {
	fitKey := fixKeyQuestPool(option...)
	store, ok := storeQuestPool.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*QuestPool)
		if ok {
			return storeMap
		}
	}
	lockQuestPool.Lock()
	defer lockQuestPool.Unlock()
	store, ok = storeQuestPool.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*QuestPool)
		if ok {
			return storeMap
		}
	}
	tblQuestPool := make(map[int64]*QuestPool)
	quest_pool_str, err := consulconfig.GetInstance().GetConfig(strQuestPool, option...)
	if quest_pool_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(quest_pool_str), &tblQuestPool)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "quest_pool", errUnmarshal)
		return nil
	}
	storeQuestPool.Store(fitKey, tblQuestPool)
	return tblQuestPool
}

func GetQuestPool(id int64, option ...consulconfig.Option) *QuestPool {
	fitKey := fixKeyQuestPool(option...)
	store, ok := storeQuestPool.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*QuestPool)
		if ok {
			return storeMap[id]
		}
	}
	lockQuestPool.Lock()
	defer lockQuestPool.Unlock()
	store, ok = storeQuestPool.Load(fitKey)
	if ok {
		tblQuestPool, ok := store.(*QuestPool)
		if ok {
			return tblQuestPool
		}
	}
	tblQuestPool := make(map[int64]*QuestPool)
	quest_pool_str, err := consulconfig.GetInstance().GetConfig(strQuestPool, option...)
	if quest_pool_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(quest_pool_str), &tblQuestPool)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "quest_pool", errUnmarshal)
		return nil
	}
	storeQuestPool.Store(fitKey, tblQuestPool)
	return tblQuestPool[id]
}

func LoadAllQuestPoolCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strQuestPool, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "QuestPool", successChannels)
	return nil
}
