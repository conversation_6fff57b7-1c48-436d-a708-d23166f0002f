// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type Progress struct {
	Id           int64  `json:"id"`
	NameLanguage int64  `json:"nameLanguage"`
	Mark         string `json:"mark"`
	Group        int32  `json:"group"`
	SubId        int64  `json:"subId"`
	Score        int64  `json:"score"`
	Reward       int64  `json:"reward"`
}

var lockProgress sync.RWMutex
var storeProgress sync.Map
var strProgress string = "progress"

func InitProgressCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strProgress, watchProgressFunc)
	return LoadAllProgressCfg()
}

func fixKeyProgress(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strProgress)
}
func watchProgressFunc(key string, js string) {
	mapProgress := make(map[int64]*Progress)
	errUnmarshal := json.Unmarshal([]byte(js), &mapProgress)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeProgress.Store(key, mapProgress)
}

func GetAllProgress(option ...consulconfig.Option) map[int64]*Progress {
	fitKey := fixKeyProgress(option...)
	store, ok := storeProgress.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*Progress)
		if ok {
			return storeMap
		}
	}
	lockProgress.Lock()
	defer lockProgress.Unlock()
	store, ok = storeProgress.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*Progress)
		if ok {
			return storeMap
		}
	}
	tblProgress := make(map[int64]*Progress)
	progress_str, err := consulconfig.GetInstance().GetConfig(strProgress, option...)
	if progress_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(progress_str), &tblProgress)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "progress", errUnmarshal)
		return nil
	}
	storeProgress.Store(fitKey, tblProgress)
	return tblProgress
}

func GetProgress(id int64, option ...consulconfig.Option) *Progress {
	fitKey := fixKeyProgress(option...)
	store, ok := storeProgress.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*Progress)
		if ok {
			return storeMap[id]
		}
	}
	lockProgress.Lock()
	defer lockProgress.Unlock()
	store, ok = storeProgress.Load(fitKey)
	if ok {
		tblProgress, ok := store.(*Progress)
		if ok {
			return tblProgress
		}
	}
	tblProgress := make(map[int64]*Progress)
	progress_str, err := consulconfig.GetInstance().GetConfig(strProgress, option...)
	if progress_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(progress_str), &tblProgress)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "progress", errUnmarshal)
		return nil
	}
	storeProgress.Store(fitKey, tblProgress)
	return tblProgress[id]
}

func LoadAllProgressCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strProgress, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "Progress", successChannels)
	return nil
}
