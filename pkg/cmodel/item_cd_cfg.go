// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type ItemCd struct {
	Id       int64 `json:"id"`
	CdSecond int64 `json:"cdSecond"`
}

var lockItemCd sync.RWMutex
var storeItemCd sync.Map
var strItemCd string = "item_cd"

func InitItemCdCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strItemCd, watchItemCdFunc)
	return LoadAllItemCdCfg()
}

func fixKeyItemCd(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strItemCd)
}
func watchItemCdFunc(key string, js string) {
	mapItemCd := make(map[int64]*ItemCd)
	errUnmarshal := json.Unmarshal([]byte(js), &mapItemCd)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeItemCd.Store(key, mapItemCd)
}

func GetAllItemCd(option ...consulconfig.Option) map[int64]*ItemCd {
	fitKey := fixKeyItemCd(option...)
	store, ok := storeItemCd.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*ItemCd)
		if ok {
			return storeMap
		}
	}
	lockItemCd.Lock()
	defer lockItemCd.Unlock()
	store, ok = storeItemCd.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*ItemCd)
		if ok {
			return storeMap
		}
	}
	tblItemCd := make(map[int64]*ItemCd)
	item_cd_str, err := consulconfig.GetInstance().GetConfig(strItemCd, option...)
	if item_cd_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(item_cd_str), &tblItemCd)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "item_cd", errUnmarshal)
		return nil
	}
	storeItemCd.Store(fitKey, tblItemCd)
	return tblItemCd
}

func GetItemCd(id int64, option ...consulconfig.Option) *ItemCd {
	fitKey := fixKeyItemCd(option...)
	store, ok := storeItemCd.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*ItemCd)
		if ok {
			return storeMap[id]
		}
	}
	lockItemCd.Lock()
	defer lockItemCd.Unlock()
	store, ok = storeItemCd.Load(fitKey)
	if ok {
		tblItemCd, ok := store.(*ItemCd)
		if ok {
			return tblItemCd
		}
	}
	tblItemCd := make(map[int64]*ItemCd)
	item_cd_str, err := consulconfig.GetInstance().GetConfig(strItemCd, option...)
	if item_cd_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(item_cd_str), &tblItemCd)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "item_cd", errUnmarshal)
		return nil
	}
	storeItemCd.Store(fitKey, tblItemCd)
	return tblItemCd[id]
}

func LoadAllItemCdCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strItemCd, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "ItemCd", successChannels)
	return nil
}
