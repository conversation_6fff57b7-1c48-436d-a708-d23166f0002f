// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type PondStoreGroups struct {
	GroupId int64 `json:"groupId"`
	Num     int32 `json:"num"`
}

type PondStore struct {
	Id       int64             `json:"id"`
	PondId   int64             `json:"pondId"`
	TimeType int32             `json:"timeType"`
	TsStart  int64             `json:"tsStart"`
	TsEnd    int64             `json:"tsEnd"`
	Groups   []PondStoreGroups `json:"Groups"`
}

var lockPondStore sync.RWMutex
var storePondStore sync.Map
var strPondStore string = "pond_store"

func InitPondStoreCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strPondStore, watchPondStoreFunc)
	return LoadAllPondStoreCfg()
}

func fixKeyPondStore(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strPondStore)
}
func watchPondStoreFunc(key string, js string) {
	mapPondStore := make(map[int64]*PondStore)
	errUnmarshal := json.Unmarshal([]byte(js), &mapPondStore)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storePondStore.Store(key, mapPondStore)
}

func GetAllPondStore(option ...consulconfig.Option) map[int64]*PondStore {
	fitKey := fixKeyPondStore(option...)
	store, ok := storePondStore.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*PondStore)
		if ok {
			return storeMap
		}
	}
	lockPondStore.Lock()
	defer lockPondStore.Unlock()
	store, ok = storePondStore.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*PondStore)
		if ok {
			return storeMap
		}
	}
	tblPondStore := make(map[int64]*PondStore)
	pond_store_str, err := consulconfig.GetInstance().GetConfig(strPondStore, option...)
	if pond_store_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(pond_store_str), &tblPondStore)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "pond_store", errUnmarshal)
		return nil
	}
	storePondStore.Store(fitKey, tblPondStore)
	return tblPondStore
}

func GetPondStore(id int64, option ...consulconfig.Option) *PondStore {
	fitKey := fixKeyPondStore(option...)
	store, ok := storePondStore.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*PondStore)
		if ok {
			return storeMap[id]
		}
	}
	lockPondStore.Lock()
	defer lockPondStore.Unlock()
	store, ok = storePondStore.Load(fitKey)
	if ok {
		tblPondStore, ok := store.(*PondStore)
		if ok {
			return tblPondStore
		}
	}
	tblPondStore := make(map[int64]*PondStore)
	pond_store_str, err := consulconfig.GetInstance().GetConfig(strPondStore, option...)
	if pond_store_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(pond_store_str), &tblPondStore)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "pond_store", errUnmarshal)
		return nil
	}
	storePondStore.Store(fitKey, tblPondStore)
	return tblPondStore[id]
}

func LoadAllPondStoreCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strPondStore, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "PondStore", successChannels)
	return nil
}
