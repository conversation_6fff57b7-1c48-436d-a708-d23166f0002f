// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type Hooks struct {
	Id             int64   `json:"id"`
	Brand          int32   `json:"brand"`
	Series         int32   `json:"series"`
	SizeName       string  `json:"sizeName"`
	SeriesDes      string  `json:"seriesDes"`
	SubType        int32   `json:"subType"`
	Name           string  `json:"name"`
	Mark           string  `json:"mark"`
	ArtId          string  `json:"artId"`
	Size           int32   `json:"size"`
	Weight         int32   `json:"weight"`
	WindageFactor  float32 `json:"windageFactor"`
	BuoyancyFactor float32 `json:"buoyancyFactor"`
	ModelSize      int32   `json:"modelSize"`
}

var lockHooks sync.RWMutex
var storeHooks sync.Map
var strHooks string = "hooks"

func InitHooksCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strHooks, watchHooksFunc)
	return LoadAllHooksCfg()
}

func fixKeyHooks(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strHooks)
}
func watchHooksFunc(key string, js string) {
	mapHooks := make(map[int64]*Hooks)
	errUnmarshal := json.Unmarshal([]byte(js), &mapHooks)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeHooks.Store(key, mapHooks)
}

func GetAllHooks(option ...consulconfig.Option) map[int64]*Hooks {
	fitKey := fixKeyHooks(option...)
	store, ok := storeHooks.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*Hooks)
		if ok {
			return storeMap
		}
	}
	lockHooks.Lock()
	defer lockHooks.Unlock()
	store, ok = storeHooks.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*Hooks)
		if ok {
			return storeMap
		}
	}
	tblHooks := make(map[int64]*Hooks)
	hooks_str, err := consulconfig.GetInstance().GetConfig(strHooks, option...)
	if hooks_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(hooks_str), &tblHooks)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "hooks", errUnmarshal)
		return nil
	}
	storeHooks.Store(fitKey, tblHooks)
	return tblHooks
}

func GetHooks(id int64, option ...consulconfig.Option) *Hooks {
	fitKey := fixKeyHooks(option...)
	store, ok := storeHooks.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*Hooks)
		if ok {
			return storeMap[id]
		}
	}
	lockHooks.Lock()
	defer lockHooks.Unlock()
	store, ok = storeHooks.Load(fitKey)
	if ok {
		tblHooks, ok := store.(*Hooks)
		if ok {
			return tblHooks
		}
	}
	tblHooks := make(map[int64]*Hooks)
	hooks_str, err := consulconfig.GetInstance().GetConfig(strHooks, option...)
	if hooks_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(hooks_str), &tblHooks)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "hooks", errUnmarshal)
		return nil
	}
	storeHooks.Store(fitKey, tblHooks)
	return tblHooks[id]
}

func LoadAllHooksCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strHooks, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "Hooks", successChannels)
	return nil
}
