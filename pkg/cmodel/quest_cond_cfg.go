// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type QuestCondDetails struct {
	Target  int32 `json:"target"`
	Operate int32 `json:"operate"`
	Value   int64 `json:"value"`
}

type QuestCond struct {
	Id           int64              `json:"id"`
	Name         string             `json:"name"`
	Mark         string             `json:"mark"`
	DescLanguage int64              `json:"descLanguage"`
	Details      []QuestCondDetails `json:"details"`
}

var lockQuestCond sync.RWMutex
var storeQuestCond sync.Map
var strQuestCond string = "quest_cond"

func InitQuestCondCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strQuestCond, watchQuestCondFunc)
	return LoadAllQuestCondCfg()
}

func fixKeyQuestCond(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strQuestCond)
}
func watchQuestCondFunc(key string, js string) {
	mapQuestCond := make(map[int64]*QuestCond)
	errUnmarshal := json.Unmarshal([]byte(js), &mapQuestCond)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeQuestCond.Store(key, mapQuestCond)
}

func GetAllQuestCond(option ...consulconfig.Option) map[int64]*QuestCond {
	fitKey := fixKeyQuestCond(option...)
	store, ok := storeQuestCond.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*QuestCond)
		if ok {
			return storeMap
		}
	}
	lockQuestCond.Lock()
	defer lockQuestCond.Unlock()
	store, ok = storeQuestCond.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*QuestCond)
		if ok {
			return storeMap
		}
	}
	tblQuestCond := make(map[int64]*QuestCond)
	quest_cond_str, err := consulconfig.GetInstance().GetConfig(strQuestCond, option...)
	if quest_cond_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(quest_cond_str), &tblQuestCond)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "quest_cond", errUnmarshal)
		return nil
	}
	storeQuestCond.Store(fitKey, tblQuestCond)
	return tblQuestCond
}

func GetQuestCond(id int64, option ...consulconfig.Option) *QuestCond {
	fitKey := fixKeyQuestCond(option...)
	store, ok := storeQuestCond.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*QuestCond)
		if ok {
			return storeMap[id]
		}
	}
	lockQuestCond.Lock()
	defer lockQuestCond.Unlock()
	store, ok = storeQuestCond.Load(fitKey)
	if ok {
		tblQuestCond, ok := store.(*QuestCond)
		if ok {
			return tblQuestCond
		}
	}
	tblQuestCond := make(map[int64]*QuestCond)
	quest_cond_str, err := consulconfig.GetInstance().GetConfig(strQuestCond, option...)
	if quest_cond_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(quest_cond_str), &tblQuestCond)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "quest_cond", errUnmarshal)
		return nil
	}
	storeQuestCond.Store(fitKey, tblQuestCond)
	return tblQuestCond[id]
}

func LoadAllQuestCondCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strQuestCond, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "QuestCond", successChannels)
	return nil
}
