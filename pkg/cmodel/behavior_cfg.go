// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type Behavior struct {
	Id            int32   `json:"id"`
	Name          string  `json:"name"`
	FakeAction    []int32 `json:"fakeAction"`
	RealAction    []int32 `json:"realAction"`
	BaseRiskValue int32   `json:"baseRiskValue"`
}

var lockBehavior sync.RWMutex
var storeBehavior sync.Map
var strBehavior string = "behavior"

func InitBehaviorCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strBehavior, watchBehaviorFunc)
	return LoadAllBehaviorCfg()
}

func fixKeyBehavior(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strBehavior)
}
func watchBehaviorFunc(key string, js string) {
	mapBehavior := make(map[int64]*Behavior)
	errUnmarshal := json.Unmarshal([]byte(js), &mapBehavior)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeBehavior.Store(key, mapBehavior)
}

func GetAllBehavior(option ...consulconfig.Option) map[int64]*Behavior {
	fitKey := fixKeyBehavior(option...)
	store, ok := storeBehavior.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*Behavior)
		if ok {
			return storeMap
		}
	}
	lockBehavior.Lock()
	defer lockBehavior.Unlock()
	store, ok = storeBehavior.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*Behavior)
		if ok {
			return storeMap
		}
	}
	tblBehavior := make(map[int64]*Behavior)
	behavior_str, err := consulconfig.GetInstance().GetConfig(strBehavior, option...)
	if behavior_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(behavior_str), &tblBehavior)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "behavior", errUnmarshal)
		return nil
	}
	storeBehavior.Store(fitKey, tblBehavior)
	return tblBehavior
}

func GetBehavior(id int64, option ...consulconfig.Option) *Behavior {
	fitKey := fixKeyBehavior(option...)
	store, ok := storeBehavior.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*Behavior)
		if ok {
			return storeMap[id]
		}
	}
	lockBehavior.Lock()
	defer lockBehavior.Unlock()
	store, ok = storeBehavior.Load(fitKey)
	if ok {
		tblBehavior, ok := store.(*Behavior)
		if ok {
			return tblBehavior
		}
	}
	tblBehavior := make(map[int64]*Behavior)
	behavior_str, err := consulconfig.GetInstance().GetConfig(strBehavior, option...)
	if behavior_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(behavior_str), &tblBehavior)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "behavior", errUnmarshal)
		return nil
	}
	storeBehavior.Store(fitKey, tblBehavior)
	return tblBehavior[id]
}

func LoadAllBehaviorCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strBehavior, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "Behavior", successChannels)
	return nil
}
