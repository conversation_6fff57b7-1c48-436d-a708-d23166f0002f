// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type BaitTest struct {
	Id            int32 `json:"id"`
	BehaviourId   int32 `json:"behaviourId"`
	ActionId      int32 `json:"actionId"`
	ProbWeight    int32 `json:"probWeight"`
	ProportionMin int32 `json:"proportionMin"`
	ProportionMax int32 `json:"proportionMax"`
	TimeMin       int32 `json:"timeMin"`
	TimeMax       int32 `json:"timeMax"`
	RiskValueCost int32 `json:"riskValueCost"`
	RefreshRate   int32 `json:"refreshRate"`
	EscapeRate    int32 `json:"escapeRate"`
}

var lockBaitTest sync.RWMutex
var storeBaitTest sync.Map
var strBaitTest string = "bait_test"

func InitBaitTestCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strBaitTest, watchBaitTestFunc)
	return LoadAllBaitTestCfg()
}

func fixKeyBaitTest(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strBaitTest)
}
func watchBaitTestFunc(key string, js string) {
	mapBaitTest := make(map[int64]*BaitTest)
	errUnmarshal := json.Unmarshal([]byte(js), &mapBaitTest)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeBaitTest.Store(key, mapBaitTest)
}

func GetAllBaitTest(option ...consulconfig.Option) map[int64]*BaitTest {
	fitKey := fixKeyBaitTest(option...)
	store, ok := storeBaitTest.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*BaitTest)
		if ok {
			return storeMap
		}
	}
	lockBaitTest.Lock()
	defer lockBaitTest.Unlock()
	store, ok = storeBaitTest.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*BaitTest)
		if ok {
			return storeMap
		}
	}
	tblBaitTest := make(map[int64]*BaitTest)
	bait_test_str, err := consulconfig.GetInstance().GetConfig(strBaitTest, option...)
	if bait_test_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(bait_test_str), &tblBaitTest)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "bait_test", errUnmarshal)
		return nil
	}
	storeBaitTest.Store(fitKey, tblBaitTest)
	return tblBaitTest
}

func GetBaitTest(id int64, option ...consulconfig.Option) *BaitTest {
	fitKey := fixKeyBaitTest(option...)
	store, ok := storeBaitTest.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*BaitTest)
		if ok {
			return storeMap[id]
		}
	}
	lockBaitTest.Lock()
	defer lockBaitTest.Unlock()
	store, ok = storeBaitTest.Load(fitKey)
	if ok {
		tblBaitTest, ok := store.(*BaitTest)
		if ok {
			return tblBaitTest
		}
	}
	tblBaitTest := make(map[int64]*BaitTest)
	bait_test_str, err := consulconfig.GetInstance().GetConfig(strBaitTest, option...)
	if bait_test_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(bait_test_str), &tblBaitTest)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "bait_test", errUnmarshal)
		return nil
	}
	storeBaitTest.Store(fitKey, tblBaitTest)
	return tblBaitTest[id]
}

func LoadAllBaitTestCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strBaitTest, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "BaitTest", successChannels)
	return nil
}
