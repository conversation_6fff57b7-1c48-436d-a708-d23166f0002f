// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type ProgressRewardRewards struct {
	ItemId int64 `json:"itemId"`
	Count  int64 `json:"count"`
}

type ProgressReward struct {
	Id      int64                   `json:"id"`
	Name    string                  `json:"name"`
	Mark    string                  `json:"mark"`
	Rewards []ProgressRewardRewards `json:"rewards"`
}

var lockProgressReward sync.RWMutex
var storeProgressReward sync.Map
var strProgressReward string = "progress_reward"

func InitProgressRewardCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strProgressReward, watchProgressRewardFunc)
	return LoadAllProgressRewardCfg()
}

func fixKeyProgressReward(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strProgressReward)
}
func watchProgressRewardFunc(key string, js string) {
	mapProgressReward := make(map[int64]*ProgressReward)
	errUnmarshal := json.Unmarshal([]byte(js), &mapProgressReward)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeProgressReward.Store(key, mapProgressReward)
}

func GetAllProgressReward(option ...consulconfig.Option) map[int64]*ProgressReward {
	fitKey := fixKeyProgressReward(option...)
	store, ok := storeProgressReward.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*ProgressReward)
		if ok {
			return storeMap
		}
	}
	lockProgressReward.Lock()
	defer lockProgressReward.Unlock()
	store, ok = storeProgressReward.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*ProgressReward)
		if ok {
			return storeMap
		}
	}
	tblProgressReward := make(map[int64]*ProgressReward)
	progress_reward_str, err := consulconfig.GetInstance().GetConfig(strProgressReward, option...)
	if progress_reward_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(progress_reward_str), &tblProgressReward)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "progress_reward", errUnmarshal)
		return nil
	}
	storeProgressReward.Store(fitKey, tblProgressReward)
	return tblProgressReward
}

func GetProgressReward(id int64, option ...consulconfig.Option) *ProgressReward {
	fitKey := fixKeyProgressReward(option...)
	store, ok := storeProgressReward.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*ProgressReward)
		if ok {
			return storeMap[id]
		}
	}
	lockProgressReward.Lock()
	defer lockProgressReward.Unlock()
	store, ok = storeProgressReward.Load(fitKey)
	if ok {
		tblProgressReward, ok := store.(*ProgressReward)
		if ok {
			return tblProgressReward
		}
	}
	tblProgressReward := make(map[int64]*ProgressReward)
	progress_reward_str, err := consulconfig.GetInstance().GetConfig(strProgressReward, option...)
	if progress_reward_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(progress_reward_str), &tblProgressReward)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "progress_reward", errUnmarshal)
		return nil
	}
	storeProgressReward.Store(fitKey, tblProgressReward)
	return tblProgressReward[id]
}

func LoadAllProgressRewardCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strProgressReward, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "ProgressReward", successChannels)
	return nil
}
