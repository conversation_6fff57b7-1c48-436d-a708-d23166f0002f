// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type PondStoreGroupDctWgt struct {
	Discount int32 `json:"discount"`
	Weight   int64 `json:"weight"`
}

type PondStoreGroup struct {
	Id         int64                  `json:"id"`
	Group      int64                  `json:"group"`
	GoodsId    int64                  `json:"goodsId"`
	Nums       int32                  `json:"nums"`
	ItemWeight int64                  `json:"itemWeight"`
	CostItem   int64                  `json:"costItem"`
	CostCount  int64                  `json:"costCount"`
	DctWgt     []PondStoreGroupDctWgt `json:"DctWgt"`
}

var lockPondStoreGroup sync.RWMutex
var storePondStoreGroup sync.Map
var strPondStoreGroup string = "pond_store_group"

func InitPondStoreGroupCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strPondStoreGroup, watchPondStoreGroupFunc)
	return LoadAllPondStoreGroupCfg()
}

func fixKeyPondStoreGroup(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strPondStoreGroup)
}
func watchPondStoreGroupFunc(key string, js string) {
	mapPondStoreGroup := make(map[int64]*PondStoreGroup)
	errUnmarshal := json.Unmarshal([]byte(js), &mapPondStoreGroup)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storePondStoreGroup.Store(key, mapPondStoreGroup)
}

func GetAllPondStoreGroup(option ...consulconfig.Option) map[int64]*PondStoreGroup {
	fitKey := fixKeyPondStoreGroup(option...)
	store, ok := storePondStoreGroup.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*PondStoreGroup)
		if ok {
			return storeMap
		}
	}
	lockPondStoreGroup.Lock()
	defer lockPondStoreGroup.Unlock()
	store, ok = storePondStoreGroup.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*PondStoreGroup)
		if ok {
			return storeMap
		}
	}
	tblPondStoreGroup := make(map[int64]*PondStoreGroup)
	pond_store_group_str, err := consulconfig.GetInstance().GetConfig(strPondStoreGroup, option...)
	if pond_store_group_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(pond_store_group_str), &tblPondStoreGroup)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "pond_store_group", errUnmarshal)
		return nil
	}
	storePondStoreGroup.Store(fitKey, tblPondStoreGroup)
	return tblPondStoreGroup
}

func GetPondStoreGroup(id int64, option ...consulconfig.Option) *PondStoreGroup {
	fitKey := fixKeyPondStoreGroup(option...)
	store, ok := storePondStoreGroup.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*PondStoreGroup)
		if ok {
			return storeMap[id]
		}
	}
	lockPondStoreGroup.Lock()
	defer lockPondStoreGroup.Unlock()
	store, ok = storePondStoreGroup.Load(fitKey)
	if ok {
		tblPondStoreGroup, ok := store.(*PondStoreGroup)
		if ok {
			return tblPondStoreGroup
		}
	}
	tblPondStoreGroup := make(map[int64]*PondStoreGroup)
	pond_store_group_str, err := consulconfig.GetInstance().GetConfig(strPondStoreGroup, option...)
	if pond_store_group_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(pond_store_group_str), &tblPondStoreGroup)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "pond_store_group", errUnmarshal)
		return nil
	}
	storePondStoreGroup.Store(fitKey, tblPondStoreGroup)
	return tblPondStoreGroup[id]
}

func LoadAllPondStoreGroupCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strPondStoreGroup, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "PondStoreGroup", successChannels)
	return nil
}
