// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type QuestRewardRewards struct {
	ItemId int64 `json:"itemId"`
	Count  int64 `json:"count"`
}

type QuestReward struct {
	Id      int64                `json:"id"`
	Name    string               `json:"name"`
	Mark    string               `json:"mark"`
	Rewards []QuestRewardRewards `json:"rewards"`
}

var lockQuestReward sync.RWMutex
var storeQuestReward sync.Map
var strQuestReward string = "quest_reward"

func InitQuestRewardCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strQuestReward, watchQuestRewardFunc)
	return LoadAllQuestRewardCfg()
}

func fixKeyQuestReward(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strQuestReward)
}
func watchQuestRewardFunc(key string, js string) {
	mapQuestReward := make(map[int64]*QuestReward)
	errUnmarshal := json.Unmarshal([]byte(js), &mapQuestReward)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeQuestReward.Store(key, mapQuestReward)
}

func GetAllQuestReward(option ...consulconfig.Option) map[int64]*QuestReward {
	fitKey := fixKeyQuestReward(option...)
	store, ok := storeQuestReward.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*QuestReward)
		if ok {
			return storeMap
		}
	}
	lockQuestReward.Lock()
	defer lockQuestReward.Unlock()
	store, ok = storeQuestReward.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*QuestReward)
		if ok {
			return storeMap
		}
	}
	tblQuestReward := make(map[int64]*QuestReward)
	quest_reward_str, err := consulconfig.GetInstance().GetConfig(strQuestReward, option...)
	if quest_reward_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(quest_reward_str), &tblQuestReward)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "quest_reward", errUnmarshal)
		return nil
	}
	storeQuestReward.Store(fitKey, tblQuestReward)
	return tblQuestReward
}

func GetQuestReward(id int64, option ...consulconfig.Option) *QuestReward {
	fitKey := fixKeyQuestReward(option...)
	store, ok := storeQuestReward.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*QuestReward)
		if ok {
			return storeMap[id]
		}
	}
	lockQuestReward.Lock()
	defer lockQuestReward.Unlock()
	store, ok = storeQuestReward.Load(fitKey)
	if ok {
		tblQuestReward, ok := store.(*QuestReward)
		if ok {
			return tblQuestReward
		}
	}
	tblQuestReward := make(map[int64]*QuestReward)
	quest_reward_str, err := consulconfig.GetInstance().GetConfig(strQuestReward, option...)
	if quest_reward_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(quest_reward_str), &tblQuestReward)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "quest_reward", errUnmarshal)
		return nil
	}
	storeQuestReward.Store(fitKey, tblQuestReward)
	return tblQuestReward[id]
}

func LoadAllQuestRewardCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strQuestReward, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "QuestReward", successChannels)
	return nil
}
