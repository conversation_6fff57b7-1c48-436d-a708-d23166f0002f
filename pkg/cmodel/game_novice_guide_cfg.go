// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type GameNoviceGuide struct {
	Id     int64   `json:"id"`
	Mode   int32   `json:"mode"`
	Mark   string  `json:"mark"`
	Type   int32   `json:"type"`
	Args   []int64 `json:"args"`
	GiftId int64   `json:"giftId"`
}

var lockGameNoviceGuide sync.RWMutex
var storeGameNoviceGuide sync.Map
var strGameNoviceGuide string = "game_novice_guide"

func InitGameNoviceGuideCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strGameNoviceGuide, watchGameNoviceGuideFunc)
	return LoadAllGameNoviceGuideCfg()
}

func fixKeyGameNoviceGuide(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strGameNoviceGuide)
}
func watchGameNoviceGuideFunc(key string, js string) {
	mapGameNoviceGuide := make(map[int64]*GameNoviceGuide)
	errUnmarshal := json.Unmarshal([]byte(js), &mapGameNoviceGuide)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeGameNoviceGuide.Store(key, mapGameNoviceGuide)
}

func GetAllGameNoviceGuide(option ...consulconfig.Option) map[int64]*GameNoviceGuide {
	fitKey := fixKeyGameNoviceGuide(option...)
	store, ok := storeGameNoviceGuide.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*GameNoviceGuide)
		if ok {
			return storeMap
		}
	}
	lockGameNoviceGuide.Lock()
	defer lockGameNoviceGuide.Unlock()
	store, ok = storeGameNoviceGuide.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*GameNoviceGuide)
		if ok {
			return storeMap
		}
	}
	tblGameNoviceGuide := make(map[int64]*GameNoviceGuide)
	game_novice_guide_str, err := consulconfig.GetInstance().GetConfig(strGameNoviceGuide, option...)
	if game_novice_guide_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(game_novice_guide_str), &tblGameNoviceGuide)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "game_novice_guide", errUnmarshal)
		return nil
	}
	storeGameNoviceGuide.Store(fitKey, tblGameNoviceGuide)
	return tblGameNoviceGuide
}

func GetGameNoviceGuide(id int64, option ...consulconfig.Option) *GameNoviceGuide {
	fitKey := fixKeyGameNoviceGuide(option...)
	store, ok := storeGameNoviceGuide.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*GameNoviceGuide)
		if ok {
			return storeMap[id]
		}
	}
	lockGameNoviceGuide.Lock()
	defer lockGameNoviceGuide.Unlock()
	store, ok = storeGameNoviceGuide.Load(fitKey)
	if ok {
		tblGameNoviceGuide, ok := store.(*GameNoviceGuide)
		if ok {
			return tblGameNoviceGuide
		}
	}
	tblGameNoviceGuide := make(map[int64]*GameNoviceGuide)
	game_novice_guide_str, err := consulconfig.GetInstance().GetConfig(strGameNoviceGuide, option...)
	if game_novice_guide_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(game_novice_guide_str), &tblGameNoviceGuide)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "game_novice_guide", errUnmarshal)
		return nil
	}
	storeGameNoviceGuide.Store(fitKey, tblGameNoviceGuide)
	return tblGameNoviceGuide[id]
}

func LoadAllGameNoviceGuideCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strGameNoviceGuide, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "GameNoviceGuide", successChannels)
	return nil
}
