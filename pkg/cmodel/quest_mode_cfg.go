// Code generated by tCloud Tools. DO NOT EDIT.

package cmodel

import (
	"encoding/json"
	"fmt"
	consulconfig "git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	logrus "github.com/sirupsen/logrus"
	"sync"
)

type QuestMode struct {
	Id             int64  `json:"id"`
	Name           string `json:"name"`
	Is<PERSON><PERSON>         bool   `json:"isOpen"`
	Lev            int32  `json:"lev"`
	DailyLimit     int32  `json:"dailyLimit"`
	DisplayCount   int32  `json:"displayCount"`
	IsRefresh      int32  `json:"isRefresh"`
	RefreshItem    int64  `json:"refreshItem"`
	RefreshItemUse int64  `json:"refreshItemUse"`
}

var lockQuestMode sync.RWMutex
var storeQuestMode sync.Map
var strQuestMode string = "quest_mode"

func InitQuestModeCfg() error {
	consulconfig.GetInstance().AddWatchHandler(strQuestMode, watchQuestModeFunc)
	return LoadAllQuestModeCfg()
}

func fixKeyQuestMode(option ...consulconfig.Option) string {
	opt := consulconfig.NewOptions(option...)
	return fmt.Sprintf("%d/%d/%s", opt.GetProduct(), opt.GetChannel(), strQuestMode)
}
func watchQuestModeFunc(key string, js string) {
	mapQuestMode := make(map[int64]*QuestMode)
	errUnmarshal := json.Unmarshal([]byte(js), &mapQuestMode)
	if errUnmarshal != nil {
		logrus.Errorf("fail to unmarshal JSON: %v", errUnmarshal)
		return
	}
	storeQuestMode.Store(key, mapQuestMode)
}

func GetAllQuestMode(option ...consulconfig.Option) map[int64]*QuestMode {
	fitKey := fixKeyQuestMode(option...)
	store, ok := storeQuestMode.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*QuestMode)
		if ok {
			return storeMap
		}
	}
	lockQuestMode.Lock()
	defer lockQuestMode.Unlock()
	store, ok = storeQuestMode.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*QuestMode)
		if ok {
			return storeMap
		}
	}
	tblQuestMode := make(map[int64]*QuestMode)
	quest_mode_str, err := consulconfig.GetInstance().GetConfig(strQuestMode, option...)
	if quest_mode_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(quest_mode_str), &tblQuestMode)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "quest_mode", errUnmarshal)
		return nil
	}
	storeQuestMode.Store(fitKey, tblQuestMode)
	return tblQuestMode
}

func GetQuestMode(id int64, option ...consulconfig.Option) *QuestMode {
	fitKey := fixKeyQuestMode(option...)
	store, ok := storeQuestMode.Load(fitKey)
	if ok {
		storeMap, ok := store.(map[int64]*QuestMode)
		if ok {
			return storeMap[id]
		}
	}
	lockQuestMode.Lock()
	defer lockQuestMode.Unlock()
	store, ok = storeQuestMode.Load(fitKey)
	if ok {
		tblQuestMode, ok := store.(*QuestMode)
		if ok {
			return tblQuestMode
		}
	}
	tblQuestMode := make(map[int64]*QuestMode)
	quest_mode_str, err := consulconfig.GetInstance().GetConfig(strQuestMode, option...)
	if quest_mode_str == "" || err != nil {
		logrus.Errorf("fail json.Unmarshal is empty, err:%+v", err)
		return nil
	}
	errUnmarshal := json.Unmarshal([]byte(quest_mode_str), &tblQuestMode)
	if errUnmarshal != nil {
		logrus.Errorf("fail json.Unmarshal %s err: %v", "quest_mode", errUnmarshal)
		return nil
	}
	storeQuestMode.Store(fitKey, tblQuestMode)
	return tblQuestMode[id]
}

func LoadAllQuestModeCfg() error {
	channels := consulconfig.GetInstance().GetAllChannel()
	successChannels := make([]int32, 0)
	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, channel := range channels {
		wg.Add(1)
		go func(chanID int32) {
			defer wg.Done()
			_, err := consulconfig.GetInstance().GetConfig(strQuestMode, consulconfig.WithChannel(chanID))
			if err != nil {
				return
			}
			mu.Lock()
			successChannels = append(successChannels, chanID)
			mu.Unlock()
		}(channel)
	}
	wg.Wait()
	if len(successChannels) == 0 {
		return fmt.Errorf("no config loaded")
	}
	logrus.Infof("successfully loaded %s config for channels: %v", "QuestMode", successChannels)
	return nil
}
