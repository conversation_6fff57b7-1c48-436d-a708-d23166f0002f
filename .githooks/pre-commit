#!/bin/sh

echo "test"
echo `pwd`
os_name=$(uname)

# 先执行 git pull 操作
echo "执行 git pull 拉取最新代码..."
if ! git pull; then
  echo "❌ git pull 失败（可能存在冲突或网络问题）"
  echo "中止推送和后续操作"
  exit 1  # 非0退出码会阻止 push 继续执行
fi


case "$os_name" in
    Linux)
        exec ./wolong_confd_linux.exe
        ;;
    Darwin)
        exec ./wolong_confd_mac.exe
        ;;
    *)
        exec ./wolong_confd_win.exe
        echo "Unknown OS: $os_name"
        ;;
esac
