#!/bin/sh

echo "test"
echo `pwd`
os_name=$(uname)

# 先执行 git pull 操作
echo "执行 git pull 拉取最新代码..."
if ! git pull; then
  echo "❌ git pull 失败（可能存在冲突或网络问题）"
  echo "中止推送和后续操作"
  exit 1  # 非0退出码会阻止 push 继续执行
fi

case "$os_name" in
    Linux)
        echo "检测到 Linux 系统，运行 linux 版本程序..."
        exec ./wolong_confd_linux.exe -plt
        ;;
    Darwin)

        echo "检测到 macOS 系统，运行 mac 版本程序..."
        exec ./wolong_confd_mac.exe -plt
        ;;
    *)
        echo "检测到其他系统$os_name，运行 windows 版本程序..."
        exec ./wolong_confd_win.exe -plt
        ;;
esac
