package main

import (
	"bufio"
	"encoding/json"
	"errors"
	"flag"
	"fmt"
	"github.com/xuri/excelize/v2"
	"io"
	"math/rand"
	"os"
	"path/filepath"
	"sort"
	"strconv"
	"strings"
	"time"
)

// Pond 对应 fish_pond_list.json 的最小字段
type Pond struct {
	ID          int64 `json:"id"`
	FishStockID int64 `json:"fishStockId"`
}

// StockRelease 对应 stock_release.json 的最小字段
type StockRelease struct {
	ID        int64 `json:"id"`
	StockID   int64 `json:"stockId"`
	FishID    int64 `json:"fishId"`
	FishEnvID int64 `json:"fishEnvId"`
	ReleaseID int64 `json:"releaseId"`
}

// FishEnv 对应 fish_env_affinity.json 的最小字段
type FishEnv struct {
	ID                 int64 `json:"id"`
	StructID           int32 `json:"structId"`
	TempID             int32 `json:"tempId"`
	LayerID            int64 `json:"layerId"` // 203xxxx，放大到 int64，满足导出列类型
	LightID            int32 `json:"lightId"`
	BaitCoeffGroup     int32 `json:"baitCoeffGroup"`
	BaitTypeCoeffGroup int32 `json:"baitTypeCoeffGroup"`
	PeriodCoeffGroup   int32 `json:"periodCoeffGroup"`
}

type DataRow struct {
	RowID        int64
	PondID       int64
	FishID       int64
	FishName     string
	PeriodName   string
	WaterLayerID int64
	StructID     int32
	TrueBait     int32
	ProposedBait int32
	StructText   string
	LayerText    string
	PeriodEnum   string
	TrueBaitText string
	LureBaitText string
}

// 质量 -> 物种
type FishQuality struct {
	ID      int64 `json:"id"`
	Species int64 `json:"species"`
}

// 物种 -> 展示名
type FishSpecies struct {
	ID       int64  `json:"id"`
	ShowName string `json:"showName"`
	Name     string `json:"name"`
}

func main() {
	rand.Seed(time.Now().UnixNano())
	var channelFlag string
	var debug bool
	flag.StringVar(&channelFlag, "channel", "", "渠道ID，如 1001/1002/2001。若不指定则交互式输入")
	flag.BoolVar(&debug, "debug", false, "开启调试日志")
	flag.Parse()

	channel := strings.TrimSpace(channelFlag)
	if channel == "" {
		var err error
		channel, err = prompt("请输入渠道(例如 1001/1002/2001): ")
		if err != nil {
			fatalf("读取输入失败: %v", err)
		}
		channel = strings.TrimSpace(channel)
	}
	if channel == "" {
		fatalf("渠道不能为空")
	}

	// 解析工作目录，定位到 configs/data/1/<channel>/
	repoRoot := mustGetRepoRoot()
	dataDir := filepath.Join(repoRoot, "configs", "data", "1", channel)
	mustEnsureDirExists(dataDir)

	// 读取通用枚举字典（渠道通用）
	enumBookPath := filepath.Join(repoRoot, "configs", "xlsx", "enum.xlsx")
	dict := mustLoadEnumDict(enumBookPath)
	if debug {
		fmt.Fprintf(os.Stderr, "enum loaded: struct=%d, waterLayer=%d, hoverLayerType=%d, baitTypes=%d\n", len(dict.structMap), len(dict.layerMap), len(dict.layerTypeName), len(dict.baitTypeName))
	}

	// 读取必要的表
	pondMap := mustReadJSONMap[Pond](filepath.Join(dataDir, "fish_pond_list.json"))
	releaseMap := mustReadJSONMap[StockRelease](filepath.Join(dataDir, "stock_release.json"))
	envMap := mustReadJSONMap[FishEnv](filepath.Join(dataDir, "fish_env_affinity.json"))
	// 旧 map 读取移除，统一使用 RawMessage 版本
	// 使用 RawMessage 重新读取以便派生时完整解码
	structAffinityRaw := mustReadJSONRaw(filepath.Join(dataDir, "struct_affinity.json"))
	waterLayerAffinityRaw := mustReadJSONRaw(filepath.Join(dataDir, "water_layer_affinity.json"))
	baitTypeAffinity := mustReadJSONMap[BaitTypeAffinityRow](filepath.Join(dataDir, "bait_type_affinity.json"))
	envAffinityMap := mustReadJSONMap[EnvMapRow](filepath.Join(dataDir, "env_affinity_map.json"))
	fishQualityMap := mustReadJSONMap[FishQuality](filepath.Join(dataDir, "basic_fish_quality.json"))
	fishSpeciesMap := mustReadJSONMap[FishSpecies](filepath.Join(dataDir, "basic_fish_species.json"))

	// 建立 stockId -> releases 映射
	stockIDToReleases := make(map[int64][]StockRelease)
	for _, sr := range releaseMap {
		stockIDToReleases[sr.StockID] = append(stockIDToReleases[sr.StockID], sr)
	}

	// 有序遍历 pond，保证输出稳定
	pondIDs := make([]int64, 0, len(pondMap))
	for _, p := range pondMap {
		pondIDs = append(pondIDs, p.ID)
	}
	sort.Slice(pondIDs, func(i, j int) bool { return pondIDs[i] < pondIDs[j] })

	// 组装数据行
	rows := make([]DataRow, 0, 1024)
	type aggKey struct {
		pondID   int64
		fishName string
	}
	agg := make(map[aggKey]*aggEntry)
	for _, pid := range pondIDs {
		p := findPondByID(pondMap, pid)
		rels := stockIDToReleases[p.FishStockID]
		if debug {
			fmt.Fprintf(os.Stderr, "pond %d stock %d rels=%d\n", p.ID, p.FishStockID, len(rels))
		}
		for _, r := range rels {
			// 鱼种名称
			fishName := ""
			if fq, ok := fishQualityMap[strconv.FormatInt(r.FishID, 10)]; ok {
				if fs, ok2 := fishSpeciesMap[strconv.FormatInt(fq.Species, 10)]; ok2 {
					if strings.TrimSpace(fs.ShowName) != "" {
						fishName = fs.ShowName
					} else {
						fishName = fs.Name
					}
				}
			}
			fe, ok := envMap[strconv.FormatInt(r.FishEnvID, 10)]
			if !ok {
				if debug {
					fmt.Fprintf(os.Stderr, "missing env for fishEnvId=%d\n", r.FishEnvID)
				}
				continue
			}
			key := aggKey{pondID: p.ID, fishName: fishName}
			a := agg[key]
			if a == nil {
				a = &aggEntry{PondID: p.ID, FishName: fishName}
				agg[key] = a
			}
			a.StructGroupIDs = appendUniqueInt(a.StructGroupIDs, int(fe.StructID))
			a.LayerGroupIDs = appendUniqueInt(a.LayerGroupIDs, int(fe.LayerID))
			a.BaitTypeGroupIDs = appendUniqueInt(a.BaitTypeGroupIDs, int(fe.BaitTypeCoeffGroup))
			a.PeriodGroupIDs = appendUniqueInt(a.PeriodGroupIDs, int(fe.PeriodCoeffGroup))
		}
	}
	if debug {
		fmt.Fprintf(os.Stderr, "agg keys=%d\n", len(agg))
	}
	var rowID int64 = 1
	for _, a := range agg {
		structText := deriveStructTextMulti(a.StructGroupIDs, structAffinityRaw, dict)
		layerText := deriveLayerTextMulti(a.LayerGroupIDs, waterLayerAffinityRaw, dict)
		periodText := derivePeriodNameMulti(a.PeriodGroupIDs, envAffinityMap)
		realText, lureText := deriveBaitTextsMulti(a.BaitTypeGroupIDs, baitTypeAffinity, envAffinityMap, dict)
		if debug {
			fmt.Fprintf(os.Stderr, "row pond=%d fish=%s structIDs=%v layerIDs=%v baitIDs=%v periodIDs=%v => struct='%s' layer='%s' time='%s' real='%s' lure='%s'\n",
				a.PondID, a.FishName, a.StructGroupIDs, a.LayerGroupIDs, a.BaitTypeGroupIDs, a.PeriodGroupIDs,
				structText, layerText, periodText, realText, lureText)
		}
		rows = append(rows, DataRow{
			RowID:        rowID,
			PondID:       a.PondID,
			FishName:     a.FishName,
			PeriodName:   periodText,
			StructText:   structText,
			LayerText:    layerText,
			TrueBaitText: realText,
			LureBaitText: lureText,
		})
		rowID++
	}

	// 准备导出目录与文件名
	// 获取当前工作目录，这样无论在哪里运行都会导出到当前目录
	outDir, err := os.Getwd()
	if err != nil {
		fatalf("获取当前工作目录失败: %v", err)
	}
	outPath := filepath.Join(outDir, fmt.Sprintf("fish_env_export_%s.xlsx", channel))

	if err := exportToExcel(outPath, rows); err != nil {
		fatalf("导出Excel失败: %v", err)
	}

	fmt.Printf("导出完成: %s\n", outPath)
}

func exportToExcel(outPath string, rows []DataRow) error {
	f := excelize.NewFile()
	sheet := "Sheet1"
	if idx, err := f.GetSheetIndex(sheet); err != nil || idx < 0 {
		newIdx, err2 := f.NewSheet(sheet)
		if err2 != nil {
			return err2
		}
		f.SetActiveSheet(newIdx)
	} else {
		f.SetActiveSheet(idx)
	}

	// 头一行：类型（新增时间段列）
	typeRow := []any{"INT64", "INT64", "STRING", "STRING", "STRING", "STRING", "STRING", "STRING"}
	if err := setRow(f, sheet, 1, typeRow); err != nil {
		return err
	}
	// 第二行：英文字段
	enHeader := []any{"id", "pond_id", "fish_name", "time", "water_layer", "struct", "true_bait", "proposed_bait"}
	if err := setRow(f, sheet, 2, enHeader); err != nil {
		return err
	}
	// 第三行：中文字段
	zhHeader := []any{"ID", "钓场", "鱼种", "时间段", "水层", "结构体", "真饵", "拟饵"}
	if err := setRow(f, sheet, 3, zhHeader); err != nil {
		return err
	}

	// 数据行，从第4行开始
	r := 4
	for _, d := range rows {
		rec := []any{d.RowID, d.PondID, d.FishName, d.PeriodName, d.LayerText, d.StructText, d.TrueBaitText, d.LureBaitText}
		if err := setRow(f, sheet, r, rec); err != nil {
			return err
		}
		r++
	}

	if err := f.SaveAs(outPath); err != nil {
		return err
	}
	return nil
}

func setRow(f *excelize.File, sheet string, row int, values []any) error {
	for i, v := range values {
		cell, err := excelize.CoordinatesToCellName(i+1, row)
		if err != nil {
			return err
		}
		if err := f.SetCellValue(sheet, cell, v); err != nil {
			return err
		}
	}
	return nil
}

// ---------- 数据结构（解析） ----------

type StructAffinityGroup struct {
}

type StructListItem struct {
	StructType int     `json:"structType"`
	Coeff      float64 `json:"coeff"`
}

type StructAffinity struct {
	ID   int              `json:"id"`
	Name string           `json:"name"`
	List []StructListItem `json:"List"`
}

type WaterLayerListItem struct {
	LayerType int     `json:"layerType"`
	Coeff     float64 `json:"coeff"`
}

type WaterLayerAffinity struct {
	ID   int                  `json:"id"`
	Name string               `json:"name"`
	List []WaterLayerListItem `json:"List"`
}

// 兼容泛型读取 water_layer_affinity.json
type WaterLayerAffinityGroup struct{}

// map[string]StructAffinityGroup 实际上 value 是 StructAffinity，需要用泛型读取到 map 再在函数内断言
// 为简化，定义真实类型并在读取后进行二次解析

type BaitTypeAffinityRow struct {
	ID                 int     `json:"id"`
	BaitTypeCoeffGroup int     `json:"baitTypeCoeffGroup"`
	BaitSubType        int     `json:"baitSubType"`
	Coeff              float64 `json:"coeff"`
	PoseGroup          int     `json:"poseGroup"`
}

type EnvMapRow struct {
	ID   int    `json:"id"`
	Name string `json:"name"`
}

// ---------- 通用枚举字典（从 configs/xlsx/enum.xlsx 加载） ----------
type enumDict struct {
	structMap     map[int]string
	layerMap      map[int]string
	poseMap       map[string]string
	baitTypeName  map[int]string
	baitTypeKind  map[int]string // real/lure
	layerTypeName map[int]string // 饵悬停水层类型（按 layerType 数值）
}

func (d enumDict) structCN(code int) string {
	if d.structMap != nil {
		if v, ok := d.structMap[code]; ok && v != "" {
			return v
		}
	}
	fatalf("枚举缺失: UNDER_WATER_STRUCTURE code=%d 在 enum.xlsx 未找到", code)
	return ""
}

func (d enumDict) layerCN(code int) string {
	if d.layerMap != nil {
		if v, ok := d.layerMap[code]; ok && v != "" {
			return v
		}
	}
	fatalf("枚举缺失: WATER_LAYER code=%d 在 enum.xlsx 未找到", code)
	return ""
}

func (d enumDict) periodEnum(internal string) string {
	return "enum_" + internal
}

func (d enumDict) poseToLabel(poseName string) (string, string) {
	if d.poseMap != nil {
		if v, ok := d.poseMap[poseName]; ok && v != "" {
			lower := strings.ToLower(poseName)
			if strings.Contains(lower, "posebp_lure_") {
				return v, "lure"
			}
			if strings.Contains(lower, "posebp_bait_") {
				return v, "real"
			}
			fatalf("枚举姿态未识别真/拟饵类型: %s", poseName)
		}
	}
	fatalf("枚举缺失: 姿态/饵映射 %s 在 enum.xlsx 未找到", poseName)
	return "", ""
}

func (d enumDict) baitName(id int) string {
	if v, ok := d.baitTypeName[id]; ok && v != "" {
		return v
	}
	fatalf("枚举缺失: 饵类型 id=%d 在 enum.xlsx 未找到", id)
	return ""
}

func (d enumDict) baitKind(id int) string {
	if v, ok := d.baitTypeKind[id]; ok && v != "" {
		return v
	}
	fatalf("枚举缺失: 饵类型(真/拟) id=%d 在 enum.xlsx 未找到", id)
	return ""
}

func mustLoadEnumDict(enumXlsxPath string) enumDict {
	d := enumDict{structMap: map[int]string{}, layerMap: map[int]string{}, poseMap: map[string]string{}, baitTypeName: map[int]string{}, baitTypeKind: map[int]string{}, layerTypeName: map[int]string{}}
	f, err := excelize.OpenFile(enumXlsxPath)
	if err != nil {
		return d
	}
	defer f.Close()
	sheets := f.GetSheetList()
	for _, sh := range sheets {
		rows, err := f.GetRows(sh)
		if err != nil || len(rows) == 0 {
			continue
		}
		// 基于 sheet 名识别水层类型枚举表
		sheetLower := strings.ToLower(strings.TrimSpace(sh))
		isWaterLayerTypeSheet := strings.Contains(sheetLower, "悬停") || strings.Contains(sheetLower, "hover") ||
			strings.Contains(sheetLower, "水层类型") || strings.Contains(sheetLower, "layer_type") || strings.Contains(sheetLower, "water_layer_type")
		// 约定：
		// 第1行：类型（如 STRING/INT/STRING）
		// 第2行：字段名（name/id/comment）
		// 第3行：中文表头（可忽略）
		// 第4行起：数据
		var header []string
		if len(rows) >= 2 {
			header = rows[1]
		} else {
			header = rows[0]
		}
		idxID, idxName, idxCN := -1, -1, -1
		for i, h := range header {
			hl := strings.ToLower(strings.TrimSpace(h))
			switch hl {
			case "id", "value", "val", "code":
				if idxID == -1 {
					idxID = i
				}
			case "name", "enum", "key":
				if idxName == -1 {
					idxName = i
				}
			case "comment", "chinesesimplified", "chinese", "cn", "desc", "description":
				if idxCN == -1 {
					idxCN = i
				}
			}
		}
		// 数据从第4行（索引3）开始；如不足3行，兼容从索引2开始
		startRow := 3
		if len(rows) <= 3 {
			startRow = 2
		}
		for r := startRow; r < len(rows); r++ {
			cols := rows[r]
			if len(cols) == 0 {
				continue
			}
			get := func(idx int) string {
				if idx >= 0 && idx < len(cols) {
					return strings.TrimSpace(cols[idx])
				}
				return ""
			}
			idStr := get(idxID)
			name := get(idxName)
			cn := get(idxCN)
			if idStr == "" && name == "" && cn == "" {
				continue
			}

			// 解析 name 列的组与值，比如 "[水层类型]上水层"
			group := ""
			label := ""
			n := strings.TrimSpace(name)
			if strings.HasPrefix(n, "[") {
				if right := strings.Index(n, "]"); right > 1 {
					group = n[1:right]
					label = strings.TrimSpace(n[right+1:])
				}
			}
			if label == "" {
				label = n
			}
			// 不使用备注覆盖命名；仅当命名缺失时回退备注
			if label == "" && cn != "" {
				label = cn
			}

			id, err := strconv.Atoi(idStr)
			if err != nil {
				continue
			}

			groupLower := strings.ToLower(group)
			nameLower := strings.ToLower(n)

			// 若当前 sheet 判定为水层类型表，则以 id->label 写入 layerTypeName
			if isWaterLayerTypeSheet && label != "" {
				d.layerTypeName[id] = label
			}

			// 结构体
			if groupLower == "under_water_structure" || strings.Contains(groupLower, "结构") {
				if label != "" {
					d.structMap[id] = label
				}
				continue
			}
			// 水层
			if groupLower == "water_layer" || strings.Contains(groupLower, "水层") || strings.HasPrefix(nameLower, "water_layer_") {
				if label != "" {
					d.layerMap[id] = label
					// 若 name 为 water_layer_1/2/3，则同步作为 layerTypeName
					if strings.HasPrefix(nameLower, "water_layer_") {
						idxStr := strings.TrimPrefix(nameLower, "water_layer_")
						if nIdx, err := strconv.Atoi(idxStr); err == nil {
							d.layerTypeName[nIdx] = label
						}
					}
				}
				continue
			}
			// 饵悬停水层类型（按层类型）
			if strings.Contains(groupLower, "悬停") || strings.Contains(groupLower, "hover") || strings.Contains(groupLower, "水层类型") || strings.Contains(groupLower, "layer_type") {
				if label != "" {
					d.layerTypeName[id] = label
				}
				continue
			}
			// 饵类型（真/拟）
			if strings.Contains(groupLower, "饵") || strings.Contains(groupLower, "bait") {
				if label != "" {
					d.baitTypeName[id] = label
					// kind 判定
					if strings.Contains(groupLower, "真") || strings.Contains(nameLower, "real") {
						d.baitTypeKind[id] = "real"
					} else if strings.Contains(groupLower, "拟") || strings.Contains(nameLower, "lure") {
						d.baitTypeKind[id] = "lure"
					}
				}
				continue
			}
			// 兼容旧姿态映射（可有可无）
			if strings.HasPrefix(nameLower, "posebp_") {
				if label != "" {
					d.poseMap[n] = label
				}
				continue
			}
		}
	}
	return d
}

// ---------- 文本派生 ----------

func deriveStructText(structID int, structMap map[string]StructAffinityGroup, dict enumDict) string {
	// 读取并转换目标组
	key := strconv.Itoa(structID)
	v, ok := structMap[key]
	if !ok {
		return ""
	}
	// 将 v 编码再解码为 StructAffinity（因为泛型 map[string]T 使用了不同结构）
	b, _ := json.Marshal(v)
	var sa StructAffinity
	if err := json.Unmarshal(b, &sa); err != nil {
		return ""
	}
	coeffs := make([]float64, 0, len(sa.List))
	pairs := make([][2]float64, 0, len(sa.List)) // [coeff, structType]
	for _, it := range sa.List {
		if it.Coeff != 0 {
			coeffs = append(coeffs, it.Coeff)
		}
		if it.Coeff >= 0.6 {
			pairs = append(pairs, [2]float64{it.Coeff, float64(it.StructType)})
		}
	}
	if len(coeffs) >= 2 && allEqual(coeffs) { // 至少2个非零
		return "任意地形"
	}
	if len(pairs) == 0 {
		return ""
	}
	sort.Slice(pairs, func(i, j int) bool {
		if pairs[i][0] == pairs[j][0] {
			return pairs[i][1] < pairs[j][1]
		}
		return pairs[i][0] > pairs[j][0]
	})
	if len(pairs) > 3 {
		pairs = pairs[:3]
	}
	labels := make([]string, 0, len(pairs))
	for _, p := range pairs {
		labels = append(labels, "[水下结构体]"+dict.structCN(int(p[1])))
	}
	return strings.Join(labels, "|")
}

func deriveLayerText(layerGroupID int, layerMap map[string]WaterLayerAffinityGroup, dict enumDict) string {
	key := strconv.Itoa(layerGroupID)
	v, ok := layerMap[key]
	if !ok {
		return ""
	}
	b, _ := json.Marshal(v)
	var wl WaterLayerAffinity
	if err := json.Unmarshal(b, &wl); err != nil {
		return ""
	}
	coeffs := make([]float64, 0, len(wl.List))
	pairs := make([][2]float64, 0, len(wl.List)) // [coeff, layerType]
	for _, it := range wl.List {
		if it.Coeff != 0 {
			coeffs = append(coeffs, it.Coeff)
		}
		if it.Coeff >= 0.5 {
			pairs = append(pairs, [2]float64{it.Coeff, float64(it.LayerType)})
		}
	}
	if len(coeffs) >= 2 && allEqual(coeffs) { // 至少2个非零
		return "任意水层"
	}
	if len(pairs) == 0 {
		return "None"
	}
	sort.Slice(pairs, func(i, j int) bool {
		if pairs[i][0] == pairs[j][0] {
			return pairs[i][1] < pairs[j][1]
		}
		return pairs[i][0] > pairs[j][0]
	})
	labels := make([]string, 0, len(pairs))
	for _, p := range pairs {
		name, ok := dict.layerTypeName[int(p[1])]
		if !ok || name == "" {
			fatalf("枚举缺失: 悬停水层 layerType=%d 在 enum.xlsx 未找到", int(p[1]))
		}
		labels = append(labels, "[饵悬停水层类型]"+name)
	}
	return strings.Join(labels, "|")
}

func derivePeriodName(periodGroupID int, envMap map[string]EnvMapRow) string {
	key := strconv.Itoa(periodGroupID)
	if row, ok := envMap[key]; ok {
		name := strings.TrimSpace(row.Name)
		if name != "" {
			return name
		}
	}
	return ""
}

func deriveBaitTexts(baitTypeCoeffGroup int, baitTypeMap map[string]BaitTypeAffinityRow, envMap map[string]EnvMapRow, dict enumDict) (string, string) {
	type Pair struct {
		name  string
		coeff float64
	}
	real := make([]Pair, 0, 8)
	lure := make([]Pair, 0, 8)
	for _, row := range baitTypeMap {
		if row.BaitTypeCoeffGroup != baitTypeCoeffGroup {
			continue
		}
		if row.Coeff < 0.6 {
			continue
		}
		baitName := dict.baitName(row.BaitSubType)
		kind := dict.baitKind(row.BaitSubType)
		if kind == "real" {
			real = append(real, Pair{baitName, row.Coeff})
		} else if kind == "lure" {
			lure = append(lure, Pair{baitName, row.Coeff})
		} else {
			fatalf("饵类型未标注真/拟: id=%d", row.BaitSubType)
		}
	}
	// 排序：系数降序；相同系数做随机打散
	sort.SliceStable(real, func(i, j int) bool { return real[i].coeff > real[j].coeff })
	sort.SliceStable(lure, func(i, j int) bool { return lure[i].coeff > lure[j].coeff })
	// 对相同系数的段落进行随机洗牌
	shuffleSameCoeff := func(a []Pair) {
		i := 0
		for i < len(a) {
			j := i + 1
			for j < len(a) && a[j].coeff == a[i].coeff {
				j++
			}
			if j-i > 1 {
				rand.Shuffle(j-i, func(x, y int) { a[i+x], a[i+y] = a[i+y], a[i+x] })
			}
			i = j
		}
	}
	shuffleSameCoeff(real)
	shuffleSameCoeff(lure)
	if len(real) > 5 {
		real = real[:5]
	}
	if len(lure) > 5 {
		lure = lure[:5]
	}
	realNames := make([]string, 0, len(real))
	for _, p := range real {
		realNames = append(realNames, "[真饵]"+p.name)
	}
	lureNames := make([]string, 0, len(lure))
	for _, p := range lure {
		lureNames = append(lureNames, "[拟饵]"+p.name)
	}
	var realText, lureText string
	if len(realNames) == 0 {
		realText = ""
	} else {
		realText = strings.Join(realNames, "|")
	}
	if len(lureNames) == 0 {
		lureText = ""
	} else {
		lureText = strings.Join(lureNames, "|")
	}
	return realText, lureText
}

func allEqual(vals []float64) bool {
	if len(vals) == 0 {
		return false
	}
	first := vals[0]
	for _, v := range vals[1:] {
		if v != first {
			return false
		}
	}
	return true
}

// 严格模式：不使用代码兜底映射

func findPondByID(m map[string]Pond, id int64) Pond {
	k := strconv.FormatInt(id, 10)
	if p, ok := m[k]; ok {
		return p
	}
	return Pond{}
}

func mustReadJSONMap[T any](path string) map[string]T {
	f, err := os.Open(path)
	if err != nil {
		fatalf("打开文件失败 %s: %v", path, err)
	}
	defer f.Close()
	b, err := io.ReadAll(f)
	if err != nil {
		fatalf("读取文件失败 %s: %v", path, err)
	}
	var m map[string]T
	if err := json.Unmarshal(b, &m); err != nil {
		fatalf("解析JSON失败 %s: %v", path, err)
	}
	return m
}

func mustReadJSONRaw(path string) map[string]json.RawMessage {
	f, err := os.Open(path)
	if err != nil {
		fatalf("打开文件失败 %s: %v", path, err)
	}
	defer f.Close()
	b, err := io.ReadAll(f)
	if err != nil {
		fatalf("读取文件失败 %s: %v", path, err)
	}
	var m map[string]json.RawMessage
	if err := json.Unmarshal(b, &m); err != nil {
		fatalf("解析JSON失败 %s: %v", path, err)
	}
	return m
}

func mustEnsureDirExists(dir string) {
	st, err := os.Stat(dir)
	if err != nil {
		if os.IsNotExist(err) {
			fatalf("目录不存在: %s", dir)
		}
		fatalf("读取目录失败: %v", err)
	}
	if !st.IsDir() {
		fatalf("不是目录: %s", dir)
	}
}

func mustGetRepoRoot() string {
	wd, err := os.Getwd()
	if err != nil {
		fatalf("获取工作目录失败: %v", err)
	}
	// 向上查找，直到包含 configs 目录
	dir := wd
	for i := 0; i < 6; i++ { // 最多向上6层
		if exists(filepath.Join(dir, "configs")) {
			return dir
		}
		parent := filepath.Dir(dir)
		if parent == dir {
			break
		}
		dir = parent
	}
	// 兜底：假设当前即仓库根
	if exists(filepath.Join(wd, "configs")) {
		return wd
	}
	fatalf("未找到仓库根目录(缺少 configs/)")
	return ""
}

func exists(path string) bool {
	_, err := os.Stat(path)
	return err == nil
}

func prompt(msg string) (string, error) {
	fmt.Print(msg)
	rd := bufio.NewReader(os.Stdin)
	line, err := rd.ReadString('\n')
	if err != nil {
		if errors.Is(err, io.EOF) {
			return strings.TrimSpace(line), nil
		}
		return "", err
	}
	return strings.TrimSpace(line), nil
}

func fatalf(format string, a ...any) {
	fmt.Fprintf(os.Stderr, format+"\n", a...)
	os.Exit(1)
}

type aggEntry struct {
	PondID           int64
	FishName         string
	StructGroupIDs   []int
	LayerGroupIDs    []int
	BaitTypeGroupIDs []int
	PeriodGroupIDs   []int
}

func appendUniqueInt(xs []int, v int) []int {
	for _, x := range xs {
		if x == v {
			return xs
		}
	}
	return append(xs, v)
}

func deriveStructTextMulti(groupIDs []int, structMap map[string]json.RawMessage, dict enumDict) string {
	// 合并所有组的条目后，应用单组的规则
	type Pair struct {
		coeff float64
		t     int
	}
	coeffs := make([]float64, 0, 16)
	pairs := make([]Pair, 0, 32)
	for _, gid := range groupIDs {
		key := strconv.Itoa(gid)
		raw, ok := structMap[key]
		if !ok {
			continue
		}
		var sa StructAffinity
		if json.Unmarshal(raw, &sa) != nil {
			continue
		}
		for _, it := range sa.List {
			if it.Coeff != 0 {
				coeffs = append(coeffs, it.Coeff)
			}
			if it.Coeff >= 0.6 {
				pairs = append(pairs, Pair{it.Coeff, it.StructType})
			}
		}
	}
	if len(coeffs) >= 2 && allEqual(coeffs) {
		return "任意地形"
	}
	if len(pairs) == 0 {
		return ""
	}
	sort.Slice(pairs, func(i, j int) bool {
		if pairs[i].coeff == pairs[j].coeff {
			return pairs[i].t < pairs[j].t
		}
		return pairs[i].coeff > pairs[j].coeff
	})
	// 去重按类型
	seen := map[int]bool{}
	labels := make([]string, 0, 3)
	for _, p := range pairs {
		if seen[p.t] {
			continue
		}
		seen[p.t] = true
		labels = append(labels, "[水下结构体]"+dict.structCN(p.t))
		if len(labels) == 3 {
			break
		}
	}
	return strings.Join(labels, "|")
}

func deriveLayerTextMulti(groupIDs []int, layerMap map[string]json.RawMessage, dict enumDict) string {
	type Pair struct {
		coeff float64
		t     int
	}
	coeffs := make([]float64, 0, 16)
	pairs := make([]Pair, 0, 32)
	for _, gid := range groupIDs {
		key := strconv.Itoa(gid)
		raw, ok := layerMap[key]
		if !ok {
			continue
		}
		var wl WaterLayerAffinity
		if json.Unmarshal(raw, &wl) != nil {
			continue
		}
		for _, it := range wl.List {
			if it.Coeff != 0 {
				coeffs = append(coeffs, it.Coeff)
			}
			if it.Coeff >= 0.5 {
				pairs = append(pairs, Pair{it.Coeff, it.LayerType})
			}
		}
	}
	if len(coeffs) >= 2 && allEqual(coeffs) {
		return "任意水层"
	}
	if len(pairs) == 0 {
		return ""
	}
	sort.Slice(pairs, func(i, j int) bool {
		if pairs[i].coeff == pairs[j].coeff {
			return pairs[i].t < pairs[j].t
		}
		return pairs[i].coeff > pairs[j].coeff
	})
	// 去重按层类型
	seen := map[int]bool{}
	labels := make([]string, 0, 8)
	for _, p := range pairs {
		if seen[p.t] {
			continue
		}
		seen[p.t] = true
		name, ok := dict.layerTypeName[p.t]
		if !ok || name == "" {
			// 回退：若 enum 没给出 layerTypeName，尝试在通用 layerMap 中查找
			if v2, ok2 := dict.layerMap[p.t]; ok2 && v2 != "" {
				name = v2
			}
		}
		if name != "" {
			labels = append(labels, "[饵悬停水层类型]"+name)
		}
	}
	if len(labels) == 0 {
		return ""
	}
	return strings.Join(labels, "|")
}

func derivePeriodNameMulti(groupIDs []int, envMap map[string]EnvMapRow) string {
	names := make([]string, 0, len(groupIDs))
	seen := map[string]bool{}
	for _, gid := range groupIDs {
		k := strconv.Itoa(gid)
		if row, ok := envMap[k]; ok {
			n := strings.TrimSpace(row.Name)
			if n != "" && !seen[n] {
				names = append(names, n)
				seen[n] = true
			}
		}
	}
	return strings.Join(names, "|")
}

func deriveBaitTextsMulti(groupIDs []int, baitTypeMap map[string]BaitTypeAffinityRow, envMap map[string]EnvMapRow, dict enumDict) (string, string) {
	type Pair struct {
		name  string
		coeff float64
	}
	real := make([]Pair, 0, 16)
	lure := make([]Pair, 0, 16)
	for _, row := range baitTypeMap {
		// 任何一个 group 命中即可
		hit := false
		for _, gid := range groupIDs {
			if row.BaitTypeCoeffGroup == gid {
				hit = true
				break
			}
		}
		if !hit {
			continue
		}
		if row.Coeff < 0.6 {
			continue
		}
		baitName := dict.baitName(row.BaitSubType)
		kind := dict.baitKind(row.BaitSubType)
		if kind == "real" {
			real = append(real, Pair{baitName, row.Coeff})
		} else if kind == "lure" {
			lure = append(lure, Pair{baitName, row.Coeff})
		}
	}
	uniq := func(in []Pair) []Pair {
		seen := map[string]bool{}
		out := make([]Pair, 0, len(in))
		for _, p := range in {
			if !seen[p.name] {
				seen[p.name] = true
				out = append(out, p)
			}
		}
		return out
	}
	real = uniq(real)
	lure = uniq(lure)
	sort.SliceStable(real, func(i, j int) bool { return real[i].coeff > real[j].coeff })
	sort.SliceStable(lure, func(i, j int) bool { return lure[i].coeff > lure[j].coeff })
	if len(real) > 5 {
		real = real[:5]
	}
	if len(lure) > 5 {
		lure = lure[:5]
	}
	rnames := make([]string, 0, len(real))
	for _, p := range real {
		rnames = append(rnames, "[真饵]"+p.name)
	}
	lnames := make([]string, 0, len(lure))
	for _, p := range lure {
		lnames = append(lnames, "[拟饵]"+p.name)
	}
	return strings.Join(rnames, "|"), strings.Join(lnames, "|")
}
