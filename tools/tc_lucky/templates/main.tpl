package main

import (
	"context"
	"runtime"

	"github.com/spf13/viper"
	"github.com/sirupsen/logrus"
	"git.keepfancy.xyz/back-end/frameworks/dict"
	"git.keepfancy.xyz/back-end/frameworks/kit/driver"
	"git.keepfancy.xyz/back-end/frameworks/lib/random"
)

type {{.SrvName}}Service struct {
	Name string
	Ctx  context.Context
}

func (s *{{.SrvName}}Service) Init() error {
    s.Ctx = context.Background()
    s.Name = viper.GetString(dict.ConfigRpcServerName)
   	logrus.Infoln(s.Name + "服务Init")

	// TODO 实现各模块初始化操作

	return nil
}

func (s *{{.SrvName}}Service) Start() error {
    // TODO 这里实现启动逻辑
	logrus.Infoln(s.Name + "服务启动成功")
	return nil
}

func (s *{{.SrvName}}Service) Stop() error {
    // TODO 这里实现服务正常关闭逻辑
	logrus.Infoln(s.Name + "服务关闭中...")
	return nil
}

func (s *{{.SrvName}}Service) ForceStop() error {
    // TODO 这里实现强制关闭逻辑
	logrus.Infoln(s.Name + " ForceStop ...")
	return nil
}

func main() {
	runtime.GOMAXPROCS(runtime.NumCPU() * 2)
	random.NewInitSource() // 初始化随机数
	driver.Run(&{{.SrvName}}Service{})
}
