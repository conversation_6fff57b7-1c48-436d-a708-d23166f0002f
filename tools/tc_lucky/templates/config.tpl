# server
rpc_server_name: {{.SrvName}}
rpc_port: 0

# 端口号
http_port: 0

# 日志相关
log_level: trace
log_write: true
log_dir: ../../logs/{{.SrvName}}
log_json: false
log_kafka_enable: false

redis_addr: 192.168.1.58:6379
redis_passwd: 8888

redis_list:
  gateway:
    addr: 192.168.1.58:6379
    passwd: 8888

  player:
    addr: 192.168.1.58:6379
    passwd: 8888

consul_addr: 192.168.1.58:8500

nsqd_addr: 192.168.1.58:4150
nsqd_http_addr: 192.168.1.58:4151
nsqlookupd_addrs:
  - 192.168.1.58:4161

rpc_server_tags: normal

kafka-producer:
  brokers: ["192.168.1.58:9092"]
  timeout: 10
